import secureToken from '~/utils/secure-token'

export default class Maintain {
  constructor(axios, ctx) {
    this.axios = axios
    this.ctx = ctx
  }

  async getMaintainList(headerData) {
    // 使用統一的安全令牌工具生成頭部
    const head = secureToken.generateSecureHeaders(headerData)

    let body = {}
    if (
      this.ctx.route.params.qcStation &&
      Object.keys(this.ctx.route.params.qcStation).length > 0
    ) {
      body.stationName = this.ctx.route.params.qcStation
    }

    let maintainres = await this.axios.get('/api/maintain/list', { headers: head, params: body })
    return maintainres
  }
}
