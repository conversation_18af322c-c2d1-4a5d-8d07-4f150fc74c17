import Vue from 'vue'
import VeeValidate, { Validator } from 'vee-validate'
import { uniqWith } from 'lodash'
import snsTemplate from '~/assets/js/sns_template.js'

export default ({ app }) => {
  Vue.use(VeeValidate, {
    i18n: app.i18n,
    i18nRootKey: 'validations',
    dictionary: {
      'zh-tw': require('vee-validate/dist/locale/zh_TW'),
      'zh-cn': require('vee-validate/dist/locale/zh_CN'),
      'vi-vn': require('vee-validate/dist/locale/vi'),
      en: require('vee-validate/dist/locale/en')
    }
  })

  Validator.extend(
    'phone_number',
    {
      validate: (value, [prefix]) => {
        return app.$phoneNumberValitator.validate(value, prefix)
      }
    },
    { hasTarget: true }
  )

  Validator.extend('nickname', {
    validate: (val) => {
      const reg = new RegExp(/^[^\\(\\)]+$/)
      return reg.test(val)
    }
  })

  Validator.extend('password', {
    validate: (val) => {
      const reg = new RegExp(/^[^\\(\\)]+$/)
      return reg.test(val)
    }
  })

  Validator.extend('email', {
    validate: (val) => {
      const reg = new RegExp(/^([A-Za-z0-9_\-\\.])+\\@([A-Za-z0-9_\-\\.])+\.([A-Za-z]{2,4})$/)
      return reg.test(val)
    }
  })

  Validator.extend(
    'date_before',
    {
      validate: (value, [targetValue]) => app.$moment(value).isSameOrBefore(targetValue)
    },
    { hasTarget: true }
  )

  Validator.extend(
    'date_after',
    {
      validate: (value, [targetValue]) => app.$moment(value).isSameOrAfter(targetValue)
    },
    { hasTarget: true }
  )

  Validator.extend(
    'date_not_same',
    {
      validate: (value, [targetValue]) => value !== targetValue
    },
    { hasTarget: true }
  )

  Validator.extend('no_space', {
    validate: (val) => {
      const reg = new RegExp(/ +/)
      return reg.test(val)
    }
  })

  Validator.extend('html', {
    validate: (val) => {
      return !new RegExp(/<\/?((?!(b|br|p|pre|u|strong)\b)\w*)\/?>/gi).test(val)
    }
  })

  Validator.extend('html_limit', {
    validate: (val, [limit]) => val.length < Number(limit)
  })

  Validator.extend('no_zero_start', {
    validate: (val) => !new RegExp(/^0/).test(val)
  })

  Validator.extend('special_character', {
    validate: (val) => {
      return !new RegExp(/[@#$%^&*()_+\-=[\]{};':"\\|,.<>/?!~`]/g).test(val)
    }
  })

  Validator.extend('name_validate', {
    validate: (val) => {
      return !/[^\u4e00-\u9fa5a-zA-Z0-9]/.test(val)
    }
  })

  Validator.extend('sns', {
    validate: (val, [arg]) => {
      let result = true
      const rule = snsTemplate.find((sns) => sns.key === arg)
      if (rule && rule.regexp) {
        result = new RegExp(rule.regexp).test(val)
      }

      return result
    }
  })

  Validator.extend('multiple_line', {
    validate: (value, expression) => {
      let result = true
      const arrayValue = value.split(/\r\n|\r|\n/)
      if (expression[0] === 'phoneNumber') {
        arrayValue.forEach((val) => {
          if (val.trim().length > 0 && !app.$phoneNumberValitator.structValidate(val)) {
            result = false
          }
        })
        return result
      }
    }
  })

  Validator.extend('max_line', {
    validate: (value, [num]) => {
      const arrayValue = value.split(/\r\n|\r|\n/)
      return arrayValue.length <= num
    }
  })

  Validator.extend('repeat_line', {
    validate: (value) => {
      const origin = value.split(/\r\n|\r|\n/)
      const uniq = uniqWith(origin, (a, b) => a.trim() === b.trim())
      return origin.length === uniq.length
    }
  })

  Validator.extend('coin_max', {
    validate: (value, num) => {
      return parseInt(value) < parseInt(num[0])
    }
  })

  Validator.extend('coin_min', {
    validate: (value, num) => {
      return parseInt(value) >= parseInt(num[0])
    }
  })

  Validator.extend('transfer_min', {
    validate: (value, num) => {
      return parseInt(value) >= parseInt(num[0])
    }
  })

  Validator.extend('coin_min_send_mail', {
    validate: (value, num) => {
      return parseInt(value) >= parseInt(num[0])
    }
  })

  Validator.extend('insufficient_quota', {
    validate: (value, num) => {
      return parseInt(value) <= parseInt(num[0])
    }
  })

  Validator.extend('insufficient_points', {
    validate: (value, num) => {
      return parseInt(value) <= parseInt(num[0])
    }
  })

  Validator.extend('card_number_limit', {
    validate: (value) => {
      const regex = /^[0-9a-zA-Z]+$/
      const strCount = value.trim().length
      return strCount >= 8 && strCount <= 16 && regex.test(value)
    }
  })

  Validator.extend('number21_limit', {
    validate: (value) => {
      const regex = /^[0-9a-zA-Z]+$/
      const strCount = value.trim().length
      return strCount === 21 && regex.test(value)
    }
  })

  Validator.extend('number4_limit', {
    validate: (value) => {
      const regex = /^[0-9a-zA-Z]+$/
      const strCount = value.trim().length
      return strCount === 4 && regex.test(value)
    }
  })

  Validator.extend('allow_blank_required', {
    validate: (value, ref) => {
      if (ref === void 0) ref = []
      var invalidateFalse = ref[0]
      if (invalidateFalse === void 0) invalidateFalse = false

      // incase a field considers `false` as an empty value like checkboxes.
      if (value === false && invalidateFalse) {
        return false
      }
      return !!String(value).length
    }
  })

  Validator.extend('validate_length', {
    validate: (value, num) => {
      return value.length == num[0]
    }
  })

  Validator.extend('validate_char_length', {
    validate: (value, num) => {
      const byteLength = [...value].reduce((acc, char) => {
        const code = char.charCodeAt(0)
        return acc + (code >= 0x00 && code <= 0x7f ? 1 : 2)
      }, 0)
      return byteLength <= num[0]
    }
  })
  //是否為數字
  Validator.extend('is_number', {
    validate: (value) => {
      const reg = new RegExp(/^[1-9]\d*$/)
      return reg.test(value)
    }
  })

  //是否為姓氏
  Validator.extend('is_last_name', {
    validate: (value) => {
      const reg = new RegExp(/^[a-zA-Z\u4e00-\u9fa5]{1,20}$/)
      return reg.test(value)
    }
  })

  //是否為數字
  Validator.extend('is_number_card', {
    validate: (value) => {
      const reg = new RegExp(/^[1-9]\d*$/)
      return reg.test(value)
    }
  })

  Validator.extend('insufficient_points_vip', {
    validate: (value, num) => {
      return parseInt(value) <= parseInt(num[0])
    }
  })
  // 是否大於星幣餘額
  Validator.extend('insufficient_store_points', {
    validate: (value, num) => {
      return parseInt(value) <= parseInt(num[0])
    }
  })
  // 是否大於已存星幣
  Validator.extend('insufficient_withdraw_points', {
    validate: (value, num) => {
      return parseInt(value) <= parseInt(num[0])
    }
  })

  //是否大於幾歲以上
  Validator.extend('age_over_limit', {
    validate: (value, [ageLimit]) => {
      if (!value || isNaN(new Date(value).getTime())) {
        return false // 確保有輸入日期
      }

      const birthDate = new Date(value) // 用戶輸入的生日
      const today = new Date() // 當前日期
      // 計算年齡
      let age = today.getFullYear() - birthDate.getFullYear()
      if (age === 18) {
        // 如果剛好18歲，判斷月與日期
        // 如果當前，年還沒有到用戶生日的月份，或者當前，年和月相同但日還沒到，年齡需要減一
        const isBeforeBirthday =
          today.getMonth() < birthDate.getMonth() ||
          (today.getMonth() === birthDate.getMonth() && today.getDate() < birthDate.getDate())
        if (isBeforeBirthday) {
          age-- // 未到生日，年齡減一
        }
      }
      // 回傳年齡，並指定為10位數判斷
      return age >= parseInt(ageLimit, 10)
    }
  })

  Validator.extend('redeem_code_limit', {
    validate: (value) => {
      const regex = /^[0-9a-zA-Z]+$/
      const strCount = value.trim().length
      return strCount >= 1 && strCount <= 32 && regex.test(value)
    }
  })

  //公會名稱上限
  Validator.extend('guild_name_limit', {
    validate: (value) => {
      const chineseLength = (value.match(/[\u4e00-\u9fff\u3400-\u4dbf]/g) || []).length
      const totalLength = chineseLength * 2 + (value.length - chineseLength)
      return totalLength <= 17 && value.length >= 2
    }
  })

  //公會名稱重複
  Validator.extend('guild_duplicate_name', {
    validate: (value, list) => {
      const guildList = list
      const hasMatch = guildList.filter((name) => name === value)
      return hasMatch.length === 0
    }
  })

  Validator.extend('send_mail_limit_coin', {
    validate: (value, num) => {
      return parseInt(value) <= parseInt(num[0].replace(/,/g, ''))
    }
  })

  Validator.extend('text_fullwidth', {
    validate: (value) => {
      if (!value) return true
      // 更完整的全形檢查，包含：
      // 全形標點符號、空格、數字、英文字母、特殊符號
      const fullWidthPattern = /[\uFF01-\uFF5E\u3000-\u303F\uFF00-\uFFEF]/
      return !fullWidthPattern.test(value)
    }
  })
  Validator.extend('validate_string_length', {
    validate: (value, num) => {
      return value.length <= num[0]
    }
  })
  Validator.extend('inappropriate_words', {
    validate(value, keywords) {
      if (!keywords || !value) return true
      return !keywords.some((keyword) => value.includes(keyword))
    }
  })
  Validator.extend('positive_number', {
    validate: (value) => {
      return Number(value) >= 0
    }
  })

  const STATION = process.env.STATION
  const stationEnValidMessage = () => {
    try {
      return require(`../locales/validations/${STATION}/en`).default.messages
    } catch {
      return {}
    }
  }
  const enValidMessage = {
    messages: {
      ...require('../locales/validations/en').default.messages,
      ...stationEnValidMessage()
    }
  }
  Validator.localize({
    'zh-tw': require('../locales/validations/zh-tw').default,
    'zh-cn': require('../locales/validations/zh-cn').default,
    'vi-vn': require('../locales/validations/vi-vn').default,
    en: enValidMessage
  })
}
