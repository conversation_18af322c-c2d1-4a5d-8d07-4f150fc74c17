import countTo from 'vue-count-to'
import Vue from 'vue'

Vue.component('countAnimate', countTo)

const indianCountTo = Vue.extend({
  extends: countTo,
  methods: {
    formatNumber(num) {
      if (this.decimals === 0) {
        return this.formatIndianNumber(num)
      }

      const formatted = num.toFixed(this.decimals)
      const parts = formatted.split('.')
      const integerPart = this.formatIndianNumber(parts[0])
      return integerPart + this.decimal + parts[1]
    },

    formatIndianNumber(num) {
      if (num === null || num === undefined || isNaN(num)) return '0'

      const numStr = String(Math.abs(parseInt(num)))

      if (numStr.length <= 3) {
        return (num < 0 ? '-' : '') + numStr
      }

      let result = numStr.slice(-3)
      let remaining = numStr.slice(0, -3)

      while (remaining.length > 0) {
        const segment = remaining.slice(-2)
        result = segment + ',' + result
        remaining = remaining.slice(0, -2)
      }

      if (result.startsWith(',')) {
        result = result.slice(1)
      }

      return (num < 0 ? '-' : '') + result
    }
  }
})

// 註冊印度數字格式專用組件
Vue.component('indianCountAnimate', indianCountTo)
