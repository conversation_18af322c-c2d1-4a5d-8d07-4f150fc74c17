export default (context, inject) => {
  /**
   * 數字格式化工具類
   * 用於將數字按不同地區規範進行格式化顯示
   */
  class NumberFormatter {
    constructor() {
      // 建立不同地區的數字格式化器
      this.localeFormatters = {
        international: new Intl.NumberFormat('en-US'), // 國際格式 (美式)
        india: new Intl.NumberFormat('en-IN') // 印度格式
      }
      // 站點對應格式化器的映射
      this.stationMap = {
        india_01: 'india'
      }
    }

    /**
     * 根據站點選擇對應的格式化器
     * @returns {Intl.NumberFormat} 對應站點的格式化器
     */
    getFormatterByStation() {
      const STATION = process.env.STATION
      const localeKey = this.stationMap[STATION] || 'international'
      return this.localeFormatters[localeKey]
    }

    /**
     * 國際格式化 (美式千分位)
     * @param {number} value - 要格式化的數字
     * @returns {string|number} 格式化後的字串或 0
     */
    int(value) {
      if (typeof value !== 'number' || isNaN(value)) return
      return this.localeFormatters.international.format(value)
    }

    /**
     * 印度格式化 (印度千分位規則)
     * @param {number} value - 要格式化的數字
     * @returns {string|number} 格式化後的字串或 0
     */
    india(value) {
      if (typeof value !== 'number' || isNaN(value)) return
      return this.localeFormatters.india.format(value)
    }

    /**
     * 根據站點自動選擇格式化方式
     * @param {number} value - 要格式化的數字
     * @returns {string|number} 根據站點格式化後的結果
     */
    station(value) {
      if (typeof value !== 'number' || isNaN(value)) return
      return this.getFormatterByStation().format(value)
    }

    /**
     * 根據站點自動選擇格式化，並格式化成公會基金格式
     * 公會基金顯示為 K 或 M
     * @param {number} value - 要格式化的數字
     * @returns {string|number} 根據站點格式化後的結果
     */
    guildFund(value) {
      if (typeof value !== 'number' || isNaN(value)) return '0'
      const abs = Math.abs(value)

      // 使用抽離的方法獲取格式化器
      const formatter = this.getFormatterByStation()

      // 千萬以上顯示為 M
      if (abs >= 1e7) {
        return `${formatter.format(Math.floor(value / 1e6))}M`
      }

      // 萬以上顯示為 K
      if (abs >= 1e4) {
        return `${formatter.format(Math.floor(value / 1e3))}K`
      }

      // 小於萬的數字正常顯示
      return formatter.format(Math.floor(value))
    }

    /**
     * 根據站點自動選擇格式化，並格式化成信箱附件金幣格式
     * 信箱附件金幣顯示為 K 或 M
     * @param {number} value - 要格式化的數字
     * @returns {string|number} 根據站點格式化後的結果
     */
    mailAttachment(value) {
      if (typeof value !== 'number' || isNaN(value)) return '0'

      // 使用抽離的方法獲取格式化器
      const formatter = this.getFormatterByStation()

      // 小於 1000 的數字正常顯示（包含千分位格式）
      if (value < 1000) {
        return formatter.format(Math.floor(value))
      }

      // 1000 到 999999 顯示為 K
      if (value < 1000000) {
        return `${formatter.format(Math.floor(value / 1000))}K`
      }

      // 1000000 以上顯示為 M
      return `${formatter.format(Math.floor(value / 1000000))}M`
    }
  }

  // 建立數字格式化器實例
  const numberFormatter = new NumberFormatter()

  // 注入到 Nuxt 上下文，讓全局可以使用 $numberFormatter
  inject('numberFormatter', numberFormatter)
}
