const thirdParty = require(`~/station/${process.env.STATION}/sdk.js`).default
const googleLogin = thirdParty.login.google
/* eslint-disable no-undef */
export default ({ app }, inject) => {
  // google 官方文件網址: https://developers.google.com/identity/gsi/web/reference/js-reference?hl=zh-tw#IdConfiguration
  const script = document.createElement('script')
  script.setAttribute('src', 'https://accounts.google.com/gsi/client')

  script.onload = () => {
    //不要讓idToken暴露，所以用key value的方式存取
    google.accounts.id.initialize({
      client_id: googleLogin.client_id,
      callback: googleLogin.callback,
      context: googleLogin.context
    })
  }

  function login({ successful = null, failed = null }) {
    google.accounts.id.prompt((notification) => {
      if (notification.isNotDisplayed()) {
        failed && failed(notification.getNotDisplayedReason())
      } else if (notification.isSkippedMoment()) {
        app.$cookies.remove('g_state')
        failed && failed(notification.getSkippedReason())
      } else if (notification.getDismissedReason() === 'credential_returned') {
        successful && successful(googleLogin.authData.idToken)
      }
    })
  }

  document.head.appendChild(script)

  const gapi = {
    login
  }

  inject('gapi', gapi)
}
