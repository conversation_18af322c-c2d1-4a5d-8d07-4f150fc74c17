import homeGameListBase from '@/mixins/homeGameListBase'
import { cloneDeep } from 'lodash'
const NUXT_ENV = process.env.NUXT_ENV
const loadConfig = require(`~/station/${process.env.STATION}/${NUXT_ENV}.js`).default

export default {
  mixins: [homeGameListBase],
  data() {
    return {
      // 擴展父類別的 list，加入 india_01 特有的 newestGame
      list: {
        inCompleteGame: [],
        recentGame: [],
        liveGame: [],
        hotGame: [],
        fishingGame: [],
        featuredGame: [],
        chessAndCardGame: [],
        newestGame: [] // india_01 特有的
      },
      // india_01 特有的配置
      newestGameSet: {
        max: 15,
        sortType: 3
      }
    }
  },
  computed: {
    // 新增 india_01 特有的 computed
    newestGameList({ list }) {
      return list.newestGame.length > 0
        ? this.filterList(this.getGameListId(list.newestGame), list.newestGame)
        : []
    },
    // 新增 gameShowMax
    gameShowMax() {
      return this.recentGameSet?.max || 10
    }
  },
  methods: {
    // 覆寫 init 方法，加入 getNewestGameList
    async init() {
      // 調用父類別的 init
      await homeGameListBase.methods.init.call(this)
      // 加入 india_01 特有的初始化
      await this.getNewestGameList()
    },

    // india_01 特有的方法
    async getNewestGameList() {
      try {
        const rawGameData = await this.getNewestGamesEvent(
          this.providers,
          this.newestGameSet.max,
          this.newestGameSet.sortType
        )
        this.list.newestGame = cloneDeep(rawGameData)
      } catch (error) {
        this.list.newestGame = []
      }
    },

    async getNewestGamesEvent(providers, max, sortType) {
      if (providers.length > 0) {
        const hasRtp = (item, provider) =>
          item.platformId == provider.id && provider.hasRtp === true
        const getGames = (showGamesCount, games) => {
          games = games.filter((item) => providers.some((provider) => hasRtp(item, provider)))
          return games.slice(0, showGamesCount)
        }
        let list = []

        await this.getNewestGamesData(sortType).then((games) => {
          games.forEach((item) => {
            item.dailyRtp = undefined
            item.weeklyRtp = undefined
            item.monthlyRtp = undefined
            item.thumbUrl = item.thumbUrl || this.$store.getters['gameHall/gameDefaultImg']
          })
          list = getGames(max, games)
        })

        // 先加入 store
        this.$store.commit('gameHall/ADD_ALL_GAME_LIST', list)

        // 獲取 RTP 資料（只更新 RTP Map）
        const nonLiveGameIds = list
          .filter((game) => game.categoryType !== 200)
          .map((game) => game.id)

        if (nonLiveGameIds.length > 0) {
          await this.$store.dispatch('gameHall/fetchRTPList', {
            gameIds: nonLiveGameIds,
            options: { fallbackToAPI: true }
          })
        }
        return list
      }
    },

    async getNewestGamesData(type) {
      let games = await this.getUnCategorizedSortGameListData(type, false)
      games = games.count > 0 ? games.list : []
      return games
    },

    // 恢復原來的 getPopularGamesData 方法
    async getPopularGamesData(gameCategoryId, type) {
      let games = await this.getSortGameListData(gameCategoryId, type)
      games = games.count > 0 ? games.list : []
      return games
    },

    // 使用 loadConfig 的站台需要覆寫此方法
    async getFeaturedGamesDataHandler() {
      // 暫時使用全局的 window.loadConfig
      window.loadConfig = loadConfig
      // 調用父類別的方法
      return homeGameListBase.methods.getFeaturedGamesDataHandler.call(this)
    }
  }
}
