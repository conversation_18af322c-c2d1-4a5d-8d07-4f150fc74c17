<template>
  <v-chip
    class="rtp-chip px-2 ma-0 w-100 custom-text-noto text-body-2"
    :color="backgroundColor"
    small
    label
    :text-color="chipTextColor"
  >
    <p class="w-100 mb-0 d-flex align-center" :class="justify">
      <span class="custom-text-noto text-caption pr-1">
        <template v-if="text"> {{ $t(text) }} </template>
        {{ rtp }}%
      </span>
      <span class="material-symbols-outlined" :class="iconColor">
        {{ icon }}
      </span>
    </p>
  </v-chip>
</template>
<script>
  export default {
    name: 'Rtpshow',
    props: {
      text: {
        type: String,
        default: ''
      },
      backgroundColor: {
        type: String,
        required: true
      },
      rtp: {
        type: Number,
        default: 0,
        required: true
      },
      icon: {
        type: String,
        required: true
      },
      iconColor: {
        type: String,
        required: true
      },
      chipTextColor: {
        type: String,
        required: true
      },
      justify: {
        type: String,
        default: 'justify-center'
      }
    }
  }
</script>
<style lang="scss">
  .rtp-chip {
    .v-chip__content {
      width: 100%;
    }
  }
</style>
