<template>
  <div>
    <v-dialog
      v-model="showPlayerInfoCardTmp"
      :fullscreen="breakpoint.xsOnly"
      width="630"
      persistent
      scrollable
      :content-class="breakpoint.xsOnly ? '' : 'rounded-lg'"
    >
      <v-card class="pa-0" elevation="0" color="dialog-fill" max-height="90vh">
        <customDialogTitle
          title-height="58px"
          :title="$t('player_info').toUpperCase()"
          @closeDialog="closeDialog"
          style="height: 58px"
        />
        <!-- 標籤 -->
        <v-tabs
          background-color="transparent"
          color="primary"
          class="px-4 px-sm-6"
          v-model="selectType"
        >
          <v-tab class="a-link" href="#personal_status" key="personal_status">
            {{ $t('personal_status') }}
          </v-tab>
          <v-tab
            v-if="$UIConfig.playerInfoCardSetting.dailyListEnable"
            class="a-link"
            href="#daily_list"
            key="daily_list"
          >
            {{ $t('daily_list') }}
          </v-tab>
          <v-tab
            v-if="$UIConfig.playerInfoCardSetting.achievementEnable"
            class="a-link"
            href="#achievement"
            key="achievement"
          >
            {{ $t('achievement') }}
          </v-tab>
        </v-tabs>
        <!-- 內容 -->
        <v-card-text
          id="player-info-card"
          :class="[
            'h-100-percent px-4 pb-4 pb-sm-6 px-sm-6',
            breakpoint.xsOnly ? '' : 'scrollable-sm'
          ]"
        >
          <v-tabs-items v-model="selectType">
            <!-- 個人狀態 -->
            <v-tab-item key="personal_status" value="personal_status">
              <div :class="{ h643: breakpoint.xsOnly }">
                <characterInfo
                  class="my-1"
                  :info.sync="selectPlayer"
                  is-card
                  :is-friend="isFriend"
                  :is-block="isBlock"
                />
                <v-divider class="mb-4"></v-divider>
                <playerDetail :user-detail="selectPlayer" is-card />
                <v-row
                  no-gutters
                  class="pt-3"
                  justify="start"
                  v-if="selectPlayer.userName !== userName"
                >
                  <!-- 加為好友 breakpoint.xsOnly -->
                  <v-col cols="6" sm="4" class="pa-2 pl-0" v-if="!isFriend">
                    <v-btn
                      outlined
                      color="primary"
                      class="custom-btn w-100"
                      @click="checkAddFriendInDialog(selectPlayer.username)"
                    >
                      <span class="material-symbols-rounded icon-left"> person_add </span>
                      <span class="pl-2 primary--text btn-text">
                        {{ $t('add_as_friend') }}
                      </span>
                    </v-btn>
                  </v-col>
                  <!-- 刪除好友 -->
                  <v-col cols="6" sm="4" class="pa-2 pl-0" v-else>
                    <v-btn
                      outlined
                      color="primary"
                      class="custom-btn w-100"
                      @click="showConfirmDeleteFriendDialog(selectPlayer.userName)"
                    >
                      <v-icon class="icon-left"> mdi-account-remove </v-icon>
                      <span class="pl-2 primary--text btn-text">
                        {{ $t('delete_friend') }}
                      </span>
                    </v-btn></v-col
                  >
                  <!-- 加黑名單 -->
                  <v-col cols="6" sm="4" class="pa-2 pr-0 pr-sm-2" v-if="!isBlock">
                    <v-btn
                      outlined
                      color="primary"
                      class="custom-btn w-100"
                      @click="checkAddBlockInDialog(selectPlayer.username)"
                    >
                      <v-icon class="icon-left"> mdi-account-cancel </v-icon>
                      <span class="pl-2 btn-text">
                        {{ $t('add_to_blacklist') }}
                      </span>
                    </v-btn></v-col
                  >
                  <!--  刪除黑名單 -->
                  <v-col cols="6" sm="4" class="pa-2 pr-0 pr-sm-2" v-else>
                    <v-btn
                      outlined
                      color="primary"
                      class="custom-btn w-100"
                      @click="showConfirmDeleteBlockDialog(selectPlayer.userName)"
                    >
                      <v-icon class="icon-left"> mdi-account-check </v-icon>
                      <span class="pl-2 btn-text">
                        {{ $t('delete_block') }}
                      </span>
                    </v-btn></v-col
                  >
                  <!-- 密語 -->
                  <v-col cols="6" sm="4" class="pa-2 pl-0 pl-sm-2 pr-sm-0">
                    <v-btn
                      outlined
                      color="primary"
                      :disabled="checkIsBlock(selectPlayer.userName)"
                      class="custom-btn w-100"
                      @click="createMessageInDialog(selectPlayer)"
                    >
                      <span class="material-symbols-outlined icon-left"> sms </span>
                      <span class="pl-2 btn-text">
                        {{ $t('whisper') }}
                      </span>
                    </v-btn></v-col
                  >
                  <!-- 寄信 -->
                  <v-col cols="6" sm="4" class="pa-2 pr-0 pb-sm-0 pr-sm-2 pl-sm-0">
                    <v-btn
                      outlined
                      color="primary"
                      :disabled="isOpenGameFrmae"
                      class="custom-btn w-100"
                      @click="openSendMailDialogInDialog(selectPlayer)"
                    >
                      <span class="material-symbols-outlined icon-left"> mail </span>
                      <span class="pl-2 btn-text">
                        {{ $t('send_mail') }}
                      </span>
                    </v-btn></v-col
                  >
                  <!-- 檢舉 -->
                  <v-col cols="6" sm="4" class="pa-2 pl-0 pb-0 pl-sm-2">
                    <v-btn
                      outlined
                      color="primary"
                      class="custom-btn w-100"
                      @click="showReportDialog(selectPlayer.userName)"
                    >
                      <v-icon class="icon-left"> mdi-account-alert </v-icon>
                      <span class="pl-2 btn-text">
                        {{ $t('report') }}
                      </span>
                    </v-btn></v-col
                  >
                  <div
                    v-if="$UIConfig.playerInfoCardSetting.useVisitBtn"
                    class="mb-3"
                    :class="{
                      'w-150-px': breakpoint.xsOnly,
                      'w-173-px': !breakpoint.xsOnly
                    }"
                  ></div>
                </v-row>
              </div>
            </v-tab-item>
            <!-- 日榜 -->
            <v-tab-item key="daily_list" value="daily_list">
              <div :class="{ h643: breakpoint.xsOnly }">
                <div class="custom-text-noto my-4">
                  {{ $t($UIConfig.playerInfoCardSetting.dailyListTableText) }}
                </div>
                <dailyList
                  :user-name="selectPlayer.username"
                  :height="breakpoint.xsOnly ? '100%' : tableHeight + 14"
                  :items-per-page="10"
                  :items-per-page-options="[10, 20, 30, -1]"
                  is-card
                ></dailyList>
              </div>
            </v-tab-item>
            <!-- 成就 -->
            <v-tab-item key="achievement" value="achievement">
              <div :class="{ h643: breakpoint.xsOnly }">
                <!-- description -->
                <v-row
                  no-gutters
                  class="custom-text-noto text-caption grey-3--text pb-3"
                  style="font-size: 12px !important"
                >
                  <v-col cols="12">
                    <span>{{ $t('achievements_desc1') }}</span></v-col
                  >
                  <v-col cols="12">
                    <span v-if="$UIConfig.lock.otherStarCityOnlinePlatformsReminderDisabled">{{
                      $t('achievements_desc2_1', { xinstar: $t('daily_list_desc2_2') })
                    }}</span>

                    <i18n v-else path="achievements_desc2_1" tag="span">
                      <template v-slot:xinstar>
                        <span class="primary--text" style="cursor: pointer" @click="goDownload">{{
                          $t('daily_list_desc2_2')
                        }}</span>
                      </template>
                    </i18n>
                  </v-col>
                </v-row>
                <achievement
                  :user-name="selectPlayer.username"
                  :height="breakpoint.xsOnly ? '100%' : tableHeight"
                  :items-per-page="10"
                  :items-per-page-options="[10, 20, 30, -1]"
                  is-card
                ></achievement>
              </div>
            </v-tab-item>
          </v-tabs-items>
        </v-card-text>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
  import languageOption from '@/mixins/languageOption.js'
  import relationship from '@/mixins/relationship.js'
  import scssLoader from '@/mixins/scssLoader.js'
  import chat from '@/mixins/chatroom/chat.js'
  import guildMgr from '@/mixins/guildMgr'
  export default {
    name: 'playerInfoCardDialog',
    mixins: [relationship, chat, scssLoader, languageOption, guildMgr],
    components: {
      customDialogTitle: () => import('~/components/customDialogTitle'),
      dailyList: () => import('~/components/player_info/dailyList'),
      achievement: () => import('~/components/player_info/achievement'),
      characterInfo: () => import('~/components/characterInfo'),
      playerDetail: () => import('~/components/player_info/playerDetail')
    },
    props: {
      showPlayerInfoCardDialogStatus: { type: Boolean, required: true, default: false }
    },
    data() {
      return {
        showPlayerInfoCardTmp: this.showPlayerInfoCardDialogStatus,
        selectType: 'personal_status',
        friendGrid: {},
        userDetail: {},
        isFriend: false,
        isBlock: false
      }
    },
    async created() {
      //使用於是否可以寄信
      this.$store.dispatch('role/updateUserDetail')
      this.$store.dispatch('social/getUserDetail', this.selectPlayer.username).then((res) => {
        this.userDetail = res
      })
    },
    computed: {
      userName({ $store }) {
        return $store.getters['role/userName']
      },
      //由於檢舉功能未上 按鈕隱藏導致高度降低，所以-50
      contentHeight() {
        return this.selectPlayer.userName === this.userName ? 582 : 690
      },
      tableHeight() {
        return this.selectPlayer.userName === this.userName ? 318 : 428
      },
      level({ $store }) {
        return $store.getters['role/level']
      },
      vipLevel({ $store }) {
        return $store.getters['role/vipLevel']
      },
      isOpenGameFrmae({ $store }) {
        const gameLink = $store.getters['gameHall/gameLink']
        return gameLink !== ''
      },
      vipLevelUpThanBronze({ $store }) {
        return $store.getters['role/vipLevel'] >= 1
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    watch: {
      showPlayerInfoCardDialogStatus: {
        async handler(status) {
          this.showPlayerInfoCardDialogStatusTmp = status
        }
      },
      friendList: {
        handler() {
          this.isFriend = this.checkIsFriend(this.selectPlayer.username)
        },
        deep: true,
        immediate: true
      },
      blockList: {
        handler() {
          this.isBlock = this.checkIsBlock(this.selectPlayer.username)
        },
        deep: true,
        immediate: true
      }
    },
    methods: {
      closeDialog() {
        this.$emit('update:showPlayerInfoCardDialogStatus', false)
      },
      goDownload() {
        if (this.$device.isAndroid || this.$device.isIos || this.$device.isMacOS) {
          const url = 'https://www.xin-stars.com/goStore'
          this.$lineOpenWindow.open(url)
        } else {
          this.$router.push({ path: this.localePath('/downloads'), hash: '#pc' })
          this.$nuxt.$emit('root:showPlayerInfoCardDialogStatus', false)
        }
      },
      //寄信
      //判斷流程 是否為正式會員 -> 手機是否有綁定 -> 是否為好友
      openSendMailDialogInDialog(player) {
        const showNotyDialog = (message) => {
          this.$notify.error(message)
        }

        const openMailDialog = () => {
          this.$nuxt.$emit('root:mailDialogStatus', {
            show: true,
            name: player.username
          })
          this.$store.commit('mail/SET_OPEN_SEND_MAIL_DIRECTLY', true)
          this.$nuxt.$emit('root:showPlayerInfoCardDialogStatus', false)
        }
        const checkIsGuildMember = (name) => {
          const memberList = this.$store.getters['guild/guildMemberInfoList']
          const isGuildMember = memberList.some((item) => item.name === name)
          return isGuildMember
        }
        if (!this.hasGuild) {
          showNotyDialog(this.$t('mail_send_noty6'))
          return
        }

        const isGuildMaster = this.guildRank === 3
        const isPlayerGuildMaster = this.guildMaster === player.userName
        const isGuildMember = checkIsGuildMember(player.userName)

        if (isGuildMaster) {
          if (isGuildMember) openMailDialog()
          else showNotyDialog(this.$t('mail_send_noty8'))
        } else {
          if (isPlayerGuildMaster) openMailDialog()
          else showNotyDialog(this.$t('mail_send_noty7'))
        }
      },
      //加好友
      async checkAddFriendInDialog(username) {
        await this.checkAddFriend(username)
        let friend = this.friendList.find(function (value) {
          return value.username === username
        })
        let friendLowerCase = this.friendList.find(function (value) {
          return value.username.toLowerCase() === username.toLowerCase()
        })
        if (!friend && friendLowerCase) {
          this.$nuxt.$loading.start()
          let role = await this.getPlayerData(friendLowerCase.username)
          this.$nuxt.$loading.finish()
          this.setSelectPlayerInfo(role)
        }
      },
      //密語
      createMessageInDialog(player) {
        if (player.online) {
          this.createMessage(player.userName)
          this.closeDialog()
        } else {
          this.$notify.warning(this.$t('player_offline', { player: player.userName }))
        }
      },
      //加黑名單
      async checkAddBlockInDialog(username) {
        await this.checkAddBlock(username)
        let block = this.blockList.find(function (value) {
          return value.username === username
        })
        let blockLowerCase = this.blockList.find(function (value) {
          return value.username.toLowerCase() === username.toLowerCase()
        })
        if (!block && blockLowerCase) {
          this.$nuxt.$loading.start()
          let role = await this.getPlayerData(blockLowerCase.username)
          this.$nuxt.$loading.finish()
          this.setSelectPlayerInfo(role)
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  $primary-variant-3: map-get($colors, 'primary-variant-3');
  $primary: map-get($colors, 'primary');
  .border-style-title {
    border-left: solid 1px $primary-variant-3 !important;
    border-right: solid 1px $primary-variant-3 !important;
    border-top: solid 1px $primary-variant-3 !important;
  }
  .border-style-bottom {
    border-left: solid 1px $primary-variant-3 !important;
    border-right: solid 1px $primary-variant-3 !important;
    border-bottom: solid 1px $primary-variant-3 !important;
  }
  .custom-btn {
    position: relative;
  }

  .icon-left {
    position: absolute;
    left: -4px;
    top: 50%;
    transform: translateY(-50%);
  }

  .btn-text {
    width: 100%;
    text-align: center;
    padding-left: 24px !important; /* 補償 icon 的寬度，使文字真正置中 */
  }
  .v-tabs-items {
    background-color: transparent !important;
  }
  .a-link {
    color: rgba(255, 255, 255, 0.6) !important;
  }
  .v-tab--active {
    color: $primary !important;
  }
  .w-130-px {
    width: 130px !important;
  }
  .w-173-px {
    width: 173px !important;
  }
  .w-150-px {
    width: 150px !important;
  }
  #player-info-card {
    &.scrollable-sm {
      max-height: calc(90vh - 76px);
      overflow-y: auto;
    }
    @supports (height: 90svh) {
      &.scrollable-sm {
        max-height: calc(90svh - 76px);
      }
    }
  }
</style>
