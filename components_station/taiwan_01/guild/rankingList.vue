<template>
  <v-row no-gutters id="guild-list-data-table">
    <v-col cols="12">
      <template>
        <v-data-table
          class="grey-6"
          :class="{ 'cursor-pointer': $vuetify.breakpoint.xsOnly }"
          fixed-header
          @pagination="scrollToTop"
          @update:page="handlePageChange"
          @click:row="onItemClick"
          :hide-default-footer="$vuetify.breakpoint.xsOnly"
          :height="isCard ? height : '100%'"
          :items-per-page="itemsPage"
          :headers="guildListHeaders"
          :page="currentPage"
          :items="rankingList"
          :footer-props="{
            'items-per-page-text': $t('items_per_page'),
            'items-per-page-all-text': $t('all'),
            'page-text': `{0}-{1} ${$t('total_page')} {2} ${$t('quantity')}`,
            'items-per-page-options': itemsPerPageOptions
          }"
        >
          <template v-if="rankingList.length === 0" v-slot:body>
            <tbody v-show="$vuetify.breakpoint.xsOnly" :height="isCard ? height - 48 : 'auto'">
              <tr class="v-data-table__empty-wrapper d-flex justify-center">
                <td class="d-flex align-center" colspan="5">
                  <span class="text-center">
                    {{ $t('no_data') }}
                  </span>
                </td>
              </tr>
            </tbody>
            <tbody v-show="!$vuetify.breakpoint.xsOnly" :height="isCard ? height - 48 : 'auto'">
              <tr class="v-data-table__empty-wrapper">
                <td colspan="5">
                  <span class="text-center">
                    {{ $t('no_data') }}
                  </span>
                </td>
              </tr>
            </tbody>
          </template>

          <!--item-->
          <template v-slot:item="{ item, headers }">
            <!--normal item-->
            <tr
              v-if="!$vuetify.breakpoint.xsOnly"
              :class="{ 'self-row-color': item.guildName === selfGuildName }"
              @click="onItemClick(item)"
            >
              <td class="cursor-pointer">
                <div class="d-flex justify-center">
                  <span>
                    {{ item.rank }}
                  </span>
                </div>
              </td>
              <td class="cursor-pointer">
                <div class="d-flex flex-nowrap my-2">
                  <div class="mr-2 character-info-avatar-badge">
                    <avatar frame middle :guild-id="item.id" />
                  </div>
                  <span class="d-flex align-center primary--text">
                    {{ item.guildName }}
                  </span>
                </div>
              </td>
              <td class="cursor-pointer">
                <div class="d-flex justify-center">
                  <span>
                    {{ item.onlineState }}
                  </span>
                </div>
              </td>
              <td class="cursor-pointer">
                <span>
                  {{ item.power.toLocaleString() }}
                </span>
              </td>
              <td v-if="isGuildRankOn" class="cursor-pointer">
                <span>
                  {{ item.rankingReward }}
                </span>
              </td>
            </tr>
            <!--normal item-->
            <!--mobile item-->
            <tr
              v-if="$vuetify.breakpoint.xsOnly"
              class="v-data-table__mobile-table-row"
              :class="{ 'self-row-color': item.guildName === selfGuildName }"
              @click="onItemClick(item)"
            >
              <div class="px-4">
                <td class="v-data-table__mobile-row">
                  <div class="v-data-table__mobile-row__header">
                    <span class="content--text px-0">
                      {{ headers[0].text }}
                    </span>
                  </div>
                  <div class="v-data-table__mobile-row__cell">
                    <span class="primary--text">
                      {{ item.rank }}
                    </span>
                  </div>
                </td>
                <td class="v-data-table__mobile-row">
                  <div class="v-data-table__mobile-row__header">
                    <span class="content--text px-0">
                      {{ headers[1].text }}
                    </span>
                  </div>
                  <div class="v-data-table__mobile-row__cell">
                    <div class="d-flex flex-nowrap my-2">
                      <div class="mr-2 character-info-avatar-badge">
                        <avatar frame middle :guild-id="item.id" />
                      </div>
                      <span class="d-flex align-center primary--text">
                        {{ item.guildName }}
                      </span>
                    </div>
                  </div>
                </td>
                <td class="v-data-table__mobile-row">
                  <div class="v-data-table__mobile-row__header">
                    <span class="content--text px-0">
                      {{ headers[2].text }}
                    </span>
                  </div>
                  <div class="v-data-table__mobile-row__cell">
                    <span>
                      {{ item.onlineState }}
                    </span>
                  </div>
                </td>
                <td class="v-data-table__mobile-row">
                  <div class="v-data-table__mobile-row__header">
                    <span class="content--text px-0">
                      {{ headers[3].text }}
                    </span>
                  </div>
                  <div class="v-data-table__mobile-row__cell">
                    <span>
                      {{ item.power.toLocaleString() }}
                    </span>
                  </div>
                </td>
                <td v-if="isGuildRankOn" class="v-data-table__mobile-row">
                  <div class="v-data-table__mobile-row__header">
                    <span class="content--text px-0">
                      {{ headers[4].text }}
                    </span>
                  </div>
                  <div class="v-data-table__mobile-row__cell">
                    <span>
                      {{ item.rankingReward }}
                    </span>
                  </div>
                </td>
              </div>
              <v-divider class="default-content-2"></v-divider>
            </tr>
            <!--mobile item-->
          </template>
          <!--item-->

          <template v-if="$vuetify.breakpoint.xsOnly" v-slot:footer="{ props: { pagination } }">
            <div class="v-data-footer">
              <v-row no-gutters>
                <v-col>
                  <div class="v-data-footer__select d-flex justify-start ml-3">
                    <span> {{ $t('items_per_page') }}</span>
                    <v-select
                      class="py-0 mt-3 mb-3"
                      v-model="select"
                      hide-details
                      height="32"
                      @input="onSelect"
                      :items="pagePaginationitem(itemsPerPageOptions)"
                    ></v-select>
                    <span class="v-data-footer__pagination">
                      {{ pagePagination(pagination) }}
                    </span>
                  </div>
                </v-col>
              </v-row>
              <v-row no-gutters>
                <v-col>
                  <v-btn
                    class="v-data-footer__icons-before"
                    icon
                    :disabled="pagination.pageStart === 0"
                    @click="currentPage = pagination.page - 1 === 0 ? 1 : pagination.page - 1"
                  >
                    <v-icon dark> mdi-chevron-left </v-icon>
                  </v-btn>
                  <v-btn
                    class="v-data-footer__icons-after"
                    icon
                    :disabled="pagination.pageStop === pagination.itemsLength"
                    @click="
                      currentPage =
                        pagination.page + 1 === pagination.pageCount
                          ? pagination.pageCount
                          : pagination.page + 1
                    "
                  >
                    <v-icon dark> mdi-chevron-right </v-icon>
                  </v-btn>
                </v-col>
              </v-row>
            </div>
          </template>
        </v-data-table>
      </template>
    </v-col>
  </v-row>
</template>

<script>
  import images from '~/mixins/images'
  import guildMgr from '~/mixins/guildMgr'
  import cloneDeep from 'lodash/cloneDeep'
  import scssLoader from '~/mixins/scssLoader.js'
  export default {
    name: 'rankingList',
    props: {
      userName: { type: String, required: true, default: '' },
      isCard: {
        type: Boolean,
        default: false
      },
      isInfoPage: {
        type: Boolean,
        default: false
      },
      height: {
        type: [Number, String],
        default: 550
      },
      itemsPerPage: {
        type: Number,
        default: 15
      },
      itemsPerPageOptions: {
        type: Array,
        default: () => [10, 20, 30, -1]
      },
      rankingList: {
        type: Array,
        default: () => []
      }
    },
    mixins: [scssLoader, guildMgr, images],
    components: {
      avatar: () => import('~/components/guild/guildAvatar')
    },
    data() {
      return {
        guildRanking: {},
        showGuildInfoCardDialogStatus: false,
        itemsPage: this.itemsPerPage,
        select: this.itemsPerPage,
        currentPage: 1,
        guildShowInfo: {
          guildMemberInfoList: [],
          guildOnlineMembers: 0,
          guildCaption: '',
          guildMembers: 0,
          guildName: '',
          guildPower: 0,
          guildRank: 1,
          guildId: 0
        }
      }
    },

    computed: {
      selfGuildName({ $store }) {
        return $store.getters['guild/guildName']
      },
      station({ $store }) {
        return $store.getters['station']
      },
      guildListHeaders() {
        const guildList = [
          {
            text: this.$t('ranking'),
            value: 'rank',
            align: 'center',
            width: '10%',
            class: 'grey-4 primary--text px-0',
            sortable: false
          },
          {
            text: this.$t('guild_name'),
            value: 'guildName',
            width: '30%',
            class: 'grey-4 primary--text ',
            sortable: false
          },
          {
            text: this.$t('online') + '/' + this.$t('head_count'),
            value: 'onlineState',
            align: 'center',
            width: '12%',
            class: 'grey-4 primary--text px-0',
            sortable: false
          },
          {
            text: this.$t('guild_power'),
            value: 'power',
            width: '24%',
            class: 'grey-4 primary--text ',
            sortable: false
          }
        ]

        if (this.isGuildRankOn) {
          guildList.push({
            text: this.$t('this_reward'),
            value: 'rankingReward',
            width: '24%',
            class: 'grey-4 primary--text ',
            sortable: false
          })
        }

        return guildList
      }
    },
    methods: {
      pagePaginationitem(itemArray) {
        let newItemArray = cloneDeep(itemArray)
        newItemArray[newItemArray.findIndex((x) => x === -1)] = this.$t('all')
        return newItemArray
      },
      onSelect() {
        if (this.select === this.$t('all')) this.itemsPage = -1
        else this.itemsPage = this.select
      },
      scrollToTop() {
        try {
          window.scrollTo({ top: 0, behavior: 'auto' })
        } catch (error) {
          console.log(error)
        }
      },
      handlePageChange(page) {
        this.currentPage = page
      },
      pagePagination(pagination) {
        return pagination.pageCount === 0
          ? '-'
          : `${pagination.pageStart + 1}-${pagination.pageStop} ${this.$t('total_page')}
            ${pagination.itemsLength} ${this.$t('quantity')}`
      },
      async onItemClick(row) {
        if (row.guildName === this.selfGuildName) {
          this.$router.push(this.localePath('/guild/info'))
        } else {
          const guildInfo = await this.setupGuildById(row.id)
          this.$nuxt.$emit('root:guildDialogStatus', { show: true, info: guildInfo })
        }
      }
    }
  }
</script>

<style lang="scss">
  $grey-4: map-get($colors, 'grey-4');
  #guild-list-data-table {
    .character-info-avatar-badge {
      position: relative;
      .badge-border {
        position: absolute;
        top: 0;
      }
    }
    .v-data-table-header-mobile {
      th {
        background: $grey-4 !important;
      }
    }
    .v-data-table {
      tbody {
        tr {
          &:hover {
            background: $grey-4 !important;
          }
        }
      }
    }
    .self-row-color {
      background: linear-gradient(90deg, #670f09 0%, #2b0002 100%) !important;
    }
  }
</style>
