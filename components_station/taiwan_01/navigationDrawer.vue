<template>
  <v-navigation-drawer
    v-if="$vuetify.breakpoint.mdAndUp"
    app
    clipped
    disable-resize-watcher
    touchless
    :value="drawerModel"
    :mini-variant="$vuetify.breakpoint.mdOnly ? false : !drawer"
    :class="{ 'background-opacity-50': $vuetify.breakpoint.lgAndUp }"
    color="transparent"
    mini-variant-width="72"
    @input="(drawer) => this.$vuetify.breakpoint.mdOnly && switchDrawer(drawer)"
  >
    <v-list expand class="py-4 px-2" color="transparent">
      <!-- logo & close drawer btn  -->
      <v-list-item v-if="$vuetify.breakpoint.mdOnly" class="pb-4">
        <v-list-item-content>
          <v-list-item-title @click="goHome">
            <v-img
              :src="logoImg"
              contain
              :width="$vuetify.breakpoint.mdAndUp ? '130' : '105'"
              class="cursor-pointer"
            />
          </v-list-item-title>
        </v-list-item-content>
        <v-list-item-action @click.stop="switchDrawer(false)">
          <v-icon>mdi-chevron-left</v-icon>
        </v-list-item-action>
      </v-list-item>
      <!-- home btn & game btn  -->
      <v-list-item-group :value="selectedItem" color="primary">
        <v-list-item
          v-for="item in gameCategory"
          v-show="item.enable"
          class="rounded-pill overflow-hidden justify-start mh-auto py-2 mb-1"
          :disabled="maintainSystem[0].maintaining"
          :key="item.name"
          :value="item.code"
          @click="item.dict === 'home' ? goHome() : goGameCategory(item.code)"
          @mouseover="hoverCategory = item.code"
          @mouseleave="hoverCategory = null"
        >
          <v-list-item-icon class="align-self-center my-0">
            <v-icon v-if="maintainSystem[0].maintaining" :disabled="maintainSystem[0].maintaining">
              {{ item.icon }}
            </v-icon>
            <lottie-vue-player
              v-else
              :src="getLottieOptions(item)"
              :key="`${animationLoop}-${item.dict}-${
                selectedItem === item.code
                  ? 'active'
                  : hoverCategory === item.code
                  ? 'hover'
                  : 'default'
              }`"
              :autoplay="animationLoop"
              :loop="animationLoop"
              style="width: 24px; height: 24px"
            />
          </v-list-item-icon>
          <v-list-item-content class="py-0">
            <v-list-item-title>{{
              item.dict === 'home' ? $t(item.dict) : $t(item.dict + '_short')
            }}</v-list-item-title>
          </v-list-item-content>
        </v-list-item>
      </v-list-item-group>
      <!-- divider  -->
      <gradientDivider
        v-if="roleCategory.length"
        divider-type="squareToSquare"
        :square-size="10"
        class="pa-3"
      />
      <!-- news -->
      <v-list-item
        class="rounded-pill overflow-hidden justify-start mh-auto py-2 mb-1"
        :class="{
          'v-item--active v-list-item--active primary--text':
            $route.path === '/news' && !maintainSystem[0].maintaining
        }"
        :disabled="maintainSystem[0].maintaining"
        @click.prevent="goOtherPage('/news?type=2&page=1')"
        @mouseover="hoverCategory = 900"
        @mouseleave="hoverCategory = null"
      >
        <v-list-item-icon class="align-self-center my-0">
          <span v-if="maintainSystem[0].maintaining" class="material-symbols-outlined">
            campaign
          </span>
          <span v-else class="material-symbols-outlined">
            <lottie-vue-player
              :src="getLottieOptions({ code: 900, dict: 'campaign' })"
              :key="`${animationLoop}-campaign-${
                selectedItem === 900 ? 'active' : hoverCategory === 900 ? 'hover' : 'default'
              }`"
              :autoplay="animationLoop"
              :loop="animationLoop"
              style="width: 24px; height: 24px"
            />
          </span>
        </v-list-item-icon>
        <v-list-item-content class="py-0">
          <v-list-item-title>
            {{ $t('announcement') }}
          </v-list-item-title>
        </v-list-item-content>
      </v-list-item>
      <!-- role btn -->
      <template v-for="item in roleCategory">
        <v-list-item
          v-if="item.name === 'redeem'"
          class="rounded-pill overflow-hidden justify-start mh-auto py-2 mb-1"
          :disabled="maintainSystem[0].maintaining"
          :key="item.name"
          :value="item.name"
          @click.prevent="showRedeemDialog()"
          @mouseover="hoverCategory = item.name"
          @mouseleave="hoverCategory = null"
        >
          <v-list-item-icon class="align-self-center my-0">
            <span v-if="maintainSystem[0].maintaining" class="material-symbols-outlined">
              {{ item.icon }}
            </span>
            <span v-else class="material-symbols-outlined">
              <lottie-vue-player
                :src="getLottieOptions({ code: item.name, dict: item.name })"
                :key="`${animationLoop}-${item.name}-${
                  hoverCategory === item.name ? 'hover' : 'default'
                }`"
                :autoplay="animationLoop"
                :loop="animationLoop"
                style="width: 24px; height: 24px"
              />
            </span>
          </v-list-item-icon>
          <v-list-item-content class="py-0">
            <v-list-item-title>
              {{ $t(item.name) }}
            </v-list-item-title>
          </v-list-item-content>
        </v-list-item>
        <v-list-group
          v-else-if="item.name === 'guild'"
          :class="[
            { 'remove-hover-bg': isTouchDevice },
            { 'dropdown-maintaining': maintainSystem[0].maintaining },
            { 'dropdown-active': isDropdownActive(item) },
            { 'primary--text': isSelectedGuildChildPage }
          ]"
          :key="`guild-${item.name}`"
          :append-icon="isAllDropdownDisable ? '' : 'mdi-chevron-down'"
          :value="item.active"
          @click.capture.stop="clickDropdown(item)"
          @mouseover="hoverCategory = item.name"
          @mouseleave="hoverCategory = null"
        >
          <template v-slot:prependIcon>
            <v-badge
              v-if="hasGuildNoty"
              color="error"
              :offset-x="drawer || $vuetify.breakpoint.mdOnly ? -40 : -23"
              :offset-y="drawer || $vuetify.breakpoint.mdOnly ? 16 : 6"
              dot
            />
            <span
              v-if="maintainSystem[0].maintaining"
              class="material-symbols-outlined"
              :class="{ 'mr-8': drawer || $vuetify.breakpoint.mdOnly }"
            >
              {{ item.icon }}
            </span>
            <span
              v-else
              class="material-symbols-outlined"
              :class="{ 'mr-8': drawer || $vuetify.breakpoint.mdOnly }"
            >
              <lottie-vue-player
                :src="
                  getLottieOptions({
                    code: item.name,
                    dict: 'flag',
                    active: item.active || isSelectedGuildChildPage
                  })
                "
                :key="`${animationLoop}-flag-${
                  item.active || isSelectedGuildChildPage
                    ? 'active'
                    : hoverCategory === item.name
                    ? 'hover'
                    : 'default'
                }`"
                :autoplay="animationLoop"
                :loop="animationLoop"
                style="width: 24px; height: 24px"
              />
            </span>
          </template>
          <template v-slot:activator>
            <v-list-item-content class="py-0">
              <v-list-item-title> {{ $t(item.name) }}</v-list-item-title>
            </v-list-item-content>
          </template>
          <v-list-item-group :value="selectedItem" color="primary">
            <v-list-item
              v-show="child.name !== 'guild_info' || hasGuild"
              class="rounded-pill overflow-hidden justify-start mh-auto py-2 mb-1"
              v-for="child in item.items"
              :disabled="isAllDropdownDisable"
              :key="child.name"
              :value="child.name"
              @click="goOtherPage(child.path)"
            >
              <v-list-item-icon class="h-24-px align-self-center my-0">
                <v-badge
                  v-if="child.name === 'guild_info' && hasGuildNoty"
                  color="error"
                  offset-x="-40"
                  offset-y="16"
                  dot
                  right
                />
              </v-list-item-icon>
              <v-list-item-content class="py-0 overflow-visible">
                <v-list-item-title class="overflow-visible">
                  <div class="d-flex justify-space-between">
                    <span> {{ $t(child.name) }}</span>
                    <v-badge
                      :value="child.name === 'guild_info' && guildChat > 0"
                      color="error"
                      left
                      offset-x="0"
                      offset-y="20"
                      :content="guildChat > 99 ? '99+' : guildChat"
                    />
                  </div>
                </v-list-item-title>
              </v-list-item-content>
            </v-list-item>
          </v-list-item-group>
        </v-list-group>
      </template>
      <!-- background music -->
      <v-list-item
        class="rounded-pill overflow-hidden justify-start mh-auto py-2 mb-1"
        :disabled="maintainSystem[0].maintaining"
        @click.prevent="showMusicPlayerDialog"
        @mouseover="hoverCategory = 'music'"
        @mouseleave="hoverCategory = null"
      >
        <v-list-item-icon class="align-self-center my-0">
          <span v-if="maintainSystem[0].maintaining" class="material-symbols-outlined">
            music_note
          </span>
          <span v-else class="material-symbols-outlined">
            <lottie-vue-player
              :src="getLottieOptions({ code: 'music', dict: 'music' })"
              :key="`${animationLoop}-music-${hoverCategory === 'music' ? 'hover' : 'default'}`"
              :autoplay="animationLoop"
              :loop="animationLoop"
              style="width: 24px; height: 24px"
            />
          </span>
        </v-list-item-icon>
        <v-list-item-content class="py-0">
          <v-list-item-title>
            {{ $t('background_music') }}
          </v-list-item-title>
        </v-list-item-content>
      </v-list-item>
    </v-list>
  </v-navigation-drawer>
</template>

<script>
  import scroll from '~/mixins/scroll'
  import preLoginAction from '@/mixins/preLoginAction.js'
  import homeDefault from '~/assets/lottie/sidebar/home_default.json'
  import homeHover from '~/assets/lottie/sidebar/home_hover.json'
  import homeSelected from '~/assets/lottie/sidebar/home_selected.json'
  import slotDefault from '~/assets/lottie/sidebar/slot-machine-outline_default.json'
  import slotHover from '~/assets/lottie/sidebar/slot-machine-outline_hover.json'
  import slotSelected from '~/assets/lottie/sidebar/slot-machine-outline_selected.json'
  import liveDefault from '~/assets/lottie/sidebar/poker-chip_default.json'
  import liveHover from '~/assets/lottie/sidebar/poker-chip_hover.json'
  import liveSelected from '~/assets/lottie/sidebar/poker-chip_selected.json'
  import chessAndCardDefault from '~/assets/lottie/sidebar/cards-playing-outline_default.json'
  import chessAndCardHover from '~/assets/lottie/sidebar/cards-playing-outline_hover.json'
  import chessAndCardSelected from '~/assets/lottie/sidebar/cards-playing-outline_selected.json'
  import fishingDefault from '~/assets/lottie/sidebar/fish_default.json'
  import fishingHover from '~/assets/lottie/sidebar/fish_hover.json'
  import fishingSelected from '~/assets/lottie/sidebar/fish_selected.json'
  import campaignDefault from '~/assets/lottie/sidebar/campaign_default.json'
  import campaignHover from '~/assets/lottie/sidebar/campaign_hover.json'
  import campaignSelected from '~/assets/lottie/sidebar/campaign_selected.json'
  import flagDefault from '~/assets/lottie/sidebar/flag_black_default.json'
  import flagHover from '~/assets/lottie/sidebar/flag_black_hover.json'
  import flagSelected from '~/assets/lottie/sidebar/flag_black_selected.json'
  import arcadesDefault from '~/assets/lottie/sidebar/gamepad-square_default.json'
  import arcadesHover from '~/assets/lottie/sidebar/gamepad-square_hover.json'
  import arcadesSelected from '~/assets/lottie/sidebar/gamepad-square_selected.json'
  import partyGamesDefault from '~/assets/lottie/sidebar/sword-cross_default.json'
  import partyGamesHover from '~/assets/lottie/sidebar/sword-cross_hover.json'
  import partyGamesSelected from '~/assets/lottie/sidebar/sword-cross_selected.json'
  import redeemDefault from '~/assets/lottie/sidebar/local_activity_default.json'
  import redeemHover from '~/assets/lottie/sidebar/local_activity_hover.json'
  import redeemSelected from '~/assets/lottie/sidebar/local_activity_selected.json'
  import musicDefault from '~/assets/lottie/sidebar/music_note_default.json'
  import musicHover from '~/assets/lottie/sidebar/music_note_hover.json'
  import musicSelected from '~/assets/lottie/sidebar/music_note_selected.json'
  import specialDefault from '~/assets/lottie/sidebar/star-shooting_default.json'
  import specialHover from '~/assets/lottie/sidebar/star-shooting_hover.json'
  import specialSelected from '~/assets/lottie/sidebar/star-shooting_selected.json'

  export default {
    name: 'NavigationDrawer',
    mixins: [scroll, preLoginAction],
    components: {
      gradientDivider: () => import('~/components/gradientDivider.vue')
    },
    data() {
      return {
        // 因為 this.$vuetify.breakpoint 錯誤初始化問題，故這邊設定一個狀態值 isMounted 來偵測
        // 可參考 https://stackoverflow.com/questions/56376653/nuxt-vue-vuetify-this-vuetify-breakpoint-incorrectly-initialized-as-xs
        isMounted: false,
        drawerTmp: null,
        isLoginFirst: true,
        isMdFirst: true,
        logoImg: `/taiwan_01/logo.webp`,
        isTouchDevice: true,
        hoverCategory: null,
        animationLoop: true,
        categoryOptions: {
          default: {
            home: homeDefault,
            slot: slotDefault,
            live: liveDefault,
            chess_and_card: chessAndCardDefault,
            fishing: fishingDefault,
            party_games: partyGamesDefault,
            arcades: arcadesDefault,
            special: specialDefault,
            campaign: campaignDefault,
            flag: flagDefault,
            redeem: redeemDefault,
            music: musicDefault
          },
          hover: {
            home: homeHover,
            slot: slotHover,
            live: liveHover,
            chess_and_card: chessAndCardHover,
            fishing: fishingHover,
            party_games: partyGamesHover,
            arcades: arcadesHover,
            special: specialHover,
            campaign: campaignHover,
            flag: flagHover,
            redeem: redeemHover,
            music: musicHover
          },
          active: {
            home: homeSelected,
            slot: slotSelected,
            live: liveSelected,
            chess_and_card: chessAndCardSelected,
            fishing: fishingSelected,
            party_games: partyGamesSelected,
            arcades: arcadesSelected,
            special: specialSelected,
            campaign: campaignSelected,
            flag: flagSelected,
            redeem: redeemSelected,
            music: musicSelected
          }
        }
      }
    },
    computed: {
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      },
      gameCategory({ $store }) {
        // 首頁選項
        const homeOption = {
          code: 0,
          dict: 'home',
          path: '/',
          icon: 'mdi-home',
          enable: true
        }
        // 如果系統維護中
        if (this.maintainSystem[0].maintaining) {
          // 檢查是否在客戶端環境
          if (process.client) {
            if (typeof window !== 'undefined' && window.localStorage) {
              const savedData = localStorage.getItem('gameCategory')
              if (savedData && savedData !== 'undefined') {
                // 合併首頁選項和本地存儲的數據
                return [homeOption, ...JSON.parse(savedData)]
              }
            }
          }
          // 如果本地存儲讀取失敗或沒有數據，只返回首頁選項
          return [homeOption]
        }
        // 非維護模式，返回首頁選項和 store 中的遊戲類別
        return [homeOption, ...$store.getters['gameProvider/gameCategory']]
      },
      drawerModel() {
        return this.$vuetify.breakpoint.lgAndUp || this.drawer
      },
      selectedItem() {
        // 移除路徑的第一個字元『/』
        const path = this.$route.path.slice(1).split('/')[0]
        // 取得選取的遊戲大廳類別
        const gameCategory = this.$route.query.gameCategory
        const gameCode = gameCategory ? Number(gameCategory) : 100
        // 將路徑拼裝為選項名稱
        const processPath = () => {
          // 移除路徑第一個的斜線，剩下分割成陣列
          let pArr = this.$route.path.slice(1).split('/')
          return pArr.join('_')
        }
        // 選取的選項名稱
        let select

        switch (path) {
          case '':
            select = 0
            break
          case 'game':
            select = gameCode
            break
          case 'guild':
            select = processPath()
            break
          case 'news':
            select = 900
            break
          default:
            select = null
            break
        }

        return select
      },
      roleCategory({ $store }) {
        this.isAllDropdownDisable && this.$store.commit('role/SET_ROLE_CATEGORY_ALL_INACTIVE')
        return $store.getters['role/roleCategory']
      },
      hasGuildNoty({ $store }) {
        const acceptCount = $store.getters['guild/guildAcceptList'].length
        const hasGuild = $store.getters['guild/hasGuild']
        const chatList = $store.getters['chat/chats']
        const guildChat = chatList.find((x) => x.title === 'guild_chat')
        const guildChatCount = guildChat === undefined ? 0 : guildChat.noty
        return (
          (acceptCount > 0 || guildChatCount > 0) &&
          $store.getters['station'].lock.guild &&
          hasGuild
        )
      },
      guildChat({ $store }) {
        const chatList = $store.getters['chat/chats']
        const hasGuild = $store.getters['guild/hasGuild']
        const guildChat = chatList.find((x) => x.title === 'guild_chat')
        return guildChat === undefined || !hasGuild ? 0 : guildChat.noty
      },
      isLogin({ $store }) {
        return $store.getters['role/isLogin']
      },
      isAllDropdownDisable() {
        return (
          !this.isLogin ||
          this.maintainSystem[0].maintaining ||
          (this.$vuetify.breakpoint.lgAndUp && !this.drawer)
        )
      },
      preLoginAction({ $store }) {
        return $store.getters['role/preLoginAction']
      },
      drawer: {
        get() {
          if (!this.isMounted) return

          // 用來判斷是否有開關選單的登入前動作（name 為 dropdown）
          const { name } = this.preLoginAction

          // 初始化 drawerTmp，lg(含)以上為 true，否則為 false
          this.drawerTmp === null && (this.drawerTmp = this.$vuetify.breakpoint.lgAndUp)

          if (this.isLogin) {
            // 若初次登入，且為 lg(含)以上，則從 localStorage 取得
            // 若 localStorage 無資料，則維持 drawerTmp
            // 加 isLoginFirst，是為了從 md 改 lg(含)以上時，能維持 md 的狀態
            // 加 name 不為 dropdown，是為了不讓 localStorage 影響接續行為
            if (this.$vuetify.breakpoint.lgAndUp && this.isLoginFirst && name !== 'dropdown') {
              const storedDrawer = JSON.parse(localStorage.getItem('drawer'))
              storedDrawer !== null && (this.drawerTmp = storedDrawer)
            }

            // 『初次登入狀態』改 false
            this.isLoginFirst = false

            // lg(含)以上，儲存狀態在 localStorage
            this.$vuetify.breakpoint.lgAndUp && localStorage.setItem('drawer', this.drawerTmp)
          } else {
            // 『初次登入狀態』改 true
            this.isLoginFirst = true
          }

          // 若斷點初次改 md，關閉左側選單
          // 加 isMdFirst，是為了在 mdOnly 時，能正常開關左側選單
          if (this.isMdFirst && this.$vuetify.breakpoint.mdOnly) {
            this.isMdFirst = false
            this.drawerTmp = false
          }

          // 若斷點不為 md，將『初次 md』改 true
          this.$vuetify.breakpoint.name !== 'md' && (this.isMdFirst = true)

          this.$nextTick(() => {
            this.passDrawer(this.drawerTmp)
          })

          return this.drawerTmp
        },
        set(val) {
          this.drawerTmp = val
        }
      },
      hasGuild({ $store }) {
        return $store.getters['guild/hasGuild']
      },
      isSelectedGuildChildPage() {
        const group = this.roleCategory.find((item) => item.name === 'guild')
        return group?.items.some((item) => item.name === this.selectedItem) ?? false
      }
    },
    watch: {
      selectedItem: {
        handler() {
          this.$vuetify.breakpoint.mdOnly && this.switchDrawer(false)
        }
      },
      drawer: {
        handler(status) {
          if (this.$vuetify.breakpoint.lgAndUp) return
          if (status) {
            document.documentElement.classList.add('overflow-y-hidden')
          } else {
            document.documentElement.classList.remove('overflow-y-hidden')
          }
        }
      },
      $route: {
        handler(to) {
          const isCurrentPath = this.roleCategory.some((item) =>
            item.items.some((subItem) => subItem.path === to.path)
          )
          !isCurrentPath && this.$store.commit('role/SET_ROLE_CATEGORY_ALL_INACTIVE')
        }
      }
    },
    created() {
      this.$nuxt.$on('drawer:switchDrawer', this.switchDrawer)
      this.$store.dispatch('role/getRoleCategory')
    },
    mounted() {
      this.$nextTick(() => {
        this.isMounted = true
      })
      this.checkDevice()
      this.$nuxt.$on('root:showMusicPlayerDialogStatus', this.handleMusicDialog)
    },
    beforeDestroy() {
      this.$nuxt.$off('drawer:switchDrawer', this.switchDrawer)
      this.$nuxt.$off('root:showMusicPlayerDialogStatus', this.handleMusicDialog)
    },
    methods: {
      switchDrawer(status, pass = true) {
        if (this.$vuetify.breakpoint.lgAndUp && !status) {
          this.$store.commit('role/SET_ROLE_CATEGORY_ALL_INACTIVE')
        }
        this.drawer = status
        pass && this.passDrawer(status)
      },
      passDrawer(status) {
        this.$nuxt.$emit('drawer:acceptDrawer', status)
      },
      async showRedeemDialog() {
        if (this.$vuetify.breakpoint.mdOnly) this.switchDrawer(false)

        if (this.isLogin) {
          this.$wsClient.send(this.$wsPacketFactory.fatchBalance())

          // 離開Web遊戲館服務
          this.$wsClient.send(
            this.$wsPacketFactory.exitService(this.$xinConfig.WEB_GAME_SERVICE.ID)
          )
          this.$store.commit('xinProtocol/SET_SERVICE_IDS', {
            serviceId: this.$xinConfig.WEB_GAME_SERVICE.ID,
            enable: false,
            connected: false,
            sendId: 0,
            receiveId: 0
          })

          this.$nuxt.$emit('root:showRedeemDialogStatus', true)
        } else {
          this.$nuxt.$emit('root:showLoginDialogStatus', { show: true })
          this.setPreLoginAction('redeem', this.showRedeemDialog)
        }
      },
      // 左側選單摺疊時，下拉選單的 icon，是否該呈現 active 狀態
      isDropdownActive(group) {
        const isExist = group.items.some((item) => item.name === this.selectedItem)
        return this.$vuetify.breakpoint.lgAndUp && !this.drawer && isExist
      },
      async clickDropdown(group, isPreLoginAction) {
        if (!this.isLogin && this.$vuetify.breakpoint.mdOnly) this.switchDrawer(false)

        if (!this.isLogin) {
          this.$nuxt.$emit('root:showLoginDialogStatus', { show: true })
          this.setPreLoginAction('dropdown', this.clickDropdown, group, true)
          this.$store.commit('role/SET_ROLE_CATEGORY_STATUS', { name: 'guild', status: true })
          return
        }

        if ((this.$vuetify.breakpoint.lgAndUp && !this.drawer) || isPreLoginAction) {
          // 目前頁面，是否為下拉選單頁面
          const isDropdownPage = group.items.some((item) => item.path === this.$route.path)
          // 下拉選單第一個選項的路徑，以公會為例，就是公會資訊
          const path = group.items[0].path
          !isDropdownPage && this.$router.push({ path: this.localePath(path) })
          this.scollToTop()
        }

        // 開啟左側選單
        !this.drawer && this.switchDrawer(true)

        // 開關下拉選單
        this.$store.commit('role/SET_ROLE_CATEGORY_STATUS', { name: group.name })

        // 解除 focus
        await this.$nextTick()
        document.activeElement.blur()
      },
      async goHome() {
        if (this.isInErrorPage) {
          this.$nuxt.error(null)
          this.$router.push('/')
        } else {
          this.$router.push('/')
        }
        this.scollToTop()
      },
      async goGameCategory(categoryCode) {
        const path =
          '/game?page=1&gameCategory=' + categoryCode + '&gameSortType=3&providerId=0&searchWord='
        this.$router.push({ path: this.localePath(path) })
        this.scollToTop()
      },
      async goOtherPage(path) {
        this.$router.push({ path: this.localePath(path) })
        this.scollToTop()
      },
      // 用來判斷是否為觸控裝置
      checkDevice() {
        this.isTouchDevice = 'ontouchend' in document
      },
      async showMusicPlayerDialog() {
        if (this.$vuetify.breakpoint.mdOnly) this.switchDrawer(false)

        this.$emit('item-click', '')
        this.$nuxt.$emit('root:showMusicPlayerDialogStatus', true)
      },
      getLottieOptions({ code, dict, active = false }) {
        let state = 'default'
        if (this.hoverCategory === code) state = 'hover'
        if (this.selectedItem === code) state = 'active'
        // 公會有下拉選單時，再次判斷icon的active狀態
        if (dict === 'flag')
          state = active ? 'active' : this.hoverCategory === code ? 'hover' : 'default'
        return (
          JSON.stringify(this.categoryOptions[state][dict]) ||
          JSON.stringify(this.categoryOptions['default']['home'])
        )
      },
      handleMusicDialog(isOpen) {
        this.$nextTick(() => {
          this.animationLoop = !isOpen
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  $card-fill-color: map-get($colors, card-fill);
  $secondary-color: map-get($colors, secondary);
  $default-content-color: map-get($colors, default-content);
  $primary: map-get($colors, 'primary');
  $mobile-bar-height: 64px;
  .v-navigation-drawer ::v-deep {
    @supports (height: 100dvh) {
      height: 100dvh !important;
      @media screen and (min-width: 1264px) {
        max-height: calc(100dvh - #{$mobile-bar-height}) !important;
      }
    }

    &::before {
      content: '';
      background-color: $card-fill-color;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
    }

    &.background-opacity-50::before {
      opacity: 0.5;
    }

    .v-navigation-drawer__border {
      width: 0.8px;
      background-color: $secondary-color !important;
    }

    .v-list-item:not(:is(.v-list-item--active, .v-list-item--disabled)) {
      color: $default-content-color;
      .v-list-item__icon,
      .v-icon {
        color: $default-content-color;
      }
    }
    .v-list-item__icon {
      .vue-lottie-player {
        background: transparent;
      }
      .lf-spinner {
        display: none;
      }
    }
    .v-list-group {
      & > .v-list-item {
        min-height: auto;
        padding-block: 8px;
        margin-bottom: 4px;
        justify-content: flex-start;
        border-radius: 9999px;
        overflow: hidden;
        .v-list-item__icon {
          margin: 0;
          min-width: auto;
        }
      }
      &.remove-hover-bg > .v-list-item:hover::before {
        opacity: 0;
      }
      &.dropdown-maintaining > .v-list-item,
      &.dropdown-maintaining .v-icon,
      &.dropdown-maintaining .v-list-item .v-list-item__icon {
        pointer-events: none;
        color: rgba(255, 255, 255, 0.5) !important;
      }
      &.dropdown-active > .v-list-item {
        color: $primary !important;
        &::before {
          background-color: $primary !important;
          opacity: 0.24;
        }
      }
      &.primary--text {
        .v-list-group__header,
        .v-icon {
          color: $primary !important;
        }
      }
    }
  }
  // 避免因重複點擊相同選項，導致 active 狀態被移除
  .v-list-item--active {
    pointer-events: none;
  }
  // 因下拉選單的子選項沒有icon，用來補足高度
  .h-24-px {
    height: 24px;
  }
</style>
