<!-- eslint-disable vue/no-v-html -->
<template>
  <div>
    <v-dialog
      v-model="showMailSendDialogStatusTmp"
      :fullscreen="breakpoint.xsOnly"
      :width="breakpoint.xsOnly ? '375px' : '457px'"
      scrollable
      persistent
      transition="dialog-transition"
      :content-class="breakpoint.xsOnly ? '' : 'rounded-lg'"
    >
      <v-card color="transparent">
        <customDialogTitle
          :title="$t('send_mail').toUpperCase()"
          @closeDialog="
            () =>
              (selectedFriend && selectedFriend.length != 0) ||
              (mailForm.mailContent && mailForm.mailContent.length != 0) ||
              (mailForm.xinCoin && mailForm.xinCoin.length != 0)
                ? (confirmLeaveStatus = true)
                : closeDialog()
          "
        />
        <v-card-text class="pb-0 px-4 pt-4 px-sm-6 pt-sm-6">
          <v-row no-gutters class="fill-height">
            <v-form ref="form" style="width: 100%">
              <v-card class="d-flex flex-column" color="transparent" flat>
                <v-autocomplete
                  ref="mailRecipient"
                  class="rounded-xxl rounded-b-0"
                  :class="{ 'custom-autocomplete': !stringNullOrEmpty(selectedFriend) }"
                  v-model="selectedFriend"
                  :search-input.sync="searchInput"
                  v-validate="'required'"
                  data-vv-name="recipient"
                  :data-vv-as="$t('recipient')"
                  data-vv-scope="mail"
                  :error-messages="errors.first('mail.recipient')"
                  :label="$t('recipient') + '*'"
                  :items="recipientShowList"
                  item-text="username"
                  item-value="username"
                  filled
                  chips
                  :disabled="recipientDisabledStatus || guildRank !== 3"
                  clearable
                  clear-icon="mdi-close-circle"
                  @click:clear="clearRecipientField"
                >
                  <template v-slot:selection="data">
                    <v-menu ref="playerInfoMenu" z-index="210">
                      <template v-slot:activator="{ on }">
                        <v-chip
                          id="recipient-chip"
                          v-bind="data.attrs"
                          v-on="on"
                          :input-value="data.selected"
                          color="grey-3"
                          class="default-content--text"
                          @click="setPlayerInfo(data.item.username)"
                        >
                          <v-avatar left>
                            <v-img :src="data.item.thumbUrl" @error="errorImgHandler(data.item)">
                              <template v-slot:placeholder>
                                <v-row class="fill-height ma-0" align="center" justify="center">
                                  <v-img :src="defaultImg" contain />
                                </v-row>
                              </template>
                            </v-img>
                          </v-avatar>
                          {{ data.item.username }}
                        </v-chip>
                      </template>
                    </v-menu>
                  </template>
                  <template v-slot:no-data>
                    <v-list-item class="px-0">
                      <v-list-item-content class="custom-text-noto text-body-2 grey-2--text pl-4">
                        {{
                          isRecipientSelf ? $t('cant_send_email_to_self') : $t('mail_send_text2')
                        }}
                      </v-list-item-content>
                    </v-list-item>
                  </template>
                  <template v-slot:item="data">
                    <template v-if="data.item.noty">
                      <span class="custom-text-noto text-body-2 grey-2--text">
                        {{ data.item.text }}
                      </span>
                    </template>
                    <template v-else>
                      <v-row no-gutters style="margin-left: -16px" class="sendMailMenu">
                        <v-badge
                          bottom
                          :color="data.item.online == 1 ? 'green' : 'grey-2'"
                          dot
                          bordered
                          offset-x="25"
                          offset-y="22"
                        >
                          <v-list-item-avatar>
                            <v-img :src="data.item.thumbUrl" contain>
                              <template v-slot:placeholder>
                                <v-row class="fill-height ma-0" align="center" justify="center">
                                  <v-img :src="defaultImg" contain />
                                </v-row>
                              </template>
                            </v-img>
                          </v-list-item-avatar>
                        </v-badge>
                        <v-list-item-content>
                          <v-list-item-title
                            class="custom-text-noto text-subtitle-2"
                            :class="{
                              'default-content--text': data.item.online == 1,
                              'btn-disable--text': !data.item.online == 1
                            }"
                            v-text="data.item.username"
                          />
                        </v-list-item-content>
                      </v-row>
                    </template>
                  </template>
                </v-autocomplete>
                <span class="custom-text-noto text-body-2 grey-3--text pb-4"
                  >＊{{ qualificationNoty }}</span
                >
                <!-- 標題 -->
                <v-text-field
                  v-model="mailForm.mailTitle"
                  v-validate="'required'"
                  disabled
                  :error-messages="errors.first('mail_title')"
                  :label="$t('mail_title') + '*'"
                  :data-vv-as="$t('mail_title')"
                  data-vv-name="mail_title"
                  data-vv-scope="mail"
                  filled
                  shaped
                />
                <!-- 內容 -->
                <v-textarea
                  v-model="mailForm.mailContent"
                  v-validate.continues="'allow_blank_required|text_fullwidth|min:1|max:120'"
                  :error-messages="errors.first('mail.mail_content')"
                  :label="$t('mail_content') + '*'"
                  data-vv-name="mail_content"
                  :data-vv-as="$t('mail_content')"
                  data-vv-scope="mail"
                  filled
                  shaped
                  height="125"
                  no-resize
                  :maxlength="handleCurrentMaxLen"
                  counter="120"
                  :counter-value="() => count"
                >
                </v-textarea>
              </v-card>
              <v-divider />
              <!-- 附件 -->
              <div class="pt-2 d-flex align-center pb-2">
                <span class="default-content--text">
                  {{ $t('appendix') }}
                </span>
                <v-btn
                  color="transparent"
                  icon
                  plain
                  elevation="0"
                  @click="openAttachmentDescriptionDialog"
                >
                  <span class="material-symbols-rounded default-content--text"> help </span>
                </v-btn>
              </div>

              <!-- 星幣 -->
              <v-card class="d-flex" color="transparent" flat>
                <v-card color="transparent" class="pr-2 mt-4" flat>
                  <v-img
                    :src="getImage('<EMAIL>')"
                    :srcset="getSrcset('coin')"
                    width="24"
                    height="24"
                  />
                </v-card>
                <v-text-field
                  ref="xinCoinInput"
                  v-model="mailForm.xinCoin"
                  v-validate="{
                    required: true,
                    coin_min_send_mail: 10000,
                    coin_max: 1000000000
                  }"
                  type="text"
                  min="10000"
                  max="999999999"
                  oninput="if(value.length>5)value=value.slice(0,9)"
                  :error-messages="errors.first('mail.xin_coin')"
                  :label="$t('xin_coin') + '*'"
                  :data-vv-as="$t('xin_coin')"
                  data-vv-name="xin_coin"
                  data-vv-scope="mail"
                  filled
                  shaped
                  class="pa-0"
                  hide-spin-buttons
                />
              </v-card>
            </v-form>
          </v-row>
        </v-card-text>
        <v-card-actions class="pt-0 px-4 pb-4 px-sm-6 pb-sm-6">
          <template v-if="breakpoint.smAndUp">
            <v-spacer />
            <v-btn
              :disabled="!valid"
              color="primary"
              class="button-content--text"
              depressed
              @click="prepareSendHandler"
            >
              {{ $t('send_out').toUpperCase() }}
            </v-btn>
          </template>
          <template v-else>
            <v-btn
              :disabled="!valid"
              color="primary"
              class="button-content--text"
              depressed
              @click="prepareSendHandler"
              style="width: 100%"
            >
              {{ $t('send_out').toUpperCase() }}
            </v-btn>
          </template>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="showAttachmentDescriptionStatus"
      :fullscreen="breakpoint.xsOnly"
      width="460px"
      scrollable
      persistent
      transition="dialog-transition"
      :content-class="breakpoint.xsOnly ? '' : 'rounded-lg'"
    >
      <v-card id="attach-desc-card" height="560px" color="dialog-fill">
        <customDialogTitle
          :title="$t('appendix').toUpperCase() + ' ' + $t('description').toUpperCase()"
          @closeDialog="closeAttachmentDescriptionDialog"
        />
        <v-card-text
          :class="[
            'align-center rounded-0 dialog-fill pa-4 pa-sm-6',
            breakpoint.xsOnly ? 'scrollable-xs' : ' scrollable-sm'
          ]"
          flat
          tile
          elevation="0"
        >
          <span class="text-body-1 default-content--text text-body-1 custom-text-noto">
            {{ $t('conditions_noty') }}
          </span>
          <ol class="pl-4 pt-4">
            <li
              v-for="(item, index) in conditionDescritionData"
              :key="index"
              class="default-content--text pa-0"
              v-html="item"
            />
          </ol>
          <v-divider class="my-4" />
          <span class="text-body-1 primary--text custom-text-noto">
            {{ $t('matters_needing_attention') }}
          </span>

          <ol class="pl-4 pt-4">
            <li class="pa-0 primary--text" v-text="$t('send_mail_description6')" />
            <li class="pa-0 primary--text" v-text="$t('send_mail_description7')" />
            <li class="pa-0 primary--text" v-text="$t('send_mail_description9')" />
          </ol>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-dialog v-if="confirmSendStatus" v-model="confirmSendStatus" persistent max-width="380">
      <v-card color="transparent" class="pa-4 pa-sm-6">
        <v-card-title class="custom-text-noto text-h6 justify-center grey-1--text pa-0">
          {{ $t('reminder').toUpperCase() }}
        </v-card-title>
        <v-card-text class="px-0 py-6">
          <v-row no-gutters>
            <span class="custom-text-noto text-body-2 default-content--text">
              {{ '{0} {1}'.format($t('dialog_before_send_noty'), selectedFriend) }}
            </span></v-row
          >
          <v-row no-gutters class="custom-text-noto text-body-2 default-content--text">
            {{ '{0} {1}s'.format(formatPrice(parseInt(mailForm.xinCoin)), $t('xin_coin')) }}
          </v-row>
        </v-card-text>

        <v-card-actions class="pa-0">
          <v-row no-gutters justify="end">
            <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pr-2">
              <v-btn
                :class="['default-content--text', breakpoint.xsOnly ? 'w-100' : '']"
                text
                @click="confirmSendStatus = false"
              >
                {{ $t('cancel').toUpperCase() }}
              </v-btn></v-col
            >
            <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pl-2">
              <v-btn
                color="primary"
                :class="['button-content--text', breakpoint.xsOnly ? 'w-100' : '']"
                depressed
                :disabled="confirmSendBtnStatus"
                @click="sendMailHandler"
              >
                {{ $t('sure').toUpperCase() }}
              </v-btn></v-col
            >
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog
      v-if="confirmLeaveStatus"
      v-model="confirmLeaveStatus"
      persistent
      max-width="380"
      content-class="rounded-lg"
    >
      <v-card color="transparent" class="pa-4 pa-sm-6">
        <v-card-title class="custom-text-noto text-h6 justify-center grey-1--text pa-0">
          {{ $t('reminder') }}
        </v-card-title>
        <v-card-text class="custom-text-noto text-body-2 default-content--text px-0 py-6">
          {{ $t('leave_mail_description1') }}
        </v-card-text>

        <v-card-actions class="pa-0">
          <v-row no-gutters justify="end">
            <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pr-2"
              ><v-btn
                :class="['default-content--text', breakpoint.xsOnly ? 'w-100' : '']"
                text
                @click="confirmLeaveStatus = false"
              >
                {{ $t('cancel').toUpperCase() }}
              </v-btn></v-col
            >
            <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pl-2">
              <v-btn
                color="primary"
                :class="['button-content--text', breakpoint.xsOnly ? 'w-100' : '']"
                depressed
                @click="closeDialog"
              >
                {{ $t('sure').toUpperCase() }}
              </v-btn></v-col
            >
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
  import { isNullOrEmpty } from '@/utils/stringUtils'
  import relationship from '@/mixins/relationship.js'
  import scssLoader from '@/mixins/scssLoader.js'
  import cloneDeep from 'lodash/cloneDeep'
  import guildMgr from '~/mixins/guildMgr'
  import images from '~/mixins/images'
  import analytics from '~/mixins/analytics'
  export default {
    name: 'MailSendDialog',
    props: {
      showMailSendDialogStatus: { type: Boolean, default: false },
      mailDialogStatus: { type: Object, default: { type: Boolean, default: false } },
      recipient: { type: String, default: '' }
    },
    mixins: [relationship, images, scssLoader, guildMgr, analytics],
    components: {
      customDialogTitle: () => import('~/components/customDialogTitle')
    },
    data() {
      const mailForm = {
        recipient: '',
        mailTitle: this.$t('player_mail'),
        mailContent: '',
        xinCoin: ''
      }
      const conditionDescritionData = [
        '<p class="mb-0 text-wrap">' + this.$t('send_mail_description1') + '<br />' + '</p>',
        this.$t('send_mail_description2'),

        '<p class="mb-0 text-wrap">' +
          this.$t('send_mail_description4', {
            balance: '<span class="primary--text">' + '10,000' + '</span>',
            xin_coin: '<span class="primary--text">' + this.$t('xin_coin') + 's' + '</span>'
          }) +
          '</p>',
        this.$t('send_mail_description5', {
          beard_attachment: '<span class="primary--text">' + this.$t('beard_attachment') + '</span>'
        })
      ]

      return {
        valid: false,
        showMailSendDialogStatusTmp: this.showMailSendDialogStatus,
        mailForm,
        senderTmp: {},
        selectedFriend: '',
        showAttachmentDescriptionStatus: false,
        conditionDescritionData,
        confirmSendStatus: false,
        confirmLeaveStatus: false,
        defaultImg: process.env.IMAGE_URL + '/photo_stickers/default.png',
        searchInput: '',
        playerInfo: {},
        showNicknameWithFullname: false,

        recipientDisabledStatus: false,
        confirmSendBtnStatus: false,
        recipientStatus: true,
        generalFee: 0.02,
        recipientShowList: []
      }
    },
    computed: {
      maintainSystem() {
        return this.$store.getters['maintain/system']
      },
      qualificationNoty() {
        if (!this.hasGuild) return this.$t('mail_send_qualification_noty')
        const isGuildMaster = this.userName === this.guildMaster
        if (isGuildMaster) return this.$t('mail_send_qualification_noty2')
        return this.$t('mail_send_qualification_noty1')
      },
      friendList({ $store }) {
        const friendList = cloneDeep($store.getters['social/friendList'])
        return friendList
      },
      userName({ $store }) {
        return $store.getters['role/userName']
      },
      transactionFee({ $store }) {
        return $store.getters['role/transactionFee']
      },
      recipientList() {
        const createHeader = () => ({ header: this.$t('friend_list') })
        const createNoFriendNoty = () => ({ noty: true, text: this.$t('mail_send_text3') })
        const hasRecipients = Boolean(this.recipient?.length)
        const mergedMap = new Map()

        if (Array.isArray(this.guildMemberInfoList)) {
          this.guildMemberInfoList.forEach((guildMember) => {
            if (this.userName !== guildMember.name) {
              if (guildMember && guildMember.name) {
                const existingMember = mergedMap.get(guildMember.name)
                if (!existingMember) {
                  mergedMap.set(guildMember.name, {
                    username: guildMember.name,
                    thumbUrl: guildMember.avator,
                    online: guildMember.onlines === 1,
                    vipLevel: guildMember.VIPLevel
                  })
                }
              }
            }
          })
        }

        const userSendMemberList = Array.from(mergedMap.values())
        const hasFriends = Boolean(userSendMemberList?.length)
        userSendMemberList.sort((a, b) => {
          // 比較在線狀態和 VIP 等級
          if (a.online !== b.online) return b.online - a.online
          if (a.vipLevel !== b.vipLevel) return b.vipLevel - a.vipLevel

          // 比較用戶名
          const nameA = a.username,
            nameB = b.username
          const maxLength = Math.max(nameA.length, nameB.length)

          for (let i = 0; i < maxLength; i++) {
            if (i >= nameA.length) return -1
            if (i >= nameB.length) return 1

            const charA = nameA[i],
              charB = nameB[i]
            const typeA = /^\d$/.test(charA) ? 0 : /^[a-zA-Z]$/.test(charA) ? 1 : 2
            const typeB = /^\d$/.test(charB) ? 0 : /^[a-zA-Z]$/.test(charB) ? 1 : 2

            if (typeA !== typeB) return typeA - typeB
            if (charA !== charB) {
              return typeA === 0
                ? parseInt(charA) - parseInt(charB)
                : typeA === 1
                ? charA.localeCompare(charB)
                : charA.localeCompare(charB, 'zh-Hant')
            }
          }
          return 0
        })
        let list = []

        if (hasFriends) {
          list = [createHeader()]
          list.push(...userSendMemberList)
        } else if (
          !(hasRecipients && this.recipientStatus && this.isFriend(this.senderTmp.username))
        ) {
          list.push(createNoFriendNoty())
        }

        list.forEach((item) => {
          if ('online' in item || 'noty' in item) {
            item.disabled = item.noty === true
          }
        })

        return list
      },
      vipLevel({ $store }) {
        return $store.getters['role/vipLevel']
      },
      count() {
        return this.mailForm.mailContent.length
      },
      orientation({ $store }) {
        return $store.getters['deviceManagement/getOrientation']
      },
      isRecipientSelf() {
        return this.userName === this.searchInput
      },
      isLegacyIOS() {
        // 是否為IOS 16以前的IOS裝置
        const isIOS =
          (this.$device.isIos || this.$device.isMacOS) &&
          this.$device.userAgent.match(/Version\/(\d+\.\d+)/)?.[1] < 16
        return isIOS
      },
      handleCurrentMaxLen() {
        const totalMaxLength = 120
        // IOS:16 以前的裝置，在輸入換行時，maxlength會計算為2字元
        if (this.isLegacyIOS) {
          const currentContent = this.mailForm.mailContent || ''
          const lineBreakCount = (currentContent.match(/\r\n|\r|\n/g) || []).length
          // 每個換行多彌補一個字元
          return totalMaxLength + lineBreakCount
        }
        return totalMaxLength
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    watch: {
      showMailSendDialogStatus: {
        handler(status) {
          this.showMailSendDialogStatusTmp = status
          //如果是回信的情況 帶入收件人
          if (status === true) this.setSelectedFriendFromRecipient()
          else {
            this.resetStatus()
            this.resetForm()
          }
        },
        immediate: true
      },
      selectedFriend: {
        async handler() {
          this.valid = await this.validateForm()
        }
      },
      async 'mailForm.mailContent'() {
        this.valid = await this.validateForm()
      },
      async 'mailForm.xinCoin'() {
        // 防止輸入小數點及其他符號
        this.mailForm.xinCoin = this.mailForm.xinCoin.replace(/^(0+)|[^\d]+/g, '')
        this.$refs.xinCoinInput.lazyValue = this.mailForm.xinCoin
        this.valid = await this.validateForm()
      },
      searchInput(val) {
        //卡特殊符號與長度12
        if (val) {
          this.searchInput = val
            .replace(/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?\uFF01-\uFF5E\s]/g, '')
            .slice(0, 12)
          this.$refs.mailRecipient.lazySearch = this.searchInput
        }
      },
      orientation: {
        handler(orientation) {
          //避免手機轉向後選單跑版
          if (orientation !== 0 && this.$refs.mailRecipient) {
            this.$refs.mailRecipient.isMenuActive = false
          }
        },
        immediate: true
      },
      recipientList: {
        handler() {
          this.setRecipientShowList()
        }
      }
    },
    mounted() {
      if (this.guildRank !== 3) this.selectedFriend = this.guildMaster
      this.setRecipientShowList()
    },
    methods: {
      async getUserUrl(user) {
        if (!user.username) return
        const index = this.friendList.findIndex((item) => item.username === user.username)
        const userData = { userName: user.username }
        return index !== -1
          ? this.friendList[index].thumbUrl
          : await this.$store.dispatch('role/getThumbUrl', userData)
      },
      async getDailyMailLimit() {
        this.$wsClient.send(this.$wsPacketFactory.getMailCoinLimit())
        let res = { coinLimit: 0 }
        await this.$xinUtility
          .waitEvent(this.$wsClient.receivedListeners, (data) => {
            return data.isFeature(this.$xinConfig.FEATURE.MAIL.TYPE.COIN_LIMIT)
          })
          .then((response) => {
            res = response
          })
          .catch((err) => {
            console.log('COIN_LIMIT ERROR:', err)
          })
        return res.coinLimit
      },
      formatPrice(value) {
        return (value || 0).toLocaleString('en-US')
      },
      closeDialog() {
        this.resetStatus()
        this.$emit('update:showMailSendDialogStatus', false)
        this.$store.commit('mail/SET_SHOW_NOTY_STATUS', false)
        //關閉寄件彈窗，不要讓信件列表滾動到最上面
        this.$store.commit('mail/SET_SCROLL_TOP_STATUS', false)
        this.$nuxt.$emit('root:mailDialogStatus', { show: true, name: '' })
      },
      resetStatus() {
        this.confirmSendStatus = false
        this.confirmLeaveStatus = false
      },
      resetForm() {
        this.mailForm = {
          recipient: '',
          mailTitle: this.$t('player_mail'),
          mailContent: '',
          xinCoin: ''
        }
        this.selectedFriend = ''
        this.$validator.reset()
      },
      openAttachmentDescriptionDialog() {
        this.showAttachmentDescriptionStatus = true
      },
      closeAttachmentDescriptionDialog() {
        this.showAttachmentDescriptionStatus = false
        this.$emit('update:showMailSendDialogStatus', true)
      },
      showNotyDialog(title, message) {
        this.$store.dispatch('easyDialog/setDialog', {
          title: title,
          message: message
        })
        this.$nuxt.$emit('root:showNotyDialogStatus', true)
      },
      async prepareSendHandler() {
        // 平台維護檢查已由 layout 處理
        const validate = await this.$validator.validate('mail.*')
        if (validate) {
          const xinCoin = this.mailForm.xinCoin
          const afterBalance = this.$store.getters['role/balance'] - xinCoin
          const getMessage = () => {
            if (afterBalance < 30000)
              return this.$t('mail_send_noty1', {
                balance: '<span class="success--text font-weight-bold">30,000</span>'
              })
            if (!this.hasGuild) return this.$t(this.$UIConfig.mailIndex.sendNoty)
            return null
          }
          const message = getMessage()
          if (message)
            this.showNotyDialog(this.$t('reminder'), `<p class="mb-0 text-wrap">${message}</p>`)
          else this.confirmSendStatus = true
        }
      },
      async sendMailHandler() {
        if (!(await this.$validator.validate('mail.*'))) return
        const { mailTitle, mailContent, xinCoin } = this.mailForm
        const recipient = this.selectedFriend
        const afterBalance = this.$store.getters['role/balance'] - xinCoin
        if (afterBalance < 30000) {
          this.showNotyDialog(
            this.$t('reminder'),
            `<p class="mb-0 text-wrap">${this.$t('mail_send_noty1', {
              balance: '<span class="success--text font-weight-bold">30000</span>'
            })}</p>`
          )
          return
        }
        this.confirmSendBtnStatus = true
        try {
          const reqData = this.$wsPacketFactory.sendMail(recipient, mailTitle, mailContent, xinCoin)
          this.$wsClient.send(reqData)
          const res = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) =>
            data.isFeature(this.$xinConfig.FEATURE.MAIL.TYPE.SEND)
          )
          if (res.isSuccess) {
            this.$store.commit('role/SET_BALANCE', afterBalance)
            this.$refs.form.inputs.forEach((input) => !input.disabled && input.reset())
            this.$notify.success(this.$t('send_mail_success'))
            this.$emit('update:showMailSendDialogStatus', false)
            this.$store.commit('mail/SET_SHOW_NOTY_STATUS', false)
            if (this.recipient?.length) this.$store.commit('mail/SET_SCROLL_TOP_STATUS', false)
            const sender = await this.getGuildAnalyticsData(this.userName)
            const receiver = await this.getGuildAnalyticsData(this.selectedFriend)
            this.sendMailAnalytics(sender, receiver, parseInt(xinCoin), 1, 1)
            this.$nuxt.$emit('root:mailDialogStatus', { show: true, name: '' })
          } else this.$notify.error(res.message)
        } finally {
          this.$validator.reset('mail.*')
          this.confirmSendBtnStatus = false
          this.confirmSendStatus = false
        }
      },
      async validateForm() {
        const self = this
        return await self.$validator.validate('mail.*')
      },
      async setSelectedFriendFromRecipient() {
        const { recipient } = this
        if (recipient?.length != 0) {
          const isFriend = this.isFriend(recipient)
          const isSameGuild = this.checkIsSameGuild(recipient)
          this.recipientDisabledStatus = true
          this.setLoading(true)
          const role = await this.getPlayerData(recipient)
          this.recipientDisabledStatus = false
          this.setLoading(false)
          this.senderTmp = role
          if (isSameGuild) {
            this.selectedFriend = role.username
          } else if (isFriend) {
            this.selectedFriend = role.username
          }
        }
      },
      clearRecipientField() {
        this.selectedFriend = ''
        this.searchInput = ''
        this.$refs.mailRecipient.isMenuActive = true
      },
      closeMenu() {
        this.$refs.playerInfoMenu.isActive = false
      },
      async setPlayerInfo(name) {
        let role = await this.getPlayerData(name)
        this.playerInfo = role
      },
      selectItem(item) {
        this.selectedFriend = item
        this.$refs.mailRecipient.isMenuActive = false
      },
      setLoading(status) {
        if (status) this.$nuxt.$loading.start()
        else this.$nuxt.$loading.finish()
      },
      isFriend(name) {
        return this.friendList.filter((item) => item.username == name).length > 0
      },
      isInGuildMemberList(name) {
        return this.guildMemberInfoList.findIndex((item) => item.username == name) > -1
      },
      errorImgHandler(item) {
        item.thumbUrl = this.defaultImg
      },

      stringNullOrEmpty(word) {
        return isNullOrEmpty(word)
      },
      async setRecipientShowList() {
        this.recipientShowList = cloneDeep(this.recipientList)
        for (var item of this.recipientShowList) {
          const avatarThumb = await this.getUserUrl(item)
          if (avatarThumb) item.thumbUrl = avatarThumb
        }
        this.$forceUpdate()
      }
    },
    beforeDestroy() {
      this.$store.commit('mail/SET_OPEN_SEND_MAIL_DIRECTLY', false)
    }
  }
</script>
<style lang="scss" scoped>
  $card-fill: map-get($colors, 'card-fill');
  .v-list-conditionDescritionData {
    min-height: 24px;
  }

  #recipient-chip::v-deep {
    button {
      color: map-get($colors, grey-3);
    }
  }
  .v-list {
    background-color: $card-fill !important;
  }
  #attach-desc-card {
    max-height: 90vh;
    .scrollable-sm {
      max-height: calc(90vh - 52px);
      overflow-y: auto;
    }
    .scrollable-xs {
      max-height: calc(100vh - 52px);
      overflow-y: auto;
    }
  }
  @supports (height: 90svh) {
    #attach-desc-card {
      max-height: 90svh;
    }
    #attach-desc-card .scrollable-sm {
      max-height: calc(90svh - 52px);
    }
    #attach-desc-card .scrollable-xs {
      max-height: calc(100svh - 52px);
    }
  }
</style>
<!-- 此區不加scoped -->
<style lang="scss">
  $dialog-fill: map-get($colors, 'dialog-fill');
  .sendMailMenu {
    .v-badge__badge::after {
      border-color: $dialog-fill !important;
    }
  }
  .custom-autocomplete {
    .v-input__control {
      .v-input__slot {
        .v-select__slot {
          .v-input__append-inner {
            margin-top: 25px !important;
            .v-icon--disabled {
              display: none;
            }
          }
        }
      }
    }
  }
</style>
