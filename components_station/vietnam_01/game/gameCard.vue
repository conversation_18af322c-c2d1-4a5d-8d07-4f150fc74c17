<template>
  <v-hover v-slot="{ hover: outerHover }">
    <v-card color="transparent" elevation="0" :class="vCardMarginX" ref="gameCard">
      <v-hover>
        <template v-slot:default="{ hover }">
          <v-img
            :lazy-src="gameDefaultJpg"
            :src="gameThumbUrl"
            aspect-ratio="1"
            :placeholder="gameDefaultJpg"
            @error="setAltImg()"
            style="
              border-top-left-radius: 12px;
              border-top-right-radius: 12px;
              border-bottom-right-radius: 12px;
            "
          >
            <div v-if="isGameBeta" class="beta-show">
              <v-card
                class="d-flex justify-center align-center"
                color="gradient-primary-reverse"
                width="60px"
                height="32px"
              >
                <span class="default-content--text text-sm-body-2"> BETA </span>
              </v-card>
            </div>

            <div
              v-if="isGameMaintaining"
              class="d-flex v-card--reveal gradient-game-maintenance h-100-percent"
            >
              <span class="material-symbols-outlined default-content--text" style="font-size: 48px">
                construction
              </span>
            </div>
            <v-fade-transition>
              <v-overlay
                v-if="hover && !isGameMaintaining"
                absolute
                opacity="0.8"
                color="game-hover"
              >
                <v-container>
                  <template>
                    <v-row no-gutters>
                      <v-col v-if="isLogin">
                        <!-- startPlay -->
                        <v-tooltip top v-model="showVipLevelLimitText" :open-on-hover="false">
                          <template v-slot:activator="{ on }">
                            <v-btn
                              v-if="isVipLevelLimit"
                              class="button-content--text"
                              :small="$vuetify.breakpoint.xsOnly"
                              :color="$UIConfig.defaultBtnColor"
                              depressed
                              min-width="93px"
                              :width="$vuetify.breakpoint.xsOnly ? '100%' : '150px'"
                              disabled
                              v-on="isVipLevelLimit ? on : {}"
                            >
                              <v-icon left> mdi-lock-outline </v-icon>
                              {{ $t('startsPlay') }}
                            </v-btn>
                          </template>
                          <span>{{ vipLevelLimitText }}</span>
                        </v-tooltip>
                        <v-btn
                          v-if="!isVipLevelLimit"
                          class="button-content--text"
                          :small="$vuetify.breakpoint.xsOnly"
                          :color="$UIConfig.defaultBtnColor"
                          depressed
                          min-width="93px"
                          :width="$vuetify.breakpoint.xsOnly ? '100%' : '150px'"
                          @click="startGameWithClick('play')"
                          :disabled="disabledStatus || !isGamePlayable"
                        >
                          {{ $t('startsPlay') }}
                        </v-btn>
                      </v-col>
                      <v-col v-else-if="!hasDemo">
                        <v-btn
                          class="button-content--text"
                          :small="$vuetify.breakpoint.xsOnly"
                          :color="$UIConfig.defaultBtnColor"
                          depressed
                          min-width="93px"
                          :width="$vuetify.breakpoint.xsOnly ? '100%' : '150px'"
                          @click="loginClickHandler"
                          :disabled="disabledStatus"
                        >
                          {{ $t('startsPlay') }}
                        </v-btn>
                      </v-col>
                    </v-row>
                    <!-- freePlay -->
                    <v-row v-if="hasDemo" no-gutters class="pt-2">
                      <v-col>
                        <v-btn
                          class="default-content--text"
                          :small="$vuetify.breakpoint.xsOnly"
                          outlined
                          color="white"
                          min-width="93px"
                          :width="$vuetify.breakpoint.xsOnly ? '100%' : '150px'"
                          @click="startGameWithClick('demo')"
                          :disabled="disabledStatus || !isGamePlayable"
                        >
                          {{ $t('freePlay') }}
                        </v-btn>
                      </v-col>
                    </v-row>
                  </template>
                </v-container>
              </v-overlay>
            </v-fade-transition>
          </v-img>
        </template>
      </v-hover>
      <v-card
        color="transparent"
        elevation="0"
        class="default-content--text custom-text-noto text-body-2"
      >
        <v-container fluid class="px-0 pt-2 pb-4">
          <div class="d-flex justify-space-between">
            <!-- 遊戲名稱 -->
            <div class="pr-2">
              <span class="game-name-type game-name-layout text-start">
                {{ game.name }}
              </span>
            </div>
            <template>
              <span
                :class="['cursor-pointer', 'material-symbols-outlined']"
                class="grey-3--text material-icons md-20"
                @click="openGameIntroDialog()"
              >
                info
              </span>
            </template>
          </div>
          <v-expand-transition>
            <v-row v-if="shouldShowRtp" no-gutters class="pt-2 align-center">
              <div class="rtpShow">
                <rtpShow
                  :background-color="showRTPStyle.dailyBackgroundColor"
                  :rtp="gameRtp ? gameRtp.dailyRtp : null"
                  :icon="showRTPStyle.dailyIcon"
                  icon-color="white--text"
                  chip-text-color="white"
                  :is-outer-hover="outerHover"
                />
              </div>
            </v-row>
          </v-expand-transition>
        </v-container>
      </v-card>
      <notyNotRealMember
        v-if="showNotyNotRealMemberDialogStatus"
        :show-noty-not-real-member-dialog-status.sync="showNotyNotRealMemberDialogStatus"
      />
      <bothRobotExpNoty
        v-if="showNotyBothRobotExpNotyDialogStatus.show"
        :show-noty-both-robot-exp-noty-dialog-status.sync="showNotyBothRobotExpNotyDialogStatus"
      />
      <noExpGainNoty
        v-else-if="showNotyNoExpGainNotyDialogStatus.show"
        :show-noty-no-exp-gain-noty-dialog-status.sync="showNotyNoExpGainNotyDialogStatus"
      />
      <hasRobotNoty
        v-else-if="showNotyHasRobotNotyDialogStatus.show"
        :show-noty-has-robot-noty-dialog-status.sync="showNotyHasRobotNotyDialogStatus"
      />
      <gameIntro
        v-if="showGameIntroDialogStatus"
        :show-game-intro-dialog-status.sync="showGameIntroDialogStatus"
        :game="game"
      />
    </v-card>
  </v-hover>
</template>

<script>
  import analytics from '@/mixins/analytics.js'
  import preLoginAction from '@/mixins/preLoginAction.js'
  import gameStatus from '@/mixins/gameStatus.js'
  import gameRtpWatcher from '@/mixins/gameRtpWatcher.js'
  const STATION = process.env.STATION
  const playGame = require(`~/mixins_station/${STATION}/playGame`).default
  export default {
    name: 'gameCard',
    mixins: [analytics, playGame, preLoginAction, gameStatus, gameRtpWatcher],
    components: {
      rtpShow: () => import(`~/components_station/${STATION}/rtp/rtpShow`),
      notyNotRealMember: () => import('~/components/notifications/notyNotRealMember.vue'),
      bothRobotExpNoty: () => import('~/components/notifications/bothRobotExpNoty.vue'),
      noExpGainNoty: () => import('~/components/notifications/noExpGainNoty.vue'),
      hasRobotNoty: () => import('~/components/notifications/hasRobotNoty.vue'),
      gameIntro: () => import(`~/components_station/${STATION}/game/gameIntro.vue`)
    },
    props: {
      game: {
        type: Object,
        default: () => {}
      },
      vCardMarginX: {
        type: String,
        default: 'mx-2'
      }
    },
    data() {
      return {
        gameDefault: this.$store.getters['gameHall/gameDefaultImg'],
        disabledStatus: false,
        showNotyNotRealMemberDialogStatus: false,
        gameDefaultJpg: this.$store.getters['gameHall/gameDefaultImgJpg'],
        showGameIntroDialogStatus: false,
        fallbackThumbUrl: null // 記錄失敗時的替代圖片
      }
    },
    computed: {
      // 動態計算遊戲縮圖 URL
      gameThumbUrl() {
        // 如果有設定替代圖片，使用替代圖片
        if (this.fallbackThumbUrl) {
          return this.fallbackThumbUrl
        }
        // 否則使用原始圖片
        return this.game.thumbUrl || this.gameDefaultJpg
      },
      isLogin({ $store }) {
        return $store.getters['role/isLogin']
      },
      openGameLock({ $store }) {
        return $store.getters['gameHall/openGameLock']
      },
      vipLevelTitle({ $store }) {
        return $store.getters['role/vipLevelTitle']
      },

      brandName({ $store }) {
        const providers = $store.getters['gameProvider/providers']
        const showProviderBrandName = this.$UIConfig.lock.showProviderBrandName
        const providerBrandName = providers.find((item) => {
          return item.id === this.game.platformId
        })
        return providerBrandName !== undefined && showProviderBrandName
          ? providerBrandName.fullname
          : ''
      },
      // "青銅"玩家特別限制，game.vipLevel為1時限制LV10(含)以上才可遊玩，為99表示LV1即可遊玩
      isVipLevelLimit() {
        const gameVipLevel = this.game.vipLevel
        // 越南站特殊處理：vipLevel為-1的遊戲允許所有玩家遊玩
        if (gameVipLevel === -1) return false
        // 未登入不顯示限制
        if (this.vipLevel === 0) return false
        // 青銅等級1的遊戲：需要玩家等級達到10級
        if (gameVipLevel === 1 && this.level < 10) return true
        // 一般VIP等級限制：玩家VIP等級需達到遊戲要求（99為特殊無限制等級）
        if (gameVipLevel !== 99 && this.vipLevel < gameVipLevel) return true

        return false
      },

      // 優化長條件判斷,增加可讀性
      gameVipLevelText() {
        return this.game.vipLevel === 1 && this.level < 10
          ? 'LV10'
          : this.$t(this.vipLevelTitle[this.game.vipLevel])
      },
      // 抽離重複使用的文字
      brandNameDisplay() {
        return this.brandName.toUpperCase()
      },
      vipLevelLimitText() {
        if (this.gameVipLevelText === 'LV10') {
          return this.$t('game_level_limit_reached', { vipLevelText: this.gameVipLevelText })
        } else {
          return this.$t('game_rank_limit_reached', { vipLevelText: this.gameVipLevelText })
        }
      },
      showVipLevelLimitText() {
        return this.isVipLevelLimit && this.isLogin
      },

      shouldShowRtp() {
        // 使用 gameStatus mixin 的 isLive computed property
        if (this.isLive) {
          return false
        }

        // 檢查是否有 RTP 資料
        const hasRtpData = this.hasRtpData(this.game.id, this.game)
        const showRtp =
          !this.isGameMaintaining && this.hasRtp && this.showRTPStyle.dailyShowStatus && hasRtpData

        return showRtp
      }
    },
    watch: {
      openGameLock: {
        handler(val) {
          this.disabledStatus = val
        }
      }
    },
    methods: {
      async setAltImg() {
        const currentUrl = this.game.thumbUrl || ''
        if (currentUrl.includes('default')) {
          return
        }

        // 嘗試使用 jpg 替代 webp
        const jpgUrl = currentUrl.replace('.webp', '.jpg')

        try {
          const imageResponse = await fetch(jpgUrl)
          if (imageResponse.ok) {
            this.fallbackThumbUrl = jpgUrl
          } else {
            this.fallbackThumbUrl = this.gameDefaultJpg
          }
        } catch (error) {
          this.fallbackThumbUrl = this.gameDefaultJpg
        }
      },
      async loginClickHandler() {
        this.startGameWithClick('play')
        this.setPreLoginAction('playGame', () => this.startGameWithClick('play'))
      },
      openGameIntroDialog() {
        this.showGameIntroDialogStatus = true
      }
    }
  }
</script>

<style lang="scss" scoped>
  .beta-show {
    position: absolute;
    top: 0px;
    right: 0px;
    z-index: 4;
  }

  .vipLimit-show {
    position: absolute;
    top: 8px;
    left: 8px;
    z-index: 4;
  }

  .vipLimit-chip {
    padding-top: 2px;
    padding-bottom: 2px;
  }

  .tag-icon {
    font-size: 14px;
    margin-right: 2px;
  }

  .rtpShow {
    width: 92px;
  }

  .no-exp-notice {
    background: rgba(97, 97, 97, 0.9);
  }

  .game-level-show {
    max-width: 90%;
  }
  .game-level-text {
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .trade-text-type {
    text-align: center;

    /* Customize/caption */
    font-family: 'Montserrat';
    font-size: 10px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    text-transform: uppercase;
  }

  .game-name-layout {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    align-self: stretch;
  }

  .game-name-type {
    overflow: hidden;
    text-overflow: ellipsis;

    /* Body/body-2 */
    font-family: 'Noto Sans TC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 142.857% */
  }
</style>
