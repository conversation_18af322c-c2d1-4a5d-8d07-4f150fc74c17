<template>
  <v-container class="pa-0 pb-6">
    <v-row no-gutters class="align-center justify-center">
      <v-col>
        <v-card color="transparent" elevation="0">
          <div
            class="pb-4 text-center gradient-title--text custom-text-noto text-h4 font-weight-bold"
          >
            {{ $t('xincity') + $t('download') }}
          </div>
          <v-card-text class="py-0">
            <p class="default-content--text custom-text-noto text-md-body-1">
              {{ $t('downloads_description') }}
            </p>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    <template v-if="$vuetify.breakpoint.xsOnly">
      <v-row no-gutters align="center">
        <v-col
          v-for="(appDownload, index) in appDownloadList"
          :key="index"
          :cols="appDownload.name == 'android' ? '7' : '5'"
        >
          <v-row no-gutters justify="center" align="center" class="mb-2">
            <v-img
              contain
              class="cursor-pointer"
              height="50px"
              min-width="175px"
              :src="appDownloadPath(appDownload.img)"
              @click="goTo(appDownload.name, appDownload.url, appDownload.target)"
            />
          </v-row>
        </v-col>
      </v-row>
    </template>
    <template v-else>
      <v-row no-gutters justify="center" align="center">
        <div v-for="(appDownload, index) in appDownloadList" :key="index">
          <v-img
            contain
            class="cursor-pointer"
            :src="appDownloadPath(appDownload.img)"
            height="50px"
            width="175px"
            @click="goTo(appDownload.name, appDownload.url, appDownload.target)"
          />
        </div>
      </v-row>
    </template>
  </v-container>
</template>

<script>
  import analytics from '@/mixins/analytics.js'
  import appDownload from '@/mixins_station/vietnam_01/appDownload.js'
  import images from '~/mixins/images'
  export default {
    name: 'IndexAppDownload',
    mixins: [analytics, appDownload, images],
    data() {
      const appDownloadList = [
        {
          img: 'app_store_download.svg',
          name: 'IOS',
          url: 'https://www.xin-stars.com/goStore',
          target: '_blank'
        },
        {
          img: '/google_play_download.svg',
          name: 'android',
          url: 'https://www.xin-stars.com/goStore',
          target: '_blank'
        },
        {
          img: 'windows_download.svg',
          name: 'windows',
          url: 'https://vtco.esgame.vn/?news=https://vtco.esgame.vn/a/huong-dan-tai-game-ban-pc',
          target: '_blank'
        }
      ]
      return {
        appDownloadList,
        cover: process.env.IMAGE_URL + '/app_download/xinstar_download_all.webp'
      }
    },
    created() {
      //在data inital的時候是無法取得this.$device.isDesktop的值，所以要在這邊重新賦值，且在data的特性為一次性賦值
      this.appDownloadList.forEach((item) => {
        if (item.name === 'android') item.url = this.androidUrl
        if (item.name === 'IOS') item.url = this.iosUrl
      })
    },
    computed: {
      imageUrlPath({ $store }) {
        return $store.getters['image/imageUrlPath']
      }
    },
    methods: {
      goTo(name, link, target) {
        let label = ''
        if (target === '_router') {
          this.$router.push(this.localePath(link))
        } else {
          this.$lineOpenWindow.open(link, target)
        }
        if (name !== 'windows') {
          switch (name) {
            case 'IOS':
              label = 'dlhdios'
              break
            case 'android':
              label = 'dlhdapk'
              break
            default:
              break
          }
          this.downloadClickAnalytics(label)
        }
      },
      appDownloadPath(image) {
        return this.getImage('downloads/' + this.$i18n.locale + '/' + image)
      }
    }
  }
</script>
