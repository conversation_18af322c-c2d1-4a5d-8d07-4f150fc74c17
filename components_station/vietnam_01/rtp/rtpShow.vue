<template>
  <v-chip
    class="rtp-chip px-2 ma-0 w-100 custom-text-noto text-body-2"
    :color="backgroundColor"
    small
    label
    :text-color="chipTextColor"
  >
    <p class="w-100 mb-0 d-flex align-center" :class="justify">
      <span class="rtp-dot mr-2" :class="[dotColorClass]"></span>
      <span class="custom-text-noto text-caption pr-1">
        <template v-if="text"> {{ $t(text) }} </template>
        {{ rtp }}%
      </span>
    </p>
  </v-chip>
</template>
<script>
  export default {
    name: 'Rtpshow',
    props: {
      text: {
        type: String,
        default: ''
      },
      backgroundColor: {
        type: String,
        required: true
      },
      rtp: {
        type: Number,
        default: 0,
        required: true
      },
      chipTextColor: {
        type: String,
        required: true
      },
      justify: {
        type: String,
        default: 'justify-center'
      },
      dotColor: {
        type: String,
        default: 'primary-variant-1'
      }
    },
    computed: {
      dotColorClass() {
        return `${this.dotColor}-dot`
      }
    }
  }
</script>
<style lang="scss" scoped>
  $primary-variant-1: map-get($colors, 'primary-variant-1');
  $primary-variant-1-opacity: rgba($primary-variant-1, 0.3);
  $primary-variant-3: map-get($colors, 'primary-variant-3');
  $primary-variant-3-opacity: rgba($primary-variant-3, 0.3);
  $grey-1: map-get($colors, 'grey-1');
  $grey-1-opacity: rgba($grey-1, 0.3);
  .rtp-chip {
    display: flex;
    align-content: center;
    justify-content: center;
    .v-chip__content {
      width: 100%;
    }
  }
  /* 使用CSS變量定義顏色 */
  .rtp-dot {
    --dot-color: #ff8c42; /* 預設顏色 */
    --pulse-color: rgba(255, 140, 66, 0.3); /* 預設擴散顏色 */

    position: relative;
    width: 8px;
    height: 8px;
    background-color: var(--dot-color);
    border-radius: 50%;
  }

  /* 外圈擴散動畫 */
  .rtp-dot::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: var(--pulse-color);
    border-radius: 50%;
    top: 0;
    left: 0;
    animation: pulse 1.5s infinite ease-in-out;
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
      opacity: 0.8;
    }
    50% {
      transform: scale(3);
      opacity: 0;
    }
    100% {
      transform: scale(1);
      opacity: 0;
    }
  }

  /* 不同顏色示例 */
  .primary-variant-1-dot {
    --dot-color: #{$primary-variant-1};
    --pulse-color: #{$primary-variant-1-opacity};
  }

  .primary-variant-3-dot {
    --dot-color: #{$primary-variant-3};
    --pulse-color: #{$primary-variant-3-opacity};
  }

  .grey-1-dot {
    --dot-color: #{$grey-1};
    --pulse-color: #{$grey-1-opacity};
  }
</style>
