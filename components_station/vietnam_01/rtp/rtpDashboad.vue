<template>
  <div class="d-flex">
    <v-container class="noscroll pa-0">
      <v-row v-if="maintainText" no-gutters align="center">
        <v-col cols="12">
          <v-card color="grey-6" elevation="0" class="d-flex justify-center">
            <span class="grey-3--text custom-text-noto text-caption">
              {{ $t('in_maintenance') }}
            </span>
          </v-card>
        </v-col>
      </v-row>
      <v-row v-else-if="!rtpStyleObj.showRTPTooltipStatus" no-gutters align="center">
        <v-col cols="12">
          <v-card color="grey-6" elevation="0" class="d-flex justify-center">
            <span class="grey-3--text custom-text-noto text-caption">
              {{ $t('rtp_show_comming_soon') }}
            </span>
          </v-card>
        </v-col>
      </v-row>
      <v-row class="rtpShowBackground" v-else no-gutters align="center">
        <v-col>
          <v-card color="transparent" elevation="0" class="d-flex">
            <rtpShowVertical
              text="single_daily"
              :rtp="dailyRtp"
              :dot-color="'primary-variant-1'"
              chip-text-color="default-content"
            />
          </v-card>
        </v-col>
        <div class="vertical-divider"></div>
        <v-col>
          <v-card color="transparent" elevation="0" class="d-flex">
            <rtpShowVertical
              text="single_weekly"
              :rtp="weeklyRtp"
              :dot-color="'primary-variant-3'"
              chip-text-color="default-content"
            />
          </v-card>
        </v-col>
        <div class="vertical-divider"></div>
        <v-col>
          <v-card color="transparent" class="grey-7 d-flex" elevation="0">
            <rtpShowVertical
              text="single_monthly"
              :rtp="monthlyRtp"
              :dot-color="'grey-1'"
              chip-text-color="default-content"
            />
          </v-card>
        </v-col>
      </v-row>
      <v-row class="mt-1" no-gutters>
        <v-col cols="12">
          <span
            v-if="!rtpStyleObj.showRTPTooltipStatus || maintainText"
            class="custom-text-noto text-caption default-content--text"
            >{{ $t('expected_value_explanation_noty1') }}
          </span>
          <span v-else class="custom-text-noto text-caption default-content--text">{{
            $t('expected_value_explanation_noty1') + ' ' + $t('expected_value_explanation_noty2')
          }}</span>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>
<script>
  const STATION = process.env.STATION
  export default {
    name: 'RtpDashBoad',
    components: {
      rtpShowVertical: () => import(`~/components_station/${STATION}/rtp/rtpShowVertical`)
    },
    props: {
      dailyRtp: {
        type: Number,
        default: () => {}
      },
      weeklyRtp: {
        type: Number,
        default: () => {}
      },
      monthlyRtp: {
        type: Number,
        default: () => {}
      },
      rtpStyleObj: {
        type: Object,
        default: () => {}
      },
      defaultRtp: {
        type: Number,
        default: 0
      },
      iconColor: {
        type: String,
        default: 'grey-3--text'
      },
      noExperienceText: {
        type: Boolean,
        default: false
      },
      hasRobotText: {
        type: Boolean,
        default: true
      },
      maintainText: {
        type: Boolean,
        default: false
      }
    }
  }
</script>
<style lang="scss" scoped>
  .rtpShowBackground {
    background: rgba(0, 0, 0, 0.2);
  }
  .vertical-divider {
    width: 1px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.08);
    margin: 0;
  }
</style>
