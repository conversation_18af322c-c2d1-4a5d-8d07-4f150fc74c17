<template>
  <div>
    <v-container fluid class="pa-4 pa-sm-6" id="coffer-container">
      <!-- 存入星幣 文字 -->
      <v-row no-gutters class="pb-4 align-center">
        <v-col class="default-content--text">
          {{ $UIConfig.coffer.saveStarCoin.title.format($t('store'), $t('xin_coin')) }}
        </v-col>
      </v-row>
      <!-- 存入星幣區塊 輸入框 -->
      <v-row no-gutters class="align-center justify-left">
        <!-- 星幣餘額 -->
        <v-col cols="12" sm="6" md="5" lg="5" class="pr-sm-2">
          <v-text-field
            v-model="selfBalance"
            disabled
            :label="$UIConfig.coffer.saveStarCoin.balance.format($t('xin_coin'), $t('balance'))"
            filled
            shaped
          />
        </v-col>
        <!-- 存入星幣 -->
        <v-col cols="12" sm="6" md="7" lg="7" class="pl-sm-2">
          <v-text-field
            v-model="storePoints"
            ref="storePoints"
            :label="$UIConfig.coffer.saveStarCoin.input.format($t('store'), $t('xin_coin'))"
            name="storePoints"
            :error-messages="errors.first('coffer.storePoints')"
            v-validate="{
              coin_min: 1,
              is_number: true,
              insufficient_store_points: selfBalance
            }"
            data-vv-scope="coffer"
            filled
            shaped
            clearable
            :placeholder="$t('store_xin_coin_msg')"
            @input="validateStorePoints"
            :disabled="(withdrawPoints !== null && withdrawPoints !== '') || loading"
          >
            <template v-slot:append-outer>
              <v-btn
                outlined
                color="primary"
                class="ml-2"
                :disabled="
                  (withdrawPoints !== null && withdrawPoints !== '') || selfBalance === 0 || loading
                "
                @click="storeAll"
              >
                MAX
              </v-btn>
            </template>
          </v-text-field>
        </v-col>
      </v-row>
      <v-divider />
      <!-- 提領星幣 文字 -->
      <v-row no-gutters class="py-4 align-center">
        <v-col class="default-content--text">
          {{ $UIConfig.coffer.withdrawCoin.title.format($t('withdraw'), $t('xin_coin')) }}
        </v-col>
      </v-row>
      <!-- 提領星幣區塊 輸入框 -->
      <v-row no-gutters class="align-center justify-left">
        <!-- 已存星幣 -->
        <v-col cols="12" sm="6" md="5" lg="5" class="pr-sm-2">
          <v-text-field
            v-model="selfSafe"
            disabled
            :label="
              $UIConfig.coffer.withdrawCoin.balance.format($t('already_saved'), $t('xin_coin'))
            "
            filled
            shaped
          />
        </v-col>
        <!-- 提領星幣 -->
        <v-col cols="12" sm="6" md="7" lg="7" class="pl-sm-2">
          <v-text-field
            v-model="withdrawPoints"
            ref="withdrawPoints"
            name="withdrawPoints"
            :error-messages="errors.first('coffer.withdrawPoints')"
            v-validate="{
              coin_min: 1,
              is_number: true,
              insufficient_withdraw_points: selfSafe
            }"
            data-vv-scope="coffer"
            :label="$UIConfig.coffer.withdrawCoin.input.format($t('withdraw'), $t('xin_coin'))"
            filled
            shaped
            clearable
            :placeholder="$t('withdraw_xin_coin_msg')"
            @input="validateWithdrawPoints"
            :disabled="(storePoints !== null && storePoints !== '') || loading"
          >
            <template v-slot:append-outer>
              <v-btn
                outlined
                color="primary"
                class="ml-2"
                :disabled="
                  (storePoints !== null && storePoints !== '') || selfSafe === 0 || loading
                "
                @click="withdrawAll"
              >
                MAX
              </v-btn>
            </template>
          </v-text-field>
        </v-col>
      </v-row>
      <!-- 確定按鈕 -->
      <v-row no-gutters>
        <v-col>
          <v-card color="transparent" elevation="0">
            <v-card-actions class="px-0 pb-0 pt-2">
              <v-spacer />

              <v-btn
                :class="['button-content--text', breakpoint.xsOnly ? 'w-100' : '']"
                :color="$UIConfig.defaultBtnColor"
                depressed
                :loading="!useCofferStatus"
                :disabled="cofferBtnDisabled || loading || !useCofferStatus"
                @click="doSure"
              >
                {{ $t('sure').toUpperCase() }}
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script>
  import payment from '@/mixins/payment.js'
  const STATION = process.env.STATION
  export default {
    name: 'coffer',
    mixins: [payment],
    data() {
      return {
        //存入多少星幣
        storePoints: null,
        //提領多少星幣
        withdrawPoints: null,
        cofferBtnDisabled: true,
        loading: false,
        //由於後端回傳3秒後才能使用保險箱，故先讓按鈕轉圈圈，實際上前端要等5秒後才能使用....
        useCofferStatus: false
      }
    },
    async mounted() {
      await this.bankInit()
      // 金流服務連線後，5秒後才能使用保險箱
      setTimeout(() => {
        this.useCofferStatus = true
      }, 5000)

      this.validateAll()
    },
    computed: {
      selfBalance({ $store }) {
        return Number($store.getters['role/balance'])
      },
      selfSafe({ $store }) {
        return Number($store.getters['role/safe'])
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    watch: {
      storePoints: {
        handler() {
          //畫面渲染完再做驗證
          this.$nextTick(() => {
            this.validateAll()
          })
        }
      },
      withdrawPoints: {
        handler() {
          //畫面渲染完再做驗證
          this.$nextTick(() => {
            this.validateAll()
          })
        }
      }
    },

    methods: {
      //全部存入
      storeAll() {
        this.withdrawPoints = null
        this.storePoints = this.selfBalance
      },
      //全部提領
      withdrawAll() {
        this.storePoints = null
        this.withdrawPoints = this.selfSafe
      },
      validateStorePoints() {
        if (this.storePoints === null) {
          return
        }
        this.storePoints = this.storePoints.replace(/[^0-9]/g, '')
        while (this.storePoints[0] === '0') {
          this.storePoints = this.storePoints.slice(1)
        }
        if (this.storePoints.length > 10) {
          this.storePoints = this.storePoints.slice(0, -1)
        }
        this.$refs.storePoints.lazyValue = this.storePoints
        this.validateAll()
      },
      validateWithdrawPoints() {
        if (this.withdrawPoints === null) {
          return
        }
        this.withdrawPoints = this.withdrawPoints.replace(/[^0-9]/g, '')
        while (this.withdrawPoints[0] === '0') {
          this.withdrawPoints = this.withdrawPoints.slice(1)
        }
        if (this.withdrawPoints.length > 10) {
          this.withdrawPoints = this.withdrawPoints.slice(0, -1)
        }
        this.$refs.withdrawPoints.lazyValue = this.withdrawPoints
        this.validateAll()
      },
      validateAll() {
        this.$validator.validate('coffer.*').then((valid) => {
          if (
            valid &&
            ((this.storePoints !== null && this.storePoints !== '') ||
              (this.withdrawPoints !== null && this.withdrawPoints !== ''))
          ) {
            this.cofferBtnDisabled = false
          } else {
            this.cofferBtnDisabled = true
          }
        })
      },
      //確認
      async doSure() {
        this.loading = true
        this.$emit('setDisableBtn', this.loading)
        //此次不是VIP購點
        this.$store.commit(`${STATION}/payment/SET_VIP_TOP_UP`, false)
        let res = ''
        //存入
        if (this.storePoints !== null && this.storePoints !== '') {
          this.$wsClient.send(this.$wsPacketFactory.depositSafeBox(this.storePoints))
          try {
            res = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
              return data.isFeature(this.$xinConfig.FEATURE.BANK.TYPE.DEPOSIT_SAFE_BOX)
            })
          } catch (error) {
            this.$notify.error(this.$t('coffer_error_msg'))
            console.log(error)
          }
          if (res) {
            this.$store.commit('role/SET_BALANCE', res.money)
            this.$store.commit('role/SET_SAFE', res.safe)
            this.withdrawPoints = null
            this.storePoints = null
            this.$notify.success(
              this.$t('toatleCoffer') +
                ' ' +
                this.formatNumber(res.safe) +
                ' ' +
                this.$t('xin_coin')
            )
          }
        }
        // 取出
        else {
          this.$wsClient.send(this.$wsPacketFactory.withdrawSafeBox(this.withdrawPoints))
          try {
            res = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
              return data.isFeature(this.$xinConfig.FEATURE.BANK.TYPE.USE_SAFE_BOX)
            })
          } catch (error) {
            this.$notify.error(this.$t('coffer_error_msg'))
            console.log(error)
          }
          if (res) {
            this.$store.commit('role/SET_BALANCE', res.money)
            this.$store.commit('role/SET_SAFE', res.safe)
            this.withdrawPoints = null
            this.storePoints = null
            this.$notify.success(
              this.$t('toatleCoffer') +
                ' ' +
                this.formatNumber(res.safe) +
                ' ' +
                this.$t('xin_coin')
            )
          }
        }
        this.loading = false
        this.$emit('setDisableBtn', this.loading)
      },
      //千分位
      formatNumber(num) {
        if (num === undefined) return
        return num.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
      }
    }
  }
</script>

<style lang="scss">
  .append-outer-text {
    width: 32px;
  }
  #coffer-container {
    .v-input__append-outer {
      margin-left: 8px !important;
    }
  }
</style>
