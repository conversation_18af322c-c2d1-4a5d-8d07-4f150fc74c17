<template>
  <div>
    <v-dialog
      v-model="showReportDialogStatustemp"
      persistent
      max-width="457px"
      :fullscreen="breakpoint.xsOnly"
      :content-class="breakpoint.xsOnly ? '' : 'rounded-lg'"
    >
      <v-card elevation="0" tile color="dialog-fill">
        <customDialogTitle :title="$t('report')" @closeDialog="closeDialog" />
        <div id="report-dialog" :class="breakpoint.xsOnly ? 'scrollable-xs' : 'scrollable-sm'">
          <v-card-text
            class="default-content--text text-body-2 custom-text-noto pa-4 pa-sm-6 pb-sm-4"
          >
            <v-row no-gutters class="align-center justify-left">
              <!-- 檢舉人暱稱 -->
              <v-text-field
                v-model="reportDialogStatus.name"
                :label="$t('counterpart_nickname')"
                disabled
                filled
                shaped
              />
            </v-row>
            <v-row no-gutters class="align-center justify-left">
              <!-- 詐騙時間 -->
              <v-menu
                v-if="!breakpoint.xsOnly"
                v-model="dateTimeMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                max-width="290px"
                min-width="auto"
                content-class="rounded-lg"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    v-model="dateAndTime"
                    :label="$t('fraud_time') + '*'"
                    filled
                    shaped
                    readonly
                    v-on="on"
                    v-bind="attrs"
                  />
                </template>
                <div v-if="dateTimeMenu" class="report-time-tab">
                  <v-tabs
                    id="reportDialogTabs"
                    v-model="dateTimeTab"
                    background-color="dialog-fill"
                    align-with-title
                    grow
                  >
                    <v-tabs-slider
                      color="primary-variant-1
                  "
                    ></v-tabs-slider>
                    <v-tab> <span class="mdi mdi-calendar-range tab-icon"></span> </v-tab>
                    <v-tab> <span class="mdi mdi-clock tab-icon"></span> </v-tab>
                  </v-tabs>
                  <v-tabs-items v-model="dateTimeTab" class="report-time-scroll">
                    <!-- 日期選擇 -->
                    <v-tab-item>
                      <v-date-picker
                        id="reportDialogDatePicker"
                        color="primary-variant-1"
                        v-model="date"
                        no-title
                        locale="zh-cn"
                        :min="mindate"
                        :max="today"
                        prev-icon="mdi-chevron-left"
                        :next-icon="islastMouth ? '' : 'mdi-chevron-right'"
                        :picker-date.sync="pickerDate"
                        @input="dateClick"
                      ></v-date-picker>
                    </v-tab-item>
                    <!-- 時間選擇 -->
                    <v-tab-item>
                      <v-time-picker
                        id="reportDialogTimePicker"
                        v-model="time"
                        :max="getRealTime()"
                        color="primary-variant-1"
                        format="24hr"
                        scrollable
                        :active-picker="activePicker"
                      >
                      </v-time-picker>
                    </v-tab-item>
                  </v-tabs-items>
                  <!-- date time功能區 -->
                  <div class="pa-3 picker-actions d-flex">
                    <v-spacer />
                    <div>
                      <v-btn text color="primary" @click="dateTimeCancel">
                        {{ $t('cancel') }}
                      </v-btn>
                    </div>
                    <div>
                      <v-btn text color="primary" @click="dateTimeCancel">
                        {{ $t('sure') }}
                      </v-btn>
                    </div>
                  </div>
                </div>
              </v-menu>
              <v-dialog
                v-else
                v-model="dateTimeMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                max-width="290px"
                min-width="auto"
                content-class="rounded-lg"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    v-model="dateAndTime"
                    :label="$t('fraud_time') + '*'"
                    filled
                    shaped
                    readonly
                    v-on="on"
                    v-bind="attrs"
                  />
                </template>
                <div v-if="dateTimeMenu" class="report-time-tab">
                  <v-tabs
                    id="reportDialogTabs"
                    v-model="dateTimeTab"
                    background-color="dialog-fill"
                    align-with-title
                    grow
                  >
                    <v-tabs-slider
                      color="primary-variant-1
                  "
                    ></v-tabs-slider>
                    <v-tab> <span class="mdi mdi-calendar-range tab-icon"></span> </v-tab>
                    <v-tab> <span class="mdi mdi-clock tab-icon"></span> </v-tab>
                  </v-tabs>
                  <v-tabs-items v-model="dateTimeTab" class="report-time-scroll">
                    <!-- 日期選擇 -->
                    <v-tab-item>
                      <v-date-picker
                        id="reportDialogDatePicker"
                        color="primary-variant-1"
                        v-model="date"
                        no-title
                        locale="zh-cn"
                        :min="mindate"
                        :max="today"
                        prev-icon="mdi-chevron-left"
                        :next-icon="islastMouth ? '' : 'mdi-chevron-right'"
                        :picker-date.sync="pickerDate"
                        @input="dateClick"
                      ></v-date-picker>
                    </v-tab-item>
                    <!-- 時間選擇 -->
                    <v-tab-item>
                      <v-time-picker
                        id="reportDialogTimePicker"
                        v-model="time"
                        :max="getRealTime()"
                        color="primary-variant-1"
                        format="24hr"
                        scrollable
                        :active-picker="activePicker"
                      >
                      </v-time-picker>
                    </v-tab-item>
                  </v-tabs-items>
                  <!-- date time功能區 -->
                  <div class="pa-3 picker-actions d-flex">
                    <v-spacer />

                    <div>
                      <v-btn text color="primary" @click="dateTimeCancel">
                        {{ $t('cancel') }}
                      </v-btn>
                    </div>
                    <div>
                      <v-btn text color="primary" @click="dateTimeCancel">
                        {{ $t('sure') }}
                      </v-btn>
                    </div>
                  </div>
                </div>
              </v-dialog>
            </v-row>
            <v-row no-gutters class="align-center justify-left">
              <!-- 詐騙原由* -->
              <v-textarea
                no-resize
                filled
                shaped
                counter="300"
                :counter-value="() => reportReasonCount"
                maxlength="300"
                :label="$t('fraud_reason') + '*'"
                background-color="rgba(255, 255, 255, 0.08)"
                v-model="reportReason"
              ></v-textarea>
            </v-row>
            <v-row no-gutters>
              <span class="grey-3--text custom-text-noto text-body-2 pt-2">{{
                $t('report_text1')
              }}</span>
            </v-row>
          </v-card-text>
          <v-card-actions class="pt-0 px-4 px-sm-6 pb-4 pb-sm-6">
            <v-spacer />

            <v-btn
              :class="['button-content--text', 'text-button', breakpoint.xsOnly ? 'w-100' : '']"
              :color="$UIConfig.defaultBtnColor"
              depressed
              :disabled="sendBtnDisable"
              @click="showConfirmReportDialog"
            >
              {{ $t('confirm') }}{{ $t('send') }}
            </v-btn>
          </v-card-actions>
        </div>
      </v-card>
    </v-dialog>
    <confirmReportDialog
      v-if="showConfirmReportDialogStatus"
      :confirm-report-dialog-status="{
        show: showConfirmReportDialogStatus,
        name: reportDialogStatus.name,
        confirm: showConfirm
      }"
      @responseParentData="confirmResponse"
    ></confirmReportDialog>
  </div>
</template>
<script>
  import scssLoader from '@/mixins/scssLoader.js'
  export default {
    name: 'showReportDialog',
    components: {
      customDialogTitle: () => import('~/components/customDialogTitle'),
      confirmReportDialog: () => import('~/components/relationShip/confirmReportDialog')
    },
    mixins: [scssLoader],
    props: {
      reportDialogStatus: { type: Object, default: { show: false, name: '' } }
    },
    mounted() {
      this.$store.commit('chat/SET_CUSTOMER_SEND_TYPE', 2)
      this.updateNowTime()
    },
    data() {
      return {
        disabledTopUpBtnStatus: false,
        dateTimeTab: null,
        pickerDate: null,
        dateTimeMenu: false,
        activePicker: null,
        showConfirm: false,
        date: '',
        time: '00:00',
        realDate: '',
        realTime: '00:00',
        //詐騙原由
        reportReason: '',
        showConfirmReportDialogStatus: false,
        showReportDialogStatustemp: this.reportDialogStatus.show
      }
    },
    watch: {
      reportDialogStatus: {
        handler(val) {
          this.showReportDialogStatustemp = val.show
        }
      },
      dateTimeMenu: {
        handler(val) {
          if (val) {
            this.dateTimeTab = 0
          }
        }
      }
    },
    computed: {
      today: function () {
        return this.$moment().utcOffset(8).format('YYYY-MM-DD')
      },
      mindate: function () {
        return this.$moment().utcOffset(8).subtract(730, 'days').format('YYYY-MM-DD')
      },
      islastMouth: function () {
        const lastMouth = this.$moment().utcOffset(8).format('YYYY-MM')
        return lastMouth === this.pickerDate
      },
      dateAndTime: function () {
        if (this.date && this.time) {
          return `${this.date} ${this.time}`
        } else {
          return this.$moment().utcOffset(8).format('YYYY-MM-DD HH:mm')
        }
      },
      sendBtnDisable: function () {
        if (this.reportDialogStatus.name && this.reportReason) {
          return false
        } else {
          return true
        }
      },
      userName({ $store }) {
        return $store.getters['role/userName']
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      },
      reportInterval() {
        return this.$UIConfig.restriction.reportInterval
      },
      reportReasonCount() {
        return this.reportReason.length
      }
    },
    methods: {
      getRealTime() {
        return this.realDate === this.date ? this.realTime : ''
      },
      updateNowTime() {
        const todayString = this.$moment().utcOffset(8).format('YYYY-MM-DD')
        const timeString = this.$moment().utcOffset(8).format('HH:mm')
        this.realDate = todayString
        this.realTime = timeString
        this.date = todayString
        this.time = timeString
      },
      closeDialog() {
        this.reportReason = ''
        this.clearDateAndTime()
        this.$store.commit('chat/SET_CUSTOMER_SEND_TYPE', 0)
        this.$emit('update:reportDialogStatus', { show: false, name: '' })
      },
      dateTimeCancel() {
        this.dateTimeTab = 0
        this.dateTimeMenu = false
      },
      //選完日期選時間
      dateClick() {
        if (this.realDate === this.date) {
          const timeDiff = this.$moment(this.time, 'HH:mm').diff(
            this.$moment(this.realTime, 'HH:mm')
          )
          if (timeDiff > 0) this.time = this.realTime
        }
        this.dateTimeTab = 1
      },
      //按叉叉清除日期時間
      clearDateAndTime() {
        this.date = this.realDate
        this.time = this.realTime
        this.dateTimeTab = 0
      },
      showConfirmReportDialog() {
        this.showConfirm = true
        this.showConfirmReportDialogStatus = true
      },
      confirmResponse(responseData) {
        this.showConfirmReportDialogStatus = false
        if (responseData.confirm && this.showConfirm) {
          this.sendReport(this.reportDialogStatus.name, this.dateAndTime, this.reportReason)
          setTimeout(() => {
            this.showConfirm = false
            this.showConfirmReportDialogStatus = true
            this.showReportDialogStatustemp = false
          }, 100)
        } else if (responseData.confirm && !this.showConfirm) {
          this.closeDialog()
        }
      },
      async sendReport(name, dateTime, reason) {
        const userReport = {
          userName: this.userName,
          reportExpired: this.$moment().add(30, 'm').format()
        }
        const customerReport = {
          userName: this.userName,
          customerExpired: this.$moment().add(this.reportInterval, 's').format()
        }
        const reportMessage = `${this.$t('fraud_title')}\r\n${this.$t(
          'counterpart_nickname'
        )}:  ${name} \r\n${this.$t('fraud_time')}:  ${dateTime}  \r\n${this.$t(
          'fraud_reason'
        )}:  ${reason}`
        const reqData = this.$wsPacketFactory.sendBilkReport(reportMessage)
        this.$wsClient.send(reqData)

        const userLocalStorage = this.$localStorage.get('reportTimeNoty').userArray
        const userReportArray = userLocalStorage ? JSON.parse(userLocalStorage) : []
        const reportIndex = userReportArray.findIndex((x) => x.userName === this.userName)
        if (reportIndex >= 0) userReportArray[reportIndex] = userReport
        else userReportArray.push(userReport)

        const usercustomerStorage = this.$localStorage.get('customerTimeNoty').userArray
        const usercustomerArray = usercustomerStorage ? JSON.parse(usercustomerStorage) : []
        const customerIndex = usercustomerArray.findIndex((x) => x.userName === this.userName)
        if (customerIndex >= 0) usercustomerArray[customerIndex] = customerReport
        else usercustomerArray.push(customerReport)
        this.$localStorage.set('customerTimeNoty', {
          userArray: JSON.stringify(usercustomerArray)
        })
        this.$localStorage.set('reportTimeNoty', {
          userArray: JSON.stringify(userReportArray)
        })
      }
    },
    beforeDestroy() {}
  }
</script>
<!-- 加deep沒用 ID長度用長一點 -->
<style lang="scss">
  $dialog-fill: map-get($colors, 'dialog-fill');
  $primary-variant-1: map-get($colors, 'primary-variant-1');
  $primary-variant-2: map-get($colors, 'primary-variant-2');
  $primary-variant-3: map-get($colors, 'primary-variant-3');
  $button-content: map-get($colors, 'button-content');

  #reportDialogTabs {
    .tab-icon {
      font-size: 24px !important;
    }
    .v-tab {
      margin: 0 !important;
    }
  }
  #reportDialogDatePicker {
    border-radius: 0;
    .v-picker__body {
      background-color: $dialog-fill !important;
    }
    .v-date-picker-header {
      background: linear-gradient(
        270deg,
        $primary-variant-3 0%,
        $primary-variant-1 100%
      ) !important;
    }
    .v-btn {
      color: $primary-variant-2 !important;
      i {
        color: $button-content;
      }
    }
    .v-btn.v-btn--disabled {
      color: rgba(255, 255, 255, 0.3) !important;
    }
    .v-btn--active {
      .v-btn__content {
        color: $button-content !important;
      }
    }
    .v-date-picker-header__value button {
      color: $dialog-fill !important;
    }
    thead tr th {
      color: $primary-variant-1;
    }
    .v-date-picker-table__current {
      border-color: $primary-variant-1 !important;
      .v-btn__content {
        color: $primary-variant-1;
      }
    }
    .v-date-picker-table {
      height: auto;
    }
  }
  #reportDialogTimePicker {
    &.v-picker--time {
      border-radius: 0;
      .v-picker__title {
        background: linear-gradient(
          270deg,
          $primary-variant-3 0%,
          $primary-variant-1 100%
        ) !important;
      }
      .v-picker__body {
        background-color: $dialog-fill !important;
      }
      .v-time-picker-clock {
        background-color: rgba(255, 255, 255, 0.3) !important;
      }
      .v-time-picker-clock__item {
        color: $primary-variant-2 !important;
      }
      .v-time-picker-clock__item--active {
        color: $button-content !important;
        background-color: $primary-variant-1 !important;
      }
      .v-time-picker-title__time {
        color: $button-content !important;
      }
      .v-time-picker-clock__hand {
        color: $primary-variant-1 !important;
        background-color: $primary-variant-1 !important;
      }
      .v-time-picker-clock__item--disabled {
        color: rgba(255, 255, 255, 0.3) !important;
      }
    }
  }
  .center-menu {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .picker-actions {
    background-color: $dialog-fill !important;
    border-top: solid 1px rgba(255, 255, 255, 0.4);
  }
  #report-dialog {
    &.scrollable-sm {
      max-height: calc(90vh - 52px);
      overflow-y: auto;
    }
    &.scrollable-xs {
      max-height: calc(100vh - 52px);
      overflow-y: auto;
    }
    @supports (height: 90svh) {
      &.scrollable-sm {
        max-height: calc(90svh - 52px);
      }
      &.scrollable-xs {
        max-height: calc(100svh - 52px);
      }
    }
  }
  .report-time-tab {
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    .report-time-scroll {
      overflow-y: auto;
    }
  }
</style>
