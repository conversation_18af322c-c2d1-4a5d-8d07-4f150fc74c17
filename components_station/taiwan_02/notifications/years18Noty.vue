<!-- eslint-disable vue/no-v-html -->
<template>
  <v-dialog
    v-model="showYears18NotyStatusTmp.show"
    persistent
    max-width="429"
    content-class="rounded-lg"
  >
    <!-- title -->
    <v-row
      no-gutters
      align="center"
      justify="center"
      class="title gradient-primary-down rounded-b-0 px-6"
    >
      <span class="button-content--text text-subtitle-1 custom-text-noto">
        {{ $t('reminder') }}
      </span>
    </v-row>
    <v-container fluid class="rounded-3 pa-4 pa-sm-6" id="wayi-years-noty">
      <v-row
        no-gutters
        justify="center"
        class="text-center custom-text-noto text-subtitle-1 default-content--text"
      >
        <v-col cols="12">
          <span class="">{{ $t('reminder_18_1') }}</span>
        </v-col>
        <v-col cols="12">
          <span>{{ $t('reminder_18_2') }}</span>
        </v-col>
      </v-row>
      <v-row no-gutters justify="center" class="mt-6">
        <v-col cols="12" class="default-content--text custom-text-noto text-h6">
          <v-menu
            ref="menu"
            v-model="menu"
            :close-on-content-click="false"
            transition="scale-transition"
            offset-y
            min-width="auto"
          >
            <template v-slot:activator="{ on, attrs }">
              <v-text-field
                v-model="date"
                v-validate="{ age_over_limit: 18 }"
                :error-messages="errors.has('date') ? errors.first('date') : []"
                name="date"
                :label="`${$t('birthdate')}*`"
                filled
                shaped
                readonly
                v-bind="attrs"
                v-on="on"
              ></v-text-field>
            </template>
            <v-date-picker
              v-model="date"
              no-title
              color="dialog-fill"
              class="years-18-date-picker primary--text"
              :active-picker.sync="activePicker"
              locale="zh-Hant"
              :show-current="date ? false : '2000-01-01'"
              :max="
                new Date(Date.now() - new Date().getTimezoneOffset() * 60000)
                  .toISOString()
                  .substring(0, 10)
              "
              min="1950-01-01"
              @change="save"
            ></v-date-picker>
          </v-menu>
        </v-col>
        <v-col cols="12" class="text-left custom-text-noto text-body-2 default-content--text">
          <ul>
            <li>
              {{ $t('warning_18_1') }}
            </li>
            <li>
              {{ $t('warning_18_2') }}
            </li>
            <li>
              {{ $t('warning_18_3') }}
            </li>
            <li>
              {{ $t('warning_18_4') }}
            </li>
            <li>
              {{ $t('warning_18_5') }}
            </li>
          </ul>
        </v-col>
      </v-row>
      <v-row no-gutters justify="center" class="text-center mt-6">
        <v-col cols="12" class="d-flex justify-center default-content--text custom-text-noto">
          <v-checkbox dense v-model="checkbox" :label="`${$t('today_dont_display')}`"></v-checkbox>
        </v-col>
      </v-row>
      <v-row no-gutters class="justify-center text-center">
        <v-col cols="12" class="d-flex justify-center text-sm-body-2 default-content--text">
          <v-btn
            block
            depressed
            color="primary"
            class="button-content--text px-16"
            @click="closeDialog"
            :disabled="!date || errors.has('date')"
          >
            {{ $t('into').toUpperCase() }}
          </v-btn>
        </v-col>
      </v-row>
    </v-container>
  </v-dialog>
</template>
<script>
  const STATION = process.env.STATION
  export default {
    name: 'Years18Noty',
    mixins: [
      require('~/mixins/hiddenScrollHtml').default,
      require(`~/mixins_station/${STATION}/login`).default,
      require('~/mixins/analytics').default,
      require('~/mixins/scssLoader').default
    ],
    props: {
      showYears18NotyStatus: {
        type: Object,
        default: () => ({ show: false, onConfirmNotify: () => {} })
      }
    },

    data() {
      return {
        showYears18NotyStatusTmp: this.showYears18NotyStatus,
        checkbox: false,
        activePicker: null,
        date: null,
        menu: false
      }
    },
    computed: {},
    watch: {
      showYears18NotyStatus: {
        handler(status) {
          this.showYears18NotyStatusTmp = status
        }
      },
      menu(val) {
        val && setTimeout(() => (this.activePicker = 'YEAR'))
      }
    },
    mounted() {},
    methods: {
      save(date) {
        this.$refs.menu.save(date)
      },
      async closeDialog() {
        if (this.$route.query.loginAuth) {
          await this.wsInit()
          await this.autologinHandler(this.$route.query.loginAuth)
        }
        if (this.checkbox) {
          this.$localStorage.set('years18Noty', { expired: this.$moment().format('YYYY-MM-DD') })
        }
        this.$store.commit('role/SET_BIRTHDATE', this.date)
        this.showYears18NotyStatusTmp.onConfirmNotify?.()
        this.$emit('update:showYears18NotyStatus', { show: false, onConfirmNotify: () => {} })
      }
    }
  }
</script>
<style scoped lang="scss">
  $dialog-fill: map-get($colors, dialog-fill);
  $primary: map-get($colors, primary);
  $primary-variant-2: map-get($colors, primary-variant-2);
  $button-content: map-get($colors, button-content);
  .title {
    height: 58px;
  }

  .years-18-date-picker::v-deep {
    .v-date-picker-header {
      .v-btn--icon {
        color: $primary-variant-2;
      }
      .v-date-picker-header__value {
        button {
          color: $primary-variant-2 !important;
        }
      }
    }

    .v-picker__body {
      background: $dialog-fill;
      .v-date-picker-table {
        th,
        .v-date-picker-table--date__week {
          color: $primary;
        }
        .v-date-picker-table__current {
          color: $primary !important;
        }
        .v-btn--active {
          color: $button-content !important;
          &:before {
            background-color: $primary;
            opacity: 1;
          }
        }
        .v-btn--text {
          color: $primary;
        }
      }

      li {
        color: $primary-variant-2 !important;
        &.active {
          color: $primary !important;
        }
      }
    }
  }
  #wayi-years-noty {
    max-height: calc(90vh - 58px);
    overflow-y: auto;
  }
  @supports (height: 90svh) {
    #wayi-years-noty {
      max-height: calc(90svh - 58px);
    }
  }
</style>
