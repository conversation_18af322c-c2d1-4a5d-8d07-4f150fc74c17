<template>
  <v-container fluid class="max-width-800px">
    <v-row class="text-center justify-space-between align-center">
      <v-col cols="6">
        <v-select
          v-model="vip"
          :items="vipNamesList"
          :disabled="disableBtnStatus"
          label="vip"
          data-vv-name="level"
          shaped
          filled
          class="mb-0"
        ></v-select>
      </v-col>
      <v-col cols="6">
        <v-btn
          class="button-content--text"
          color="primary-variant-1"
          elevation="0"
          :disabled="disableBtnStatus"
          @click="setVIP()"
        >
          VIP update
        </v-btn>
      </v-col>
      <v-col cols="6">
        <v-text-field
          ref="money"
          v-model="money"
          v-validate="'required|positive_number'"
          type="number"
          :disabled="disableBtnStatus"
          :error-messages="errors.first('form.money')"
          label="money"
          data-vv-name="money"
          data-vv-scope="form"
          shaped
          filled
          hide-spin-buttons
        />
      </v-col>
      <v-col cols="6">
        <v-btn
          class="button-content--text"
          color="primary-variant-1"
          elevation="0"
          :disabled="disableBtnStatus || !isPositiveNumber"
          @click="setMoney()"
        >
          MONEY update
        </v-btn>
      </v-col>
      <!-- 語言 -->
      <!-- <v-col cols="6">
        <v-select
          v-model="language"
          :items="languageList"
          label="game related - language "
          shaped
          filled
          class="mb-0"
        ></v-select>
      </v-col>
      <v-col cols="6">
        <v-btn
          class="button-content--text"
          color="primary-variant-1"
          elevation="0"
          @click="updateStationSetting()"
        >
          game related update
        </v-btn>
      </v-col> -->
    </v-row>
  </v-container>
</template>
<script>
  export default {
    name: 'QcGameSetting',
    data() {
      return {
        vip: '1_LowBronze_低等青銅',
        disableBtnStatus: false,
        money: 0,
        language: 'zh-tw',
        languageList: ['zh-tw', 'en-us'],
        clientId: '',
        allStations: []
      }
    },
    computed: {
      isLogin({ $store }) {
        return $store.getters['role/isLogin']
      },
      vipNamesList({ $store }) {
        // 青銅階級有 1-9 與 10 - 19 不同級距，所以多個低等青銅作區分
        const vips = ['1_LowBronze_低等青銅']
        const vipLevelInfo = $store.getters['role/vipLevelInfo']
        const vipLevelInfoMap = vipLevelInfo.map((item) => {
          return item.VipLevel + '_' + item.Vip + '_' + item.DisplayName
        })
        return [...vips, ...vipLevelInfoMap]
      },
      vipNames({ $store }) {
        // 青銅階級有 1-9 與 10 - 19 不同級距，所以多個低等青銅作區分
        const vips = ['LowBronze']
        const vipLevelInfo = $store.getters['role/vipLevelInfo'].map((item) => {
          return item.VipName
        })
        return [...vips, ...vipLevelInfo]
      },
      level({ $store }) {
        return $store.getters['role/level']
      },
      isPositiveNumber() {
        return Number(this.money) >= 0
      },
      userVipLevel({ $store }) {
        return $store.getters['role/vipLevel']
      }
    },
    async created() {},
    mounted() {
      if (this.isLogin) {
        if (this.level < 10 && this.userVipLevel === 1) {
          this.vip = this.vipNamesList[0]
        } else {
          this.vip = this.vipNamesList[this.userVipLevel]
        }
      }
    },
    methods: {
      vipLevel(vip, status) {
        let settingObj = {
          vip: 0,
          level: 0
        }
        if (vip === 1) {
          if (status === 'LowBronze') {
            settingObj.vip = 1
            settingObj.level = 9
          } else {
            settingObj.vip = 1
            settingObj.level = 19
          }
        } else if (vip === 2) {
          settingObj.vip = 2
          settingObj.level = 49
        } else if (vip === 3) {
          settingObj.vip = 3
          settingObj.level = 149
        } else if (vip === 4) {
          settingObj.vip = 4
          settingObj.level = 1049
        } else if (vip === 5) {
          settingObj.vip = 5
          settingObj.level = 2049
        } else if (vip === 6) {
          settingObj.vip = 6
          settingObj.level = 5049
        } else if (vip === 7) {
          settingObj.vip = 7
          settingObj.level = 10049
        } else if (vip === 8) {
          settingObj.vip = 8
          settingObj.level = 50049
        } else if (vip === 9) {
          settingObj.vip = 9
          settingObj.level = 100049
        } else if (vip === 10) {
          settingObj.vip = 10
          settingObj.level = 1000049
        }
        return settingObj
      },
      async setVIP() {
        this.disableBtnStatus = true
        const receivedString = this.vip
        const parts = receivedString.split('_')
        const settingObj = this.vipLevel(Number(parts[0]), parts[1])
        const vipCode = this.$route.params.qcStation === 'vietnam_01' ? 'vip=' : 'setvip='
        await this.$store.dispatch('social/sendGMT', vipCode + settingObj.vip)
        this.$notify.info('VIP SET OK !!')
        setTimeout(async () => {
          const levelCode = this.$route.params.qcStation === 'vietnam_01' ? 'lv=' : 'setlevel='
          await this.$store.dispatch('social/sendGMT', levelCode + settingObj.level)
          this.$notify.info('LEVEL SET OK !!')
          this.disableBtnStatus = false
        }, 4000)
      },
      async setMoney() {
        const validate = await this.$validator.validate('form.*')
        if (validate) {
          this.disableBtnStatus = true
          const moneyCode = this.$route.params.qcStation === 'vietnam_01' ? 'gold=' : 'setmoney='
          const commandLine = moneyCode + Number(this.money)
          await this.$store.dispatch('social/sendGMT', commandLine)
          this.$notify.info('MONEY SET OK !!')
          setTimeout(() => {
            this.money = 0
            this.disableBtnStatus = false
          }, 3000)
        }
      }
    },
    watch: {
      language(val) {
        this.$store.commit('gameHall/SET_LANGUAGE', val)
      }
    }
  }
</script>
<style lang="scss" scoped>
  .max-width-800px {
    max-width: 800px;
  }
</style>
