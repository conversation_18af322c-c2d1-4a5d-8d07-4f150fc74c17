<template>
  <client-only>
    <!-- 引入該功能出現以下錯誤 [Vue warn]: The client-side rendered virtual DOM tree is not matching server-rendered content. This is likely caused by incorrect HTML markup, for example nesting block-level elements inside <p>, or missing <tbody>. Bailing hydration and performing full client-side render. -->
    <notifications
      group="notify"
      :duration="5000"
      :position="'right top'"
      :style="{ top: isKeyboardOpen ? `${notificationsPositionHeight}px` : '58px' }"
      :class="{ 'notch-right': hasRightNotch }"
    >
      <template v-slot:body="{ item }">
        <!-- 以後在看有沒有機會換成vuetify 原生 -->
        <!-- <v-alert :icon="icon(item.type)" prominent shaped :type="item.type">
          <div class="notification-title">
            {{ item.title }}
          </div>
          <div class="notification-content">
            {{ item.text }}
          </div>
        </v-alert> -->
        <div
          class="d-flex justify-left align-start notification"
          :class="{
            'notification--success': item.type === 'success',
            'notification--error': item.type === 'error',
            'notification--info': item.type === 'info',
            'notification--warning': item.type === 'warning'
          }"
          @click="clean('notify')"
        >
          <div class="d-flex align-start">
            <v-icon>
              {{ icon(item.type) }}
            </v-icon>
          </div>
          <div class="notification-title">
            {{ convertMessage(item.title) }}
          </div>
          <div v-html="convertNotyMessage(item.text)" class="notification-content"></div>
        </div>
      </template>
    </notifications>
  </client-only>
</template>

<script>
  import converter from '~/mixins/converter'
  import orientation from '@/mixins/orientation.js'
  export default {
    name: 'notify',
    mixins: [converter, orientation],
    data() {
      return {
        notificationsPositionHeight: 58,
        visualViewportHeight: 0
      }
    },
    computed: {
      isKeyboardOpen({ $store }) {
        return $store.getters['chat/isKeyboardOpen']
      }
    },
    watch: {
      isKeyboardOpen(val) {
        if (val) {
          if (this.$device.isMobile && this.$device.isSamsung) {
            this.notificationsPositionHeight = 58
          } else {
            this.$nextTick(() => {
              //確保鍵盤已完全打開 safari需要約700ms的時間才會完全打開
              setTimeout(() => {
                this.notificationsPositionHeight =
                  this.visualViewportHeight - window.visualViewport.height + 10
              }, 700)
            })
          }
        }
      }
    },
    mounted() {
      this.visualViewportHeight = window.visualViewport.height
    },
    methods: {
      convertNotyMessage(text) {
        try {
          let responseText = this.convertMessage(text)
          responseText = responseText.replace('\r\n', '<br>')
          return responseText.trim()
        } catch (e) {
          console.log(e, 'text: ', text)
          return ''
        }
      },
      icon(type) {
        let icon = ''
        switch (type) {
          case 'success': {
            icon = 'mdi-check-circle'
            break
          }
          case 'error': {
            icon = 'mdi-alert-octagon'
            break
          }
          case 'warning': {
            icon = 'mdi-alert'
            break
          }
          case 'info': {
            icon = 'mdi-information'
            break
          }
        }

        return icon
      },
      //清除所有相同group的notify
      clean(group) {
        this.$notify.clean(group)
      }
    }
  }
</script>

<style scoped>
  .vue-notification-group > span > .vue-notification-wrapper {
    z-index: 300;
  }
</style>
<style lang="scss" scoped>
  @media (orientation: landscape) {
    .notch-right {
      right: env(safe-area-inset-right) !important;
    }
  }
</style>
