<template>
  <v-carousel
    height="auto"
    v-model="activeSlide"
    hide-delimiter-background
    show-arrows-on-hover
    :show-arrows="isBannerCountMoreThanOne()"
    hide-delimiters
    class="rounded"
  >
    <v-carousel-item
      v-for="(item, idx) of bannerAdapt"
      :key="idx + 'img'"
      @click.native="item.event"
    >
      <v-img
        v-show="!hasMatch(item.src, videoExtensions)"
        :lazy-src="bannerDefault"
        :src="item.src"
        :aspect-ratio="$vuetify.breakpoint.mdAndUp ? 1600 / 390 : 960 / 400"
        class="cursor-pointer"
        :error="bannerDefault"
      >
        <template #placeholder>
          <v-skeleton-loader type="image" height="100%" />
        </template>
      </v-img>
      <video
        :id="idx + 'video'"
        v-show="hasMatch(item.src, videoExtensions)"
        :class="{
          'video-aspect-ratio': $vuetify.breakpoint.mdAndUp,
          'video-aspect-ratio-sm': !$vuetify.breakpoint.mdAndUp
        }"
        :src="item.src"
        class="cursor-pointer video"
        muted
        playsInline
        :loop="!isBannerCountMoreThanOne()"
        @ended="handleVideoEnd(idx, activeSlide)"
      ></video>
    </v-carousel-item>
  </v-carousel>
</template>
<script>
  export default {
    name: 'Banner',
    props: {
      bannerAdapt: {
        type: Object,
        default: {}
      },
      imageInterval: {
        type: Number,
        default: 5
      }
    },
    data() {
      const videoExtensions = ['.mp4', '.webm'] //判斷是否為影片 若否則為圖片
      return {
        activeSlide: 0,
        imgTimerCount: -1,
        videoExtensions,
        timer: null
      }
    },
    computed: {
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      },
      station({ $store }) {
        return $store.getters['station']
      },
      bannerDefault() {
        return this.$vuetify.breakpoint.smAndUp
          ? `${process.env.IMAGE_URL}/banner/${this.station.client_id}/default_banner_lg.webp`
          : `${process.env.IMAGE_URL}/banner/${this.station.client_id}/default_banner_xs.webp`
      }
    },
    async created() {
      this.resetCarouselView(this.activeSlide)
    },
    mounted() {
      this.timer = setInterval(() => {
        if (this.imgTimerCount > 0) this.imgTimerCount--
      }, 1000)
    },
    beforeDestroy() {
      clearInterval(this.timer)
    },
    methods: {
      hasMatch(src, extensionArray) {
        return extensionArray.some((extension) => src.includes(extension))
      },
      resetCarouselView(activeSlide) {
        const srcValues = Object.values(this.bannerAdapt)
        this.imgTimerCount = -1
        if (srcValues.length > 0) {
          if (this.hasMatch(srcValues[activeSlide].src, this.videoExtensions)) {
            requestAnimationFrame(() => {
              const srcKeys = Object.keys(this.bannerAdapt)[this.activeSlide]
              const video = document.getElementById(srcKeys + 'video')
              video.play()
            })
          } else {
            this.imgTimerCount = this.imageInterval
          }
        }
      },
      isBannerCountMoreThanOne() {
        const srcValues = Object.values(this.bannerAdapt)
        return srcValues.length > 1
      },
      handleVideoEnd(key, activeSlide) {
        const activeSlideKey = Object.keys(this.bannerAdapt)[activeSlide]
        if (key === activeSlideKey) this.setNextPage()
      },
      setNextPage() {
        this.activeSlide = (this.activeSlide + 1) % Object.keys(this.bannerAdapt).length
      }
    },
    watch: {
      bannerAdapt: {
        handler(val) {
          const srcValues = Object.values(val)
          if (srcValues.length > 0) this.resetCarouselView(this.activeSlide)
        }
      },

      activeSlide: {
        handler(val) {
          this.resetCarouselView(val)
        }
      },
      imgTimerCount: {
        handler(value) {
          if (value === 0) {
            this.setNextPage()
          }
        }
      },
      '$vuetify.breakpoint.mdAndUp': {
        handler() {
          this.resetCarouselView(this.activeSlide)
        }
      }
    }
  }
</script>
<style lang="scss" scoped>
  .video-aspect-ratio {
    aspect-ratio: 1600 / 390;
  }
  .video-aspect-ratio-sm {
    aspect-ratio: 960 / 400;
  }
  .video {
    display: flex;
    justify-content: center;
    align-content: center;
    width: 100%;
    object-fit: cover;
  }
</style>
