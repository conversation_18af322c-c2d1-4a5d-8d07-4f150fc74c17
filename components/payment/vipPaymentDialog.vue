<!-- eslint-disable vue/no-v-html -->
<template>
  <v-dialog
    v-model="showVipPaymentDialogTmp"
    persistent
    :max-width="imgStyle.imgWidth"
    :max-heaght="imgStyle.imgHeight"
    content-class="transparent elevation-0"
  >
    <!-- img  -->
    <div class="img-block">
      <!-- close button -->
      <v-btn fab text class="close-btn" @click="goVipPayment">
        <span class="material-symbols-outlined"> close </span>
      </v-btn>
      <div @click="goVipPayment" class="img-btn">
        <v-img
          contain
          :width="imgStyle.imgWidth"
          :height="imgStyle.imgHeight"
          :src="getImage(imgStyle.imgSrc)"
        />
      </div>
    </div>
    <!-- checkbox -->
    <v-row no-gutters align="center" justify="center">
      <v-checkbox v-model="todayDontDisplay" :label="$t('today_dont_display')">
        <template v-slot:label>
          <div class="default-content--text custom-text-noto text-body-1">
            {{ $t('today_dont_display') }}
          </div>
        </template>
      </v-checkbox>
    </v-row>
  </v-dialog>
</template>
<script>
  import hiddenScrollHtml from '@/mixins/hiddenScrollHtml.js'
  import images from '~/mixins/images'
  export default {
    name: 'vipPaymentDialog',
    mixins: [hiddenScrollHtml, images],
    props: {
      showVipPaymentDialogStatus: { type: Boolean, default: false }
    },
    data() {
      return {
        showVipPaymentDialogTmp: this.showVipPaymentDialogStatus,
        todayDontDisplay: false
      }
    },
    watch: {
      showVipPaymentDialogStatus: {
        handler(status) {
          this.showVipPaymentDialogTmp = status
        }
      }
    },
    created() {},
    mounted() {
      this.$nextTick(() => {
        // 今日不顯示
        let localStorageVipPay = this.$localStorage.get('localStorageVipPay').expired
        if (
          localStorageVipPay &&
          !this.$moment(localStorageVipPay).isBefore(this.$moment(), 'day')
        ) {
          this.$emit('update:showVipPaymentDialogStatus', false)
          this.$nuxt.$emit('root:showPaymentDialogStatus', true)
        }
      })
    },
    methods: {
      goVipPayment() {
        this.$emit('update:showVipPaymentDialogStatus', false)
        this.$nuxt.$emit('root:showPaymentDialogStatus', true)
        if (this.todayDontDisplay) {
          //明天失效
          let today = this.$moment()
          this.$localStorage.set('localStorageVipPay', { expired: today })
        }
      }
    },
    computed: {
      imgStyle() {
        let viewWidth = window.innerWidth
        let viewHeight = window.innerHeight
        //0直向 1橫向
        let mobileState = 0
        if (viewWidth > viewHeight) {
          //橫向
          mobileState = 1
        } else {
          //直向
          mobileState = 0
        }
        //之後須考慮所有裝置呈現 (目前只有lg與md)
        let imgWidth = 0
        let imgHeight = 0
        let imgSrc = ''
        //電腦版
        if (this.$device.isDesktop) {
          if (this.$vuetify.breakpoint.mdAndUp) {
            imgWidth = 800
            imgHeight = 640
            imgSrc = 'vip_discount_lg.webp'
          } else {
            imgWidth = 400
            imgHeight = 667
            imgSrc = 'vip_discount_md.webp'
          }
        }
        //平板
        else if (this.$device.isTablet) {
          if (mobileState === 1) {
            //平板橫向
            imgWidth = 600
            imgHeight = 480
            imgSrc = 'vip_discount_lg.webp'
          } else {
            //平板直向
            imgWidth = 500
            imgHeight = 834
            imgSrc = 'vip_discount_md.webp'
          }
        }
        // 手機
        else {
          if (mobileState === 1) {
            //平板橫向
            imgWidth = 270
            imgHeight = 216
            imgSrc = 'vip_discount_lg.webp'
          } else {
            //手機直向
            imgWidth = 327
            imgHeight = 545
            imgSrc = 'vip_discount_md.webp'
          }
        }

        let imgObj = { imgWidth: imgWidth, imgHeight: imgHeight, imgSrc: imgSrc }
        return imgObj
      }
    }
  }
</script>

<style lang="scss" scoped>
  .img-block {
    position: relative;
  }
  .close-btn {
    position: absolute;
    right: 16px;
    top: 16px;
    z-index: 10;
  }
  .img-btn {
    cursor: pointer;
  }
</style>
