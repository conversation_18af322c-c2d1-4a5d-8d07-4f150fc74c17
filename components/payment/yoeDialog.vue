<template>
  <v-dialog
    fullscreen
    max-width="620px"
    persistent
    v-model="showYoeDialogStatus.show"
    content-class="yoe-dialog-background"
  >
    <v-card tile color="transparent" class="rounded-t-lg">
      <v-card-text class="d-flex justify-center align-center pa-0 yoe-dialog-content">
        <iframe
          title="yoeframe"
          ref="yoeGameFrame"
          class="iframe-height-normal"
          :src="yoeShopUrl"
          id="yoeIframe"
          sandbox="allow-scripts allow-same-origin allow-orientation-lock allow-popups allow-forms"
          loading="lazy"
          scrolling="no"
          frameborder="0"
          style="display: block; border: 0px; width: 100%"
          allowfullscreen
        >
        </iframe>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>
<script>
  import setupScrollHtml from '~/mixins/setupScrollHtml'
  import yoeGameMgr from '@/mixins/yoeGameMgr.js'
  import analytics from '@/mixins/analytics.js'
  import orientation from '~/mixins/orientation'
  import images from '~/mixins/images'

  export default {
    name: 'yoeDialog',
    mixins: [setupScrollHtml, orientation, images, yoeGameMgr, analytics],
    props: {
      showYoeDialogStatus: {
        type: Object,
        required: true,
        default: { show: false, shopItem: {} }
      }
    },
    data() {
      return {
        showYoeDialogStatusTmp: false,
        isInit: false,
        alreadyOwneItemArray: []
      }
    },
    watch: {
      showYoeDialogStatus: {
        async handler(value) {
          this.showYoeDialogStatusTmp = value.show
          if (value.show) {
            if (!this.iframe) this.setupIframe()
            if (this.isInit) {
              this.sendBagItemObject()
            } else {
              await this.waitForFlag()
            }
          }
          this.setupScrollActive(value)
        }
      },
      alreadyOwneItemArray: {
        async handler(value) {
          if (value.length !== 0) {
            const alreadyItem = value[0]
            const purchaseSuccess = this.purchaseItem(alreadyItem.purchaseToken)
            if (purchaseSuccess) this.sendAnalytics(alreadyItem)
          }
        }
      }
    },
    computed: {
      isLogin({ $store }) {
        return $store.getters['role/isLogin']
      },
      remainingBalance({ $store }) {
        return $store.getters['yoeShop/remainingBalance']
      },
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      },
      userName({ $store }) {
        return $store.getters['role/userName']
      },
      yoeShopArray({ $store }) {
        return $store.getters['yoeShop/yoeShopArray']
      }
    },
    mounted() {},
    destroyed() {
      this.destroy()
    },
    methods: {
      setupIframe() {
        this.getIframeElement()
          .then((iframe) => {
            this.$store.commit('yoeShop/SET_IFRAME', iframe)
            this.init()
            iframe.src = this.yoeShopUrl
          })
          .catch((error) => {
            console.error('Failed to get iframe:', error)
          })
      },
      getIframeElement() {
        return new Promise((resolve, reject) => {
          const maxAttempts = 10
          let attempts = 0
          const tryGetIframe = () => {
            const iframe = this.$refs.yoeGameFrame || document.getElementById('yoeIframe')
            if (iframe) {
              resolve(iframe)
              return
            }
            attempts++
            if (attempts >= maxAttempts) {
              reject(new Error('Max attempts reached'))
              return
            }
            setTimeout(tryGetIframe, 100)
          }
          tryGetIframe()
        })
      },
      closeDialog() {
        const dialogStatus = {
          show: false,
          shopItem: {},
          cancel: true
        }
        this.$emit('update:showYoeDialogStatus', dialogStatus)
        this.$nuxt.$emit('yoe:dialogStatusChange', dialogStatus)
        setTimeout(() => {
          this.sendMessage({ type: this.sendType.QUERY_PENDING_PURCHASE })
        }, 5000)
      },
      waitForFlag() {
        return new Promise(() => {
          const checkFlag = () => {
            if (this.isInit) {
              this.sendBagItemObject()
            } else {
              setTimeout(checkFlag, 100) // 每100ms檢查一次
            }
          }
          checkFlag()
        })
      },
      async sendBagItemObject() {
        const bagItem = this.showYoeDialogStatus.shopItem
        // 彈窗禮包ID
        const dialogGiftPack = ['wanin.y_gpnew02_30', 'wanin.y_gpnew03_70', 'wanin.y_gppow01_100']
        const maxCount = () => {
          // 檢查是否為彈窗禮包
          if (dialogGiftPack.includes(bagItem.id)) {
            return bagItem.limitPurchase
          }

          // 不是彈窗裡包則檢查是否有購買上限
          if (bagItem.maxBuyLimit > 0) {
            return Math.min(99, bagItem.maxBuyLimit - bagItem.userBuyCount)
          }
          // 無購買上限的項預設單次最高99個
          return 99
        }
        const userBase64 = await this.getUserBase64(this.userName)
        const optionsParam = {
          defineMsg: userBase64,
          isUseAvailableAmount: false,
          qty: {
            max: maxCount(),
            min: 1
          },
          paymentGetWay: 0
        }
        if (this.$UIConfig.countryCode) optionsParam.countryCode = this.$UIConfig.countryCode
        this.sendMessage({
          type: this.sendType.PURCHASE_WIT_OPTIONS,
          arg: {
            productID: bagItem.id,
            optionsParam
          }
        })
      },
      async handleSetVendorID() {
        this.isInit = true
      },
      handleSDKCancel() {
        this.closeDialog()
      },
      async handlePayBill(data) {
        const pendingOrder = {
          user: this.userName,
          purchaseToken: data.result.purchaseToken
        }
        this.$cookies.set('pendingOrders', pendingOrder)
        const purchaseSuccess = this.purchaseItem(data.result.purchaseToken)
        if (purchaseSuccess) this.sendAnalytics()
      },
      async handleQueryPendingPurchase(data) {
        if (data.result.length === 0) return
        this.alreadyOwneItemArray = data.result
      },
      async handleItemAlreadyOwned(data) {
        if (data.result.length === 0) return
        this.alreadyOwneItemArray = data.result
      },
      // eslint-disable-next-line no-unused-vars
      async handleConsumePurchase(data) {
        this.alreadyOwneItemArray.shift()
        this.$emit('update:showYoeDialogStatus', { show: false, shopItem: {}, cancel: false })
        this.$nuxt.$emit('yoe:showYoeDialogStatus', this.showYoeDialogStatus)
      },

      sendAnalytics(alreadyItem) {
        const { shopItem: defaultShopItem } = this.showYoeDialogStatus
        const shopItem = alreadyItem
          ? this.yoeShopArray.find((item) => item.id === alreadyItem.productId)
          : defaultShopItem
        if (shopItem) {
          this.paymentAnalyticsToPlatform({
            username: this.userName,
            type: shopItem.id.toLowerCase().includes('vip') ? 8 : 7,
            point: shopItem.amount
          })
        }
      },
      async purchaseItem(purchaseToken) {
        // 要送消單給sdk的情況: 1.成功  2.錯誤碼=4  3.訂單ErrorCode=28
        //> { 0, 19, 2 } 失敗:0(byte), 錯誤碼(byte), 訂單ErrorCode(byte)[有剩餘資料在讀取]
        //> { 0, 19, 2 } 成功:1(byte)
        //錯誤碼:
        // 1=金流端服務異常
        // 2=金流端訂單錯誤
        // 3=訂單暱稱異常
        // 4=商品ID異常(表示星城不賣了卻買到)

        // 從 cookie 取得待處理訂單資訊
        const pendingOrder = this.$cookies.get('pendingOrders')
        // 檢查 pendingOrder 是否存在且有 purchaseToken 屬性
        if (!pendingOrder || !pendingOrder.purchaseToken) {
          // 如果沒有相關資料，則為原本purchaseItem流程
          // 要送消單給sdk的情況: 1.成功  2.錯誤碼=4  3.訂單ErrorCode=28
          this.$wsClient.send(this.$wsPacketFactory.checkGiftBag(this.userName, purchaseToken))
        } else {
          // 有資料的話才進行比對
          const isSame = purchaseToken === pendingOrder.purchaseToken
          // 檢查是否為同一角色
          const currentName =
            isSame && pendingOrder.user !== this.userName ? pendingOrder.user : this.userName
          this.$wsClient.send(this.$wsPacketFactory.checkGiftBag(currentName, purchaseToken))
        }
        const res = await this.waitResponse(this.$xinConfig.FEATURE.YOEGAME.TYPE.CHECK_GIFT_BAG)
        if (!res) {
          this.alreadyOwneItemArray.shift()
          return false
        }
        if (res.success) {
          this.sendMessage({
            type: this.sendType.CONSUME_PURCHASE,
            arg: { purchaseToken: purchaseToken }
          })
          // 消單成功後清除 cookie
          this.$cookies.remove('pendingOrders')
          return true
        } else {
          if (res.errorCode === 4 || res.orderErrorCode === 28) {
            this.sendMessage({
              type: this.sendType.CONSUME_PURCHASE,
              arg: { purchaseToken: purchaseToken }
            })
            return true
          } else {
            if (this.alreadyOwneItemArray.length !== 0) this.alreadyOwneItemArray.shift()
            else
              this.$nuxt.$emit('update:showYoeDialogStatus', {
                show: false,
                shopItem: {},
                cancel: false,
                purChaseCancel: true
              })
          }
          return false
        }
      }
    }
  }
</script>
<style lang="scss" scoped>
  .rounded-t-lg {
    position: relative;
    .yoe-dialog-title {
      z-index: 1;
    }
    iframe {
      position: absolute;
      bottom: 0;
    }
  }
  .iframe-height-normal {
    height: 100vh;
  }
  ::v-deep {
    .yoe-dialog-background {
      background: #1a0b0bcc !important;
    }
  }
</style>
