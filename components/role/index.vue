<!-- eslint-disable vue/no-use-v-if-with-v-for -->
<template>
  <v-dialog
    v-model="showRoleDialogStatusTmp"
    :max-width="$UIConfig.role.roleDialogWidth"
    :fullscreen="breakpoint.xsOnly"
    scrollable
    persistent
    transition="dialog-transition"
    :content-class="breakpoint.xsOnly ? '' : 'rounded-lg'"
  >
    <v-card
      id="choose-role-card"
      class="d-flex flex-column justify-space-between dialog-fill bg-dialog"
    >
      <!-- 選擇角色登入 文字 -->

      <v-card-title class="gradient-primary-left" :class="$UIConfig.replaceColor.bgDialogHeader">
        <v-row no-gutters justify="center">
          <span
            class="button-content--text custom-text-noto text-subtitle-1 font-weight-regular text-dialog-header-inverse--text"
            style="font-size: 16px !important"
          >
            {{ $t('selecte_role').toUpperCase() }}
          </span>
        </v-row>
      </v-card-title>
      <v-card-text style="flex: 1" class="pt-4 px-4 pb-0 px-sm-6 pt-sm-6">
        <!-- 倒數計時 -->
        <v-row no-gutters class="pb-4">
          <span class="material-symbols-outlined pr-1 text-soft--text">
            nest_clock_farsight_analog
          </span>
          <span class="text-soft--text">{{ $t('countdown') }}</span
          ><span class="warning--text pl-1 text-heavy--text"> {{ loginLastTime }} </span>
          <span class="text-soft--text">{{ $t('second') }}</span>
        </v-row>
        <v-row
          no-gutters
          :style="{ 'max-height': breakpoint.lgAndDown ? '' : '600px; min-height: 250px' }"
          class="overflow-y-auto"
        >
          <v-col v-if="roleList.length <= 0" class="d-flex justify-center align-center">
            <span class="grey-3--text text-soft--text">
              {{ $t('create_role_noty1') }}
            </span>
          </v-col>
          <v-col v-for="(item, index) in roleList" :key="index" cols="12" class="py-2">
            <v-card
              elevation="0"
              class="d-flex align-center gradient-primary-box rounded-lg bg-character-card"
            >
              <v-card elevation="0" class="transparent mr-auto">
                <v-card-title class="primary--text text-subtitle-2 title-character-card--text">
                  {{ item.username }}
                </v-card-title>
                <v-card-subtitle
                  class="grey-2--text text-caption"
                  :class="$UIConfig.replaceColor.textCharacterCard"
                >
                  Lv.{{ item.level }}
                </v-card-subtitle>
              </v-card>
              <!-- 登入 -->
              <v-card elevation="0" class="transparent ml-auto">
                <v-btn
                  class="mx-4 button-content--text text-btn-heavy--text"
                  depressed
                  rounded
                  :color="$UIConfig.replaceColor.roleIndexBgBtnHeavy"
                  @click="roleLogin(item.username)"
                >
                  {{ $t('login').toUpperCase() }}
                </v-btn>
              </v-card>
            </v-card>
          </v-col>
        </v-row>
      </v-card-text>
      <v-card-actions class="pa-4 pt-sm-4 px-sm-6 pb-sm-6">
        <v-row no-gutters justify="end">
          <!-- 登出 -->
          <v-col class="pr-2" :cols="breakpoint.xsOnly ? '6' : 'auto'">
            <v-btn
              :class="breakpoint.xsOnly ? 'w-100' : ''"
              outlined
              :color="$UIConfig.replaceColor.btnRegular"
              @click="$nuxt.$emit('root:showLogoutDialogStatus', true)"
            >
              {{ $t('logout').toUpperCase() }}
            </v-btn></v-col
          >
          <!-- 創建角色 -->
          <v-col class="pl-2" :cols="breakpoint.xsOnly ? '6' : 'auto'">
            <v-btn
              :class="['button-content--text text-btn--text', breakpoint.xsOnly ? 'w-100' : '']"
              :color="$UIConfig.replaceColor.bgBtn"
              depressed
              @click="createRole"
            >
              {{ $t('create_role').toUpperCase() }}
            </v-btn></v-col
          >
        </v-row>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
  import hiddenScrollHtml from '@/mixins/hiddenScrollHtml.js'
  import analytics from '@/mixins/analytics.js'
  import preLoginAction from '@/mixins/preLoginAction.js'
  export default {
    name: 'RoleDialog',
    mixins: [hiddenScrollHtml, analytics, preLoginAction],
    props: {
      showRoleDialogStatus: { type: Boolean, default: false }
    },
    data() {
      return {
        showRoleDialogStatusTmp: this.showRoleDialogStatus,
        waitLoginTimer: null,
        waitLoginTime: 30,
        waitLoginTimerStatus: false,
        showDeviceWhiteDialogStatus: false,
        verifyCode: null
      }
    },
    created() {
      if (!this.loginTimmerState) {
        this.startLoginTimer()
      }
    },
    computed: {
      roleList({ $store }) {
        return $store.getters['role/list']
      },
      //倒數計時
      loginLastTime({ $store }) {
        return $store.getters['role/loginLastTime']
      },
      //倒數計時狀態
      loginTimmerState({ $store }) {
        return $store.getters['role/loginTimmerState']
      },
      phoneNumber({ $store }) {
        return $store.getters['role/phoneNumber']
      },
      showGameModeStatus({ $store }) {
        return $store.getters['menu/showGameModeStatus']
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    watch: {
      showRoleDialogStatus: {
        handler(status) {
          this.showRoleDialogStatusTmp = status
        }
      },
      waitLoginTimerStatus: {
        handler(val) {
          if (val) {
            this.waitLoginTimer = setInterval(this.countDown, 1000)
          }
        }
      },
      loginLastTime: {
        handler(val) {
          if (val <= 0) {
            this.logout()
            this.$nuxt.$emit('root:showNotyDialogStatus', false)
            this.$nuxt.$emit('root:showLogoutDialogStatus', false)
            this.$nuxt.$emit('root:showDeviceWhiteDialogStatus', false)
          }
        }
      }
    },
    beforeDestroy() {
      clearInterval(this.waitLoginTimer)
    },
    methods: {
      createRole() {
        const roleListCount = this.$store.getters['role/roleCount']
        if (roleListCount >= 5) {
          const title = this.$t('reminder')
          let message = '<p class="mb-0 text-wrap">' + this.$t('create_role_limit') + '</p>'
          this.showNotyDialog(title, message)
          return
        } else {
          this.$nuxt.$emit('root:showRoleDialogStatus', false)
          this.$nuxt.$emit('root:showCreateRoleDialogStatus', true)
          // 清除導連
          this.$nuxt.$emit('root:clearRedirect')
        }
      },
      countDown() {
        this.waitLoginTime--
        if (this.waitLoginTime === 0) {
          this.waitLoginTimerStatus = false
          clearInterval(this.waitLoginTimer)
          this.waitLoginTime = 30
        }
      },
      showNotyDialog(title, message) {
        this.$store.dispatch('easyDialog/setDialog', {
          title: title,
          message: message
        })
        this.$nuxt.$emit('root:showNotyDialogStatus', true)
      },
      async roleLogin(userName) {
        const title = this.$t('reminder')
        const promote = this.$store.getters['social/promote']

        this.$store.commit('xinProtocol/SET_SERVICE_IDS', {
          serviceId: this.$xinConfig.LOGIN_SERVICE.ID,
          enable: false,
          connected: false
        })

        this.$wsClient.send(this.$wsPacketFactory.selectCharacter({ username: userName, promote }))
        let message = ''
        // 避免剛登入時連續跳出 noty info 的設計，先透過該值關閉通知
        this.$store.commit('mail/SET_FIRST_RECIVE', true)
        try {
          const res = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
            return data.isFeature(this.$xinConfig.FEATURE.LOGIN.ID)
          })
          // 因流程可能會未登入成功(角色在線、裝置驗證、裝置驗證失敗)
          if (
            res.type === 0 &&
            res.commandId === this.$xinConfig.LOGIN_SERVICE.TYPE.PLAYER_INFO.ID
          ) {
            //成功登入時將倒數計時暫停
            this.$store.commit('role/SET_LOGIN_TIMMER_STATE', false)
            // 先發送前三個服務的加入請求
            const initialServices = [
              this.$xinConfig.LOTTERY_SERVICE.ID, // 進入摸彩服務
              this.$xinConfig.GAME_SERVICE.ID, // 進入遊戲服務
              this.$xinConfig.SOCIAL_SERVICE.ID // 進入社群服務
            ]

            initialServices.forEach((serviceId) => {
              this.$wsClient.send(this.$wsPacketFactory.getServiceJoin(serviceId))
              this.$store.commit('xinProtocol/SET_SERVICE_IDS', {
                serviceId,
                enable: true,
                connected: true
              })
            })

            // 等待 SOCIAL_SERVICE 確認
            await this.$xinUtility
              .waitEvent(
                this.$wsClient.receivedListeners,
                (data) =>
                  data.protocolId === this.$xinConfig.PROTOCOL_ID.SERVICE &&
                  data.serviceId === this.$xinConfig.SOCIAL_SERVICE.ID
              )
              .then(() => {
                this.$wsClient.send(this.$wsPacketFactory.initMail())
                // 延遲五秒後開啟新信件通知
                setTimeout(() => {
                  this.$store.commit('mail/SET_FIRST_RECIVE', false)
                }, 5000)
              })
              .catch((err) => {
                console.log('SOCIAL_SERVICE ERROR:', err)
              })
            // 初始化角色資料
            await this.$store.dispatch('role/profileInit', res)
            // 再加入公會服務
            this.$wsClient.send(
              this.$wsPacketFactory.getServiceJoin(this.$xinConfig.GUILD_SERVICE.ID)
            )
            this.$store.commit('xinProtocol/SET_SERVICE_IDS', {
              serviceId: this.$xinConfig.GUILD_SERVICE.ID,
              enable: true,
              connected: true
            })

            if (
              Object.prototype.hasOwnProperty.call(res, 'lastLogoutTime') &&
              res.lastLogoutTime === '1900/01/01 00:00:00'
            ) {
              this.createRoleAnalytics()
            }

            //取得與伺服器時間差
            await this.$store.dispatch('xinProtocol/getServerLocalTimeDiff')

            setTimeout(async () => {
              this.$nuxt.$emit('root:showRoleDialogStatus', false)
              this.$nuxt.$emit('root:showLoginDialogStatus', {
                show: false,
                onCancelNotify: () => {}
              })
              // 成功登入，執行使用者登入前動作
              this.callPreLoginAction()
              // 站台差異註解
              if (this.$UIConfig.lock.getSlotIdentity) {
                const getSlotIdentity = await this.$waninNorthApi.user.getSlotIdentity({
                  PhoneNumber: this.phoneNumber
                })
                if (getSlotIdentity.status !== 200) {
                  this.$nuxt.$emit('root:showIdentityVerifiDialogStatus', true)
                }
              }
            }, 500)
            this.$nuxt.$emit('root:showWelcomeStatus', true)
          } else if (res.commandId === 136) {
            this.$notify.info(res.message)
            this.$nuxt.$emit('root:showDeviceWhiteDialogStatus', true)
          } else if (res.commandId === 135) {
            message = res.message
            this.showNotyDialog(title, message)
          }
        } catch (error) {
          console.log('role Page', error)
        }
      },
      startLoginTimer() {
        this.$store.commit('role/SET_LOGIN_LAST_TIME', 300)
        this.$store.dispatch('role/startLoginTimer')
      },
      logout() {
        const reqData = this.$wsPacketFactory.logout()
        this.$wsClient.send(reqData)
        this.$wsClient.disconnect()
        this.$nuxt.$emit('root:showRoleDialogStatus', false)
        this.$nuxt.$emit('root:showLoginDialogStatus', {
          show: true,
          onCancelNotify:
            this.showGameModeStatus && this.$route.params.mode === 'play'
              ? () => this.$router.push(this.localePath('/'))
              : () => {}
        })
        this.$store.dispatch('clear')
        this.$cookies.remove('xinToken', { path: '/' })
        // 清除導連
        this.$nuxt.$emit('root:clearRedirect')
      }
    }
  }
</script>
<style lang="scss" scoped>
  @media screen and (max-width: 599px) {
    #choose-role-card.v-card {
      border-radius: 0px !important;
    }
  }
</style>
