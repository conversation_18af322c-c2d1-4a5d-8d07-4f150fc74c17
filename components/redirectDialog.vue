<template>
  <v-dialog
    v-model="redirectDialogStatusLocal.show"
    persistent
    :max-width="400"
    content-class="rounded-lg"
  >
    <v-card tile color="dialog-fill pa-4 pa-sm-6 bg-dialog">
      <v-card-title class="d-flex justify-center pa-0 text-dialog-header--text">
        {{ $t('hint').toUpperCase() }}
      </v-card-title>
      <v-card-text class="px-0 py-6">
        <span class="default-content--text accept-word-break text-regular--text"
          >{{ $t('go_download_ask') }} ?</span
        >
      </v-card-text>
      <v-card-actions class="pa-0">
        <v-row no-gutters justify="end">
          <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pr-2">
            <v-btn
              :class="['default-content--text btn-soft--text', breakpoint.xsOnly ? 'w-100' : '']"
              text
              @click="closeDialog"
            >
              {{ $t('cancel').toUpperCase() }}
            </v-btn>
          </v-col>
          <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pl-2">
            <v-btn
              :class="['button-content--text text-btn--text', breakpoint.xsOnly ? 'w-100' : '']"
              :color="$UIConfig.replaceColor.bgBtn"
              depressed
              @click="confirm"
            >
              {{ $t('sure').toUpperCase() }}
            </v-btn>
          </v-col>
        </v-row>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
  import analytics from '@/mixins/analytics.js'
  export default {
    name: 'redirectDialog',
    mixins: [analytics],
    props: {
      redirectDialogStatus: {
        type: Object,
        default: {
          show: false,
          drawAnalytics: false
        }
      }
    },
    data() {
      return {
        redirectDialogStatusLocal: this.redirectDialogStatus
      }
    },
    computed: {
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    methods: {
      closeDialog() {
        this.$emit('update:redirectDialogStatus', { show: false, drawAnalytics: false })
        this.$store.commit('yoeShop/SET_IS_PURCHASING', false)
      },
      confirm() {
        this.handleRedirect()
        this.closeDialog()
      },
      handleRedirect() {
        // 確定目標URL
        const targetUrl = this.getRedirectUrl()

        // 記錄分析數據
        if (this.redirectDialogStatus.drawAnalytics) {
          this.trackRedirectAnalytics()
        }

        // 執行導向
        this.$lineOpenWindow.open(targetUrl, '_blank')
      },
      getRedirectUrl() {
        // 移動裝置（iOS/Android/MacOS）使用相同URL
        if (
          this.$device.isIos ||
          this.$device.isMacOS ||
          this.$device.isAndroid ||
          this.isSamsungBrowser()
        ) {
          return 'https://www.xin-stars.com/QPPandroid?action=apk'
        }
        // PC版
        return 'https://www.xin-stars.com/Downloads'
      },
      trackRedirectAnalytics() {
        if (this.$device.isIos) {
          this.drawAnalytics('phone_ios')
        } else if (this.$device.isAndroid) {
          this.drawAnalytics('phone_android')
        } else if (this.$device.isDesktop) {
          this.drawAnalytics('phone_pc')
        }
      },
      isSamsungBrowser() {
        // 檢查是否為Samsung瀏覽器
        return navigator.userAgent.includes('samsungbrowser')
      }
    },
    watch: {
      redirectDialogStatus(val) {
        this.redirectDialogStatusLocal = val
      }
    }
  }
</script>
