<template>
  <div v-if="device || !customerBtnAnim">
    <v-badge
      v-if="getCustomerServiceNotyCount.enable"
      overlap
      color="error"
      :content="getCustomerServiceNotyCount.content"
      offset-x="20"
      offset-y="20"
    >
      <v-btn
        fab
        min-width="0"
        :loading="loading"
        :small="$vuetify.breakpoint.xsOnly || $device.isMobile"
        class="mb-2 pa-0 button-content--text"
        color="gradient-button"
        :disabled="maintainSystem[0].maintaining"
        @click="showCustomerServiceDialogStatusEvent"
      >
        <v-icon>mdi-face-agent</v-icon>
      </v-btn>
    </v-badge>
    <v-btn
      v-else
      fab
      min-width="0"
      :loading="loading"
      :small="$vuetify.breakpoint.xsOnly || $device.isMobile"
      class="mb-2 pa-0 button-content--text"
      color="gradient-button"
      :disabled="maintainSystem[0].maintaining"
      @click="showCustomerServiceDialogStatusEvent"
    >
      <v-icon>mdi-face-agent</v-icon>
    </v-btn>
  </div>
  <div v-else class="d-flex justify-end mr-2 mr-sm-n2">
    <v-badge
      v-if="getCustomerServiceNotyCount.enable"
      overlap
      color="error"
      :content="getCustomerServiceNotyCount.content"
      offset-x="20"
      offset-y="20"
    >
      <v-hover v-slot="{ hover }">
        <v-btn
          fab
          rounded
          min-width="0"
          color="gradient-button"
          class="mb-2 pa-0 button-content--text"
          :width="hover ? '94px' : $vuetify.breakpoint.xsOnly ? '40px' : '56px'"
          :small="$vuetify.breakpoint.xsOnly"
          :class="{
            'customer-btn-on-hover': hover && !$vuetify.breakpoint.xsOnly,
            'customer-btn': !hover && !$vuetify.breakpoint.xsOnly,
            'customer-btn-on-hover-xs': hover && $vuetify.breakpoint.xsOnly,
            'customer-btn-xs': !hover && $vuetify.breakpoint.xsOnly
          }"
          :disabled="maintainSystem[0].maintaining"
          @click="showCustomerServiceDialogStatusEvent"
        >
          <v-icon
            :class="{
              'ml-8': !hover && !$vuetify.breakpoint.xsOnly,
              'ml-n2': hover && !$vuetify.breakpoint.xsOnly,
              'ml-7': !hover && $vuetify.breakpoint.xsOnly,
              'ml-n1': hover && $vuetify.breakpoint.xsOnly
            }"
            >mdi-face-agent</v-icon
          >
          <span :class="{ 'customer-text-on-hover': hover, 'customer-text': !hover }">{{
            $t('customer_service')
          }}</span>
        </v-btn>
      </v-hover>
    </v-badge>
    <div v-else>
      <v-hover v-slot="{ hover }">
        <v-btn
          fab
          rounded
          min-width="0"
          color="gradient-button"
          class="mb-2 pa-0 button-content--text"
          :width="hover ? '94px' : $vuetify.breakpoint.xsOnly ? '40px' : '56px'"
          :small="$vuetify.breakpoint.xsOnly"
          :class="{
            'customer-btn-on-hover': hover && !$vuetify.breakpoint.xsOnly,
            'customer-btn': !hover && !$vuetify.breakpoint.xsOnly,
            'customer-btn-on-hover-xs': hover && $vuetify.breakpoint.xsOnly,
            'customer-btn-xs': !hover && $vuetify.breakpoint.xsOnly
          }"
          :disabled="maintainSystem[0].maintaining"
          @click="showCustomerServiceDialogStatusEvent"
        >
          <v-icon
            :class="{
              'ml-8': !hover && !$vuetify.breakpoint.xsOnly,
              'ml-n2': hover && !$vuetify.breakpoint.xsOnly,
              'ml-7': !hover && $vuetify.breakpoint.xsOnly,
              'ml-n1': hover && $vuetify.breakpoint.xsOnly
            }"
            >mdi-face-agent</v-icon
          >
          <span :class="{ 'customer-text-on-hover': hover, 'customer-text': !hover }">{{
            $t('customer_service')
          }}</span>
        </v-btn>
      </v-hover>
    </div>
  </div>
</template>

<script>
  import languageOption from '@/mixins/languageOption.js'
  import throttle from 'lodash/throttle'

  export default {
    name: 'customerServiceFabBtn',
    mixins: [languageOption],
    props: {
      headerHeight: {
        type: Number,
        default: 0
      }
    },
    data() {
      return {
        device: false,
        loading: false,
        overflowTop: 0,
        throttledUpdatePosition: null
      }
    },
    computed: {
      getCustomerServiceNotyCount() {
        const getCustomerServiceNotyCount = this.$store.getters['chat/customerServiceNoty']
        const result = {}
        if (getCustomerServiceNotyCount === 0) {
          result.enable = false
          result.content = 0
        } else if (getCustomerServiceNotyCount > 99) {
          result.enable = true
          result.content = '99+'
        } else {
          result.enable = true
          result.content = getCustomerServiceNotyCount
        }

        return result
      },
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      },
      orientation({ $store }) {
        return $store.getters['deviceManagement/getOrientation']
      }
    },
    mounted() {
      this.checkDevice()
      if (this.$vuetify.breakpoint.smAndDown) {
        // 在手機畫面，處理被覆蓋的客服按鈕
        this.updatePosition()
        this.throttledUpdatePosition = throttle(this.updatePosition, 200)
        window.addEventListener('scroll', this.throttledUpdatePosition)
      }
    },
    beforeDestroy() {
      if (this.$vuetify.breakpoint.smAndDown) {
        window.removeEventListener('scroll', this.throttledUpdatePosition)
      }
    },
    methods: {
      async showCustomerServiceDialogStatusEvent() {
        this.loading = true
        if (!this.maintainSystem[0].maintaining) {
          this.$nuxt.$emit('root:showCustomerServiceDialogStatusEvent', true)
        }
        this.loading = false
      },
      checkDevice() {
        const userAgent = this.$device.userAgent
        const isSafari = userAgent.indexOf('Safari') !== -1 && userAgent.indexOf('Version') !== -1
        const isIphone = userAgent.indexOf('iPhone') !== -1 && userAgent.indexOf('Version') !== -1
        const isIpadPro = isSafari && !isIphone && 'ontouchend' in document // 判斷是否為ipadPro
        this.device =
          this.$device.isMobile || this.$device.isSamsung || this.$device.isTablet || isIpadPro
      },
      updatePosition() {
        if (!this.orientation) {
          document.documentElement.style.removeProperty('--overflowTop')
          if (this.overflowTop) this.overflowTop = 0
          return
        }
        const button = this.$el?.querySelector('button')
        if (!button) return
        const btnsRect = this.$el?.querySelector('button').getBoundingClientRect()
        if (btnsRect.top < this.headerHeight) {
          const offsetDifference = Math.abs(btnsRect.top - this.headerHeight)
          this.overflowTop += offsetDifference
          document.documentElement.style.setProperty('--overflowTop', `${this.overflowTop}px`)
        }
      }
    }
  }
</script>
<style lang="scss" scoped>
  .customer-btn-on-hover {
    width: 94px !important;
    transition: all 0.1s linear;
  }
  .customer-btn {
    width: 56px !important;
    transition: all 0.1s linear;
    :deep(.v-btn__content) {
      pointer-events: none;
    }
  }
  .customer-btn-on-hover-xs {
    width: 67px !important;
    transition: all 0.1s linear;
  }
  .customer-btn-xs {
    width: 40px !important;
    transition: all 0.1s linear;
  }
  .customer-text-on-hover {
    visibility: visible;
    opacity: 1;
    transition-delay: 0.15s;
    transform: translate(5px);
  }
  .customer-text {
    visibility: hidden;
    opacity: 0;
    white-space: nowrap;
    transition: 0.15s;
    transform: translate(-30px);
  }
</style>
