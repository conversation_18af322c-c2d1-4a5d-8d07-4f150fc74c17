<template>
  <div>
    <audio id="audioPlayer" ref="audioPlayer" preload="auto" crossorigin="anonymous"></audio>
  </div>
</template>

<script>
  export default {
    name: 'musicPlayer',
    mixins: [require('~/mixins/music').default],
    data() {
      return {
        audioElement: null,
        audioContext: null,
        source: null, // 將 source 宣告為資料
        gainNode: null,
        readPlayStatus: false,
        initalPlayStatus: false
      }
    },
    computed: {
      isPlaying({ $store }) {
        return $store.getters['music/isPlaying']
      },
      tracks({ $store }) {
        return $store.getters['music/tracks']
      },
      currentTrack({ $store }) {
        return $store.getters['music/currentTrack']
      },
      volume({ $store }) {
        return $store.getters['music/volume'] / 100
      },
      isPlayByOrder({ $store }) {
        return $store.getters['music/isPlayByOrder']
      }
    },
    watch: {
      isPlaying: {
        handler(val) {
          if (this.audioElement)
            if (val) {
              this.playTrack()
            } else {
              this.stopTrack()
            }
        }
      },
      volume: {
        handler(val) {
          this.setVolume(val)
        }
      },
      currentTrack: {
        handler(val) {
          if (this.audioElement && this.readPlayStatus) {
            this.setTrack(val)
            this.playTrack()
          }
        }
      },
      isPlayByOrder: {
        handler(val) {
          if (val) {
            this.setTrack(0)
            this.playTrack()
          }
        }
      }
    },
    mounted() {
      this.audioElement = document.getElementById('audioPlayer')
      this.getMusicPreferenceFromLocalStroage()
      this.initalAudioContext()

      document.addEventListener('click', this.handleDocumentClick)
      document.addEventListener('touchmove', this.handleDocumentClick)
      document.addEventListener('touchstart', this.handleDocumentClick)
      document.addEventListener('touchend', this.handleDocumentClick)
      document.addEventListener('touchcancel', this.handleDocumentClick)

      //ios 解鎖屏後撥放音樂
      if (this.$device.isIos) this.$lifecycle.addEventListener('statechange', this.unlockScreen)
    },
    beforeDestroy() {
      if (this.$device.isIos) document.removeEventListener('statechange', this.unlockScreen)
    },
    methods: {
      nextTrack() {
        let nextIndex = this.currentTrack + 1
        if (nextIndex >= this.tracks.length) {
          nextIndex = 0
        }
        this.$store.commit('music/SET_CURRENT_TRACK', nextIndex)
      },
      playTrack() {
        const player = this.audioElement
        if (this.audioContext.state === 'suspended') {
          this.audioContext.resume()
        }
        player.load()
        player
          .play()
          .then(() => {
            if (!this.initalPlayStatus) this.removeUserEventListener()
            const timer = setInterval(() => {
              if (player.ended) {
                if (this.isPlayByOrder) this.nextTrack()
                else this.playTrack()
                clearInterval(timer)
              }
            }, 1000)
          })
          .catch(() => {
            // 如果遭到瀏覽器阻擋 且為第一次播放 將會重新嘗試播放一秒一次
            if (!this.initalPlayStatus)
              setTimeout(() => {
                this.playTrack()
              }, 1000)
          })
      },
      stopTrack() {
        const player = this.audioElement
        player.pause()
      },
      setTrack(index) {
        this.audioElement.src = process.env.IMAGE_URL + this.tracks[index].src
      },
      setVolume(val) {
        if (this.audioElement) {
          this.$nextTick(() => {
            this.gainNode.gain.value = val
          })
        }
      },
      handleDocumentClick() {
        this.readPlayStatus = true
        this.setTrack(this.currentTrack)
        this.$store.commit('music/SET_IS_PLAYING', true)
        this.setVolume(this.volume)
      },
      removeUserEventListener() {
        if (!this.audioElement.paused) {
          this.initalPlayStatus = true
          document.removeEventListener('click', this.handleDocumentClick)
          document.removeEventListener('touchmove', this.handleDocumentClick)
          document.removeEventListener('touchstart', this.handleDocumentClick)
          document.removeEventListener('touchend', this.handleDocumentClick)
          document.removeEventListener('touchcancel', this.handleDocumentClick)
        }
      },
      initalAudioContext() {
        this.audioContext = new (window.AudioContext || window.webkitAudioContext)()
        this.source = this.audioContext.createMediaElementSource(this.audioElement) // 存放 source 到資料中
        // 建立 GainNode 來控制音量
        this.gainNode = this.audioContext.createGain()
        this.source.connect(this.gainNode)

        // 將 GainNode 連接到 AudioContext 的 destination
        this.gainNode.connect(this.audioContext.destination)

        // 設置音量 (0.0 - 2.0)
        this.gainNode.gain.value = this.volume
      },
      unlockScreen(event) {
        const self = this
        if (event.oldState == 'passive' && event.newState == 'hidden') {
          self.playTrack()
        }
      }
    }
  }
</script>
