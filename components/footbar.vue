<template>
  <v-card tile class="mx-auto overflow-hidden">
    <v-bottom-navigation
      horizontal
      fixed
      background-color="transparent"
      :height="$device.isIos ? '70px' : '56px'"
    >
      <v-container class="bottom-bar-gradient pa-0" :class="{ 'pb-3': $device.isIos }">
        <v-row justify="center" no-gutters class="fill-height align-center flex-nowrap">
          <!-- game btn and part of role btn -->
          <v-col class="d-flex justify-center col-max-width h-100-percent">
            <v-bottom-sheet
              fullscreen
              :content-class="`pa-2 overflow-y-auto ${$UIConfig.footbar.menuBackgroundColor}`"
              v-model="bottomSheetStatus"
            >
              <template v-slot:activator="{ attrs }">
                <v-btn
                  elevation="0"
                  :disabled="
                    maintainSystem[0].maintaining ||
                    (gameCategory.length === 0 && roleCategory.length === 0)
                  "
                  text
                  v-bind="attrs"
                  @click="showMenu"
                  class="btn-min-height w-100 px-3 py-6-px"
                  :class="{ 'remove-hover-bg': isTouchDevice }"
                >
                  <v-badge :value="hasGuildNoty" color="error" dot offset-x="0" offset-y="11">
                    <div class="d-flex flex-column">
                      <span
                        class="material-symbols-outlined"
                        :class="{
                          'app-bar-button': !maintainSystem[0].maintaining,
                          'app-bar-disable-button': maintainSystem[0].maintaining
                        }"
                      >
                        notes
                      </span>
                      <span
                        :class="{
                          'app-bar-button': !maintainSystem[0].maintaining,
                          'app-bar-disable-button': maintainSystem[0].maintaining
                        }"
                      >
                        {{ $t('list') }}
                      </span>
                    </div>
                  </v-badge>
                </v-btn>
              </template>
              <!-- close btn -->
              <v-row no-gutters>
                <v-col cols="12" class="text-end pb-4">
                  <v-btn icon @click="bottomSheetStatus = false">
                    <v-icon>mdi-close</v-icon>
                  </v-btn>
                </v-col>
              </v-row>
              <v-list
                rounded
                :class="[
                  'overflow-x-hidden pa-0',
                  { 'notch-left': hasLeftNotch, 'notch-right': hasRightNotch }
                ]"
                :max-height="vListHeight"
              >
                <!-- game btn -->
                <v-row class="px-4 gutters-16">
                  <v-col
                    v-for="item in gameCategory"
                    v-show="item.enable"
                    :key="item.name"
                    cols="6"
                    sm="4"
                  >
                    <v-btn
                      height="56"
                      class="custom-btn w-100 ps-2 pe-0 rounded-lg gradient-side-bar-game text-none"
                      :class="$UIConfig.footbar.gameCategoryColor"
                      @click="item.dict === 'home' ? goHome() : goGameCategory(item.code)"
                    >
                      <span class="text-content">
                        {{ item.dict === 'home' ? $t(item.dict) : $t(item.dict + '_short') }}
                      </span>
                      <lottie-vue-player
                        class="game-category-img"
                        :key="`${animationLoop}-${item.img}`"
                        :src="animationOptions(item.img)"
                        :autoplay="animationLoop"
                        :loop="animationLoop"
                        style="width: 56px; height: 56px"
                      />
                    </v-btn>
                  </v-col>
                </v-row>
                <!-- divider  -->
                <gradientDivider divider-type="squareToSquare" :square-size="10" class="pa-4" />
                <div v-if="!isQCStation">
                  <!-- news -->
                  <v-list-item
                    class="overflow-hidden justify-start mb-2"
                    :class="{
                      'v-item--active v-list-item--active primary--text': $route.path === '/news'
                    }"
                    @click.prevent="goOtherPage('/news?type=2&page=1')"
                  >
                    <v-list-item-icon class="align-self-center">
                      <span class="material-symbols-outlined"> campaign </span>
                    </v-list-item-icon>
                    <v-list-item-content class="py-0">
                      <v-list-item-title>
                        {{ $t('announcement') }}
                      </v-list-item-title>
                    </v-list-item-content>
                  </v-list-item>
                  <!-- role btn -->
                  <template v-for="(item, index) in roleCategory">
                    <v-list-item
                      v-if="item.name === 'redeem'"
                      class="overflow-hidden justify-start mh-auto"
                      :class="{ 'pb-2': index !== roleCategory.length - 1 }"
                      :key="`redeem-${item.name}`"
                      :value="item.name"
                      @click.prevent="showRedeemDialog()"
                    >
                      <v-list-item-icon class="h-24-px align-self-center">
                        <span class="material-symbols-outlined">
                          {{ item.icon }}
                        </span>
                      </v-list-item-icon>
                      <v-list-item-content class="py-0">
                        <v-list-item-title>
                          {{ $t(item.name) }}
                        </v-list-item-title>
                      </v-list-item-content>
                    </v-list-item>
                    <v-list-group
                      v-else-if="item.name === 'guild'"
                      :class="{
                        'remove-hover-bg': isTouchDevice,
                        'pb-2': index !== roleCategory.length - 1,
                        'primary--text': isSelectedGuildChildPage
                      }"
                      :key="`guild-${item.name}`"
                      :append-icon="isAllDropdownDisable ? '' : 'mdi-chevron-down'"
                      :value="item.active"
                      @click.capture.stop="clickDropdown(item)"
                    >
                      <template v-slot:prependIcon>
                        <v-badge
                          v-if="hasGuildNoty"
                          color="error"
                          offset-x="-40"
                          offset-y="16"
                          dot
                        />
                        <span class="material-symbols-outlined">
                          {{ item.icon }}
                        </span>
                      </template>
                      <template v-slot:activator>
                        <v-list-item-content class="py-0">
                          <v-list-item-title> {{ $t(item.name) }}</v-list-item-title>
                        </v-list-item-content>
                      </template>
                      <v-list-item-group :value="selectedItem" color="primary">
                        <v-list-item
                          v-show="child.name !== 'guild_info' || hasGuild"
                          v-for="(child, index) in item.items"
                          class="overflow-hidden justify-start mh-auto"
                          :class="{
                            'mt-2': index === 0,
                            'mb-2': index !== item.items.length - 1
                          }"
                          :disabled="isAllDropdownDisable"
                          :key="child.name"
                          :value="child.name"
                          @click="goOtherPage(child.path)"
                        >
                          <v-list-item-icon class="h-24-px align-self-center">
                            <v-badge
                              v-if="child.name === 'guild_info' && hasGuildNoty"
                              color="error"
                              offset-x="-40"
                              offset-y="16"
                              dot
                              right
                            />
                          </v-list-item-icon>
                          <v-list-item-content class="py-0 overflow-visible">
                            <v-list-item-title class="overflow-visible">
                              <div class="d-flex justify-space-between">
                                <span> {{ $t(child.name) }}</span>
                                <v-badge
                                  :value="child.name === 'guild_info' && guildChat > 0"
                                  color="error"
                                  left
                                  offset-x="0"
                                  offset-y="20"
                                  :content="guildChat > 99 ? '99+' : guildChat"
                                />
                              </div>
                            </v-list-item-title>
                          </v-list-item-content>
                        </v-list-item>
                      </v-list-item-group>
                    </v-list-group>
                  </template>
                </div>

                <!-- background music -->
                <v-list-item
                  class="overflow-hidden justify-start"
                  @click.prevent="showMusicPlayerDialog"
                >
                  <v-list-item-icon class="align-self-center">
                    <span class="material-symbols-outlined"> music_note </span>
                  </v-list-item-icon>
                  <v-list-item-content class="py-0">
                    <v-list-item-title>
                      {{ $t('background_music') }}
                    </v-list-item-title>
                  </v-list-item-content>
                </v-list-item>
              </v-list>
            </v-bottom-sheet>
          </v-col>
          <template>
            <!-- pay -->
            <v-col v-if="!isQCStation" class="d-flex justify-center col-max-width h-100-percent">
              <v-btn
                elevation="0"
                :disabled="maintainSystem[0].maintaining || paymentDialogDisable"
                text
                color="transparent"
                @click="goPayment()"
                class="btn-min-height w-100 px-3 py-6-px"
              >
                <div class="d-flex flex-column align-center">
                  <v-img
                    v-if="$UIConfig.footbar.paymentIcon"
                    class="material-symbols-outlined"
                    :class="{
                      'app-bar-button': !maintainSystem[0].maintaining,
                      'app-bar-disable-button': maintainSystem[0].maintaining
                    }"
                    :src="getImage($UIConfig.footbar.paymentIcon)"
                    width="24"
                    height="24"
                    center
                  />
                  <span
                    v-else
                    class="material-symbols-outlined"
                    :class="{
                      'app-bar-button': !maintainSystem[0].maintaining,
                      'app-bar-disable-button': maintainSystem[0].maintaining
                    }"
                  >
                    paid
                  </span>
                  <span
                    :class="{
                      'app-bar-button': !maintainSystem[0].maintaining,
                      'app-bar-disable-button': maintainSystem[0].maintaining
                    }"
                  >
                    {{ $t('stored_mall') }}
                  </span>
                </div>
              </v-btn>
            </v-col>
            <!-- mail -->
            <v-col class="d-flex justify-center col-max-width h-100-percent">
              <v-btn
                elevation="0"
                :disabled="maintainSystem[0].maintaining"
                text
                color="transparent"
                @click="checkMaintenanceForBtn('root:mailDialogStatus')"
                class="btn-min-height w-100 px-3 py-6-px"
              >
                <div class="d-flex flex-column">
                  <v-badge
                    v-if="getNotyCount > 0"
                    overlap
                    color="error"
                    :content="getNotyCount > 99 ? '99+' : getNotyCount"
                  >
                    <span
                      class="material-symbols-outlined"
                      :class="{
                        'app-bar-button': !maintainSystem[0].maintaining,
                        'app-bar-disable-button': maintainSystem[0].maintaining
                      }"
                    >
                      mail
                    </span>
                  </v-badge>
                  <span
                    v-else
                    class="material-symbols-outlined"
                    :class="{
                      'app-bar-button': !maintainSystem[0].maintaining,
                      'app-bar-disable-button': maintainSystem[0].maintaining
                    }"
                  >
                    mail
                  </span>
                  <span
                    :class="{
                      'app-bar-button': !maintainSystem[0].maintaining,
                      'app-bar-disable-button': maintainSystem[0].maintaining
                    }"
                  >
                    {{ $t('mail') }}
                  </span>
                </div>
              </v-btn>
            </v-col>
            <!-- chat -->
            <v-col v-if="!isQCStation" class="d-flex justify-center col-max-width h-100-percent">
              <v-btn
                elevation="0"
                :disabled="maintainSystem[0].maintaining"
                text
                color="transparent"
                @click="checkMaintenanceForBtn('root:showChatRoomDialogStatus')"
                class="btn-min-height w-100 px-3 py-6-px"
              >
                <div class="d-flex flex-column">
                  <v-badge
                    v-if="getChatNotyCount.enable"
                    overlap
                    color="error"
                    :content="getChatNotyCount.content"
                  >
                    <span
                      class="material-symbols-outlined"
                      :class="{
                        'app-bar-button': !maintainSystem[0].maintaining,
                        'app-bar-disable-button': maintainSystem[0].maintaining
                      }"
                    >
                      sms
                    </span>
                  </v-badge>
                  <span
                    v-else
                    class="material-symbols-outlined"
                    :class="{
                      'app-bar-button': !maintainSystem[0].maintaining,
                      'app-bar-disable-button': maintainSystem[0].maintaining
                    }"
                  >
                    sms
                  </span>
                  <span
                    :class="{
                      'app-bar-button': !maintainSystem[0].maintaining,
                      'app-bar-disable-button': maintainSystem[0].maintaining
                    }"
                  >
                    {{ $t('chat_session') }}
                  </span>
                </div>
              </v-btn>
            </v-col>
          </template>
        </v-row>
      </v-container>
    </v-bottom-navigation>
  </v-card>
</template>
<script>
  const STATION = process.env.STATION
  import scroll from '~/mixins/scroll'
  import chat from '~/mixins/chatroom/chat'
  import preLoginAction from '@/mixins/preLoginAction.js'
  import utilWhiteList from '@/utils/whiteList.js'
  import cards_playing from '~/assets/lottie/gameCategory/cards_playing.json'
  import fish from '~/assets/lottie/gameCategory/fish.json'
  import gamepad_square from '~/assets/lottie/gameCategory/gamepad_square.json'
  import roulette from '~/assets/lottie/gameCategory/roulette.json'
  let slot_machine
  try {
    slot_machine = require(`~/assets/lottie/gameCategory/${STATION}/slot_machine.json`)
  } catch {
    slot_machine = require('~/assets/lottie/gameCategory/slot_machine.json')
  }
  import sword_cross from '~/assets/lottie/gameCategory/sword_cross.json'
  import star_shooting from '~/assets/lottie/gameCategory/star_shooting.json'
  import images from '~/mixins/images'
  import orientation from '@/mixins/orientation.js'

  export default {
    name: 'FootBar',
    components: {
      gradientDivider: () => import('~/components/gradientDivider.vue')
    },
    mixins: [scroll, chat, preLoginAction, orientation, images],
    data() {
      return {
        bottomSheetStatus: false,
        getBackBtnStatus: false,
        isTouchDevice: true,
        vListHeight: 'calc(100% - 52px - 16px)',
        animationLoop: true
      }
    },
    computed: {
      paymentDialogDisable({ $store }) {
        if (!process.client) return false
        const userName = $store.getters['role/userName']
        const level = $store.getters['role/vipLevel']
        const url = window.location.origin
        return utilWhiteList.paymentDialogDisable(userName, level, url)
      },
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      },
      selfBalance({ $store }) {
        return $store.getters['role/balance']
      },
      getNotyCount() {
        return this.$store.getters['mail/getNotyCount']
      },
      gameCategory({ $store }) {
        const gameCategoryTmp = $store.getters['gameProvider/gameCategory']
        return gameCategoryTmp.filter((game) => game.enable)
      },

      profileList({ $store }) {
        return $store.getters['role/playerProfile']
      },
      isLogin() {
        return this.$store.getters['role/isLogin']
      },
      payVip({ $store }) {
        return $store.getters[`${STATION}/payment/vipState`]
      },
      selfLimit({ $store }) {
        return $store.getters['role/limit']
      },
      isInErrorPage({ $store }) {
        return $store.getters['isInErrorPage']
      },
      station() {
        return STATION
      },
      hasGuildNoty({ $store }) {
        const acceptCount = $store.getters['guild/guildAcceptList'].length
        const hasGuild = $store.getters['guild/hasGuild']
        const chatList = $store.getters['chat/chats']
        const guildChat = chatList.find((x) => x.title === 'guild_chat')
        const guildChatCount = guildChat === undefined ? 0 : guildChat.noty
        return (acceptCount > 0 || guildChatCount > 0) && this.$UIConfig.lock.guild && hasGuild
      },
      guildChat({ $store }) {
        const chatList = $store.getters['chat/chats']
        const hasGuild = $store.getters['guild/hasGuild']
        const guildChat = chatList.find((x) => x.title === 'guild_chat')
        return guildChat === undefined || !hasGuild ? 0 : guildChat.noty
      },
      countPerCol() {
        return this.$vuetify.breakpoint.xsOnly ? 2 : 3
      },
      roleCategory({ $store }) {
        this.isAllDropdownDisable && this.$store.commit('role/SET_ROLE_CATEGORY_ALL_INACTIVE')
        return $store.getters['role/roleCategory']
      },
      isAllDropdownDisable() {
        return !this.isLogin || this.maintainSystem[0].maintaining
      },
      isQCStation() {
        return this.station === 'qc_overseas' || this.station === 'qc_domestic'
      },
      hasGuild({ $store }) {
        return $store.getters['guild/hasGuild']
      },
      isSelectedGuildChildPage() {
        const group = this.roleCategory.find((item) => item.name === 'guild')
        return group?.items.some((item) => item.name === this.selectedItem) ?? false
      },
      selectedItem() {
        const group = this.roleCategory.find((item) => item.name === 'guild')
        const match = group?.items.find((child) => this.$route.path === child.path)
        return match ? match.name : null
      }
    },
    watch: {
      $route: {
        handler(to) {
          // 目前 roleCategory 的下拉選單，都是登入前不可展開
          // 且可 active 的選項，都存在於下拉選單中
          if (this.isLogin) {
            this.checkCurrentPage(to.path)
          }
        }
      },
      isLogin: {
        handler(isLogin) {
          // 登入時，公會下拉選單預設展開
          isLogin &&
            this.$store.commit('role/SET_ROLE_CATEGORY_STATUS', { name: 'guild', status: true })
        }
      }
    },
    mounted() {
      this.$nuxt.$on('root:showMusicPlayerDialogStatus', this.handleMusicDialog)
    },
    beforeDestroy() {
      this.$nuxt.$off('root:showMusicPlayerDialogStatus', this.handleMusicDialog)
    },
    methods: {
      async checkMaintenanceForBtn(rootEmitName) {
        const self = this

        if (self.maintainSystem[0].maintaining) return

        if (
          rootEmitName !== 'root:showPaymentDialogStatus' &&
          (!this.$UIConfig.paymentIndex.showVipTab ||
            rootEmitName !== 'root:showVipPaymentDialogStatus')
        ) {
          // 離開Web遊戲館服務
          self.$wsClient.send(
            self.$wsPacketFactory.exitService(self.$xinConfig.WEB_GAME_SERVICE.ID)
          )
          self.$store.commit('xinProtocol/SET_SERVICE_IDS', {
            serviceId: self.$xinConfig.WEB_GAME_SERVICE.ID,
            enable: false,
            connected: false,
            sendId: 0,
            receiveId: 0
          })
        }

        if (!self.isLogin) {
          // 依據『rootEmitName』決定要存入哪些『登入前動作紀錄』
          switch (rootEmitName) {
            case 'root:showVipPaymentDialogStatus':
              if (this.$UIConfig.paymentIndex.showVipTab) {
                self.setPreLoginAction('goPayment', self.goPayment)
              }
              break
            case 'root:showPaymentDialogStatus':
              self.setPreLoginAction('goPayment', self.goPayment)
              break
            case 'root:mailDialogStatus':
              self.setPreLoginAction('mail', self.checkMaintenanceForBtn, 'root:mailDialogStatus')
              break
            case 'root:showChatRoomDialogStatus':
              self.setPreLoginAction(
                'chatRoom',
                self.checkMaintenanceForBtn,
                'root:showChatRoomDialogStatus'
              )
              break
            default: {
              break
            }
          }

          rootEmitName = 'root:showLoginDialogStatus'
        }

        if (rootEmitName == 'root:mailDialogStatus') {
          self.$store.commit('mail/SET_SHOW_NOTY_STATUS', true)
          self.$store.commit('mail/SET_SCROLL_TOP_STATUS', true)
          self.$nuxt.$emit(rootEmitName, { show: true, name: '' })
        } else if (rootEmitName === 'root:showLoginDialogStatus') {
          this.$nuxt.$emit(rootEmitName, { show: true })
        } else {
          self.$nuxt.$emit(rootEmitName, true)
        }
      },
      async goGameCategory(categoryCode) {
        this.bottomSheetStatus = false
        this.scollToTop()
        if (this.maintainSystem[0].maintaining) return
        let path = ''
        const isQcStation =
          process.env.STATION === 'qc_overseas' || process.env.STATION === 'qc_domestic'

        if (isQcStation) {
          const qcStation = this.$route.params.qcStation
          path = `/${qcStation}/game?page=1&gameCategory=${categoryCode}&gameSortType=3&providerId=0&searchWord=`
        } else {
          path =
            '/game?page=1&gameCategory=' + categoryCode + '&gameSortType=3&providerId=0&searchWord='
        }
        this.$router.push({ path: this.localePath(path) })
      },
      formatPrice(value) {
        return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      },
      isJson(str) {
        try {
          JSON.parse(str)
        } catch (e) {
          return false
        }
        return true
      },
      async goPayment() {
        if (this.isLogin) {
          await this.$store.dispatch('role/updateUserDetail')
        }
        // 使用 UIConfig 來判斷是否要顯示 VIP 優惠窗
        if (this.payVip && this.selfLimit !== 0 && this.$UIConfig.paymentIndex.showVipTab) {
          this.checkMaintenanceForBtn('root:showVipPaymentDialogStatus')
        } else {
          this.checkMaintenanceForBtn('root:showPaymentDialogStatus')
        }
      },
      async goHome() {
        this.bottomSheetStatus = false
        this.scollToTop()
        if (this.maintainSystem[0].maintaining) return

        if (this.isInErrorPage) {
          this.$nuxt.error(null)
          this.$router.push(this.localePath('/'))
        } else {
          this.$router.push(this.localePath('/'))
        }
      },
      async showRedeemDialog() {
        this.bottomSheetStatus = false
        if (this.maintainSystem[0].maintaining) {
          this.scollToTop()
          return
        }

        if (this.isLogin) {
          this.$wsClient.send(this.$wsPacketFactory.fatchBalance())

          // 離開Web遊戲館服務
          this.$wsClient.send(
            this.$wsPacketFactory.exitService(this.$xinConfig.WEB_GAME_SERVICE.ID)
          )
          this.$store.commit('xinProtocol/SET_SERVICE_IDS', {
            serviceId: this.$xinConfig.WEB_GAME_SERVICE.ID,
            enable: false,
            connected: false,
            sendId: 0,
            receiveId: 0
          })

          this.$nuxt.$emit('root:showRedeemDialogStatus', true)
        } else {
          this.$nuxt.$emit('root:showLoginDialogStatus', { show: true })
          this.setPreLoginAction('redeem', this.showRedeemDialog)
        }
      },
      async clickDropdown(group, isPreLoginAction) {
        if (!this.isLogin) {
          this.bottomSheetStatus = false
          if (this.maintainSystem[0].maintaining) {
            this.scollToTop()
            return
          }
          this.$nuxt.$emit('root:showLoginDialogStatus', { show: true })
          this.setPreLoginAction('dropdown', this.clickDropdown, group, true)
          this.$store.commit('role/SET_ROLE_CATEGORY_STATUS', { name: 'guild', status: true })
          return
        }

        if (isPreLoginAction) {
          this.$wsClient.send(this.$wsPacketFactory.fatchBalance())

          // 目前頁面，是否為下拉選單頁面
          const isDropdownPage = group.items.some((item) => item.path === this.$route.path)
          // 下拉選單第一個選項的路徑，以公會為例，就是公會資訊
          const path = group.items[0].path
          !isDropdownPage && this.$router.push({ path: this.localePath(path) })
          this.scollToTop()
        }

        // 開關下拉選單
        this.$store.commit('role/SET_ROLE_CATEGORY_STATUS', { name: group.name })
      },
      async goOtherPage(path) {
        this.bottomSheetStatus = false
        this.scollToTop()
        if (this.maintainSystem[0].maintaining) return

        this.$router.push({ path: this.localePath(path) })
      },
      // 用來判斷是否為觸控裝置
      checkDevice() {
        this.isTouchDevice = 'ontouchend' in document
      },
      // 判斷目前路由是否為選單(roleCategory)頁面
      checkCurrentPage(toPath) {
        // 取得有子選單的資料(扁平化資料結構)
        const flatItems = this.roleCategory.flatMap((category) =>
          category.items
            ? category.items.map((item) => ({
                categoryName: category.name,
                itemName: item.name,
                path: item.path
              }))
            : []
        )
        // 取得沒有子選單、有路由的資料
        const noNestItems = this.roleCategory
          .filter((item) => item.path)
          .map((item) => ({
            categoryName: null,
            itemName: item.name,
            path: item.path
          }))
        const allItems = [...flatItems, ...noNestItems]
        const matchItem = allItems.find((item) => item.path === toPath)

        if (matchItem) {
          this.$store.commit('role/SET_ROLE_CATEGORY_STATUS', {
            name: matchItem.categoryName,
            status: true
          })
        } else {
          this.$store.commit('role/SET_ROLE_CATEGORY_ALL_INACTIVE')
        }
      },
      async showMusicPlayerDialog() {
        this.bottomSheetStatus = false
        if (this.maintainSystem[0].maintaining) {
          this.scollToTop()
          return
        }
        this.$nuxt.$emit('root:showMusicPlayerDialogStatus', true)
      },
      async showMenu() {
        // 開啟前檢查維護狀態，若維護中，則不開啟選單
        if (this.maintainSystem[0].maintaining) return
        this.bottomSheetStatus = true
      },
      animationOptions(imgPath) {
        const animationName = imgPath.split('.')[0]
        const lottieAnimation = {
          cards_playing,
          fish,
          gamepad_square,
          roulette,
          slot_machine,
          star_shooting,
          sword_cross
        }

        return (
          JSON.stringify(lottieAnimation[animationName]) ||
          JSON.stringify(lottieAnimation.cards_playing)
        )
      },
      handleMusicDialog(isOpen) {
        this.$nextTick(() => {
          this.animationLoop = !isOpen
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  $default-content-color: map-get($colors, default-content);
  $primary: map-get($colors, 'primary');
  .btn-min-height {
    min-height: 50px;
  }
  .app-bar-disable-button {
    background: #ffffffb0;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  .col-max-width {
    max-width: 168px;
  }
  .py-6-px {
    padding-block: 6px !important;
  }
  .v-btn {
    &.black {
      color: #000000;
    }
    &.white {
      color: #ffffff;
    }
    .game-category-img {
      position: absolute;
      right: 0;
      filter: drop-shadow(0px 3px 5px rgba(0, 0, 0, 0.12))
        drop-shadow(0px 9px 2px rgba(0, 0, 0, 0.14)) drop-shadow(0px 5px 4px rgba(0, 0, 0, 0.2));
    }
  }
  .v-list-item:not(:is(.v-list-item--active, .v-list-item--disabled)) {
    color: $default-content-color;
    .v-list-item__icon,
    .v-icon {
      color: $default-content-color;
    }
  }
  // 避免因重複點擊相同選項，導致 active 狀態被移除
  .v-list-item--active {
    pointer-events: none;
  }

  // 因下拉選單的子選項沒有icon，用來補足高度
  .h-24-px {
    height: 24px;
  }
  .v-list-group ::v-deep {
    .v-list-item:not(:is(.v-list-item--active, .v-list-item--disabled)) {
      color: $default-content-color;
      .v-list-item__icon,
      .v-icon {
        color: $default-content-color;
      }
    }
    & > .v-list-item {
      margin-bottom: 0 !important;
    }
    &.remove-hover-bg > .v-list-item:hover::before {
      opacity: 0;
    }
    &.primary--text {
      .v-list-group__header {
        .v-list-item__content,
        .v-list-item__icon,
        .v-icon {
          color: $primary !important;
        }
      }
    }
  }
  .theme--dark.v-bottom-navigation .v-btn.remove-hover-bg {
    color: transparent !important;
  }
  .custom-btn {
    position: relative;
    display: flex !important;
    align-items: center !important;
    justify-content: flex-start !important;
    overflow: hidden !important;

    ::v-deep .v-btn__content {
      flex-shrink: 1;

      .text-content {
        max-width: calc(100% - 56px);
        white-space: normal;
        word-break: break-word;
        line-height: 1.2;
        text-align: left;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
      .vue-lottie-player {
        background: transparent;
      }
      .lf-spinner {
        display: none;
      }
    }
  }
  .gutters-16 {
    margin: -8px;
    > div {
      padding: 8px;
    }
  }
  @media (orientation: landscape) {
    .notch-left {
      padding-left: env(safe-area-inset-left) !important;
    }
    .notch-right {
      padding-right: env(safe-area-inset-right) !important;
    }
  }
</style>
