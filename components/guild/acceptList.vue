<template>
  <v-row no-gutters id="guild-list-data-table">
    <v-col cols="12">
      <template>
        <v-data-table
          class="custom-table grey-6"
          fixed-header
          @pagination="scrollToTop"
          @update:page="handlePageChange"
          mobile-breakpoint="0"
          :hide-default-footer="breakpoint.xsOnly"
          :height="isCard && !breakpoint.xsOnly ? '' : tableHeightXS"
          :items-per-page="itemsPage"
          :headers="guildListHeaders"
          :page="currentPage"
          :items="guildInfoShowList"
          :footer-props="{
            'items-per-page-text': $t('items_per_page'),
            'items-per-page-all-text': $t('all'),
            'page-text': `{0}-{1} ${$t('total_page')} {2} ${$t('quantity')}`,
            'items-per-page-options': itemsPerPageOptions
          }"
        >
          <template v-if="guildInfoShowList.length === 0" v-slot:body>
            <tbody :height="isCard ? height - 48 : 'auto'">
              <tr class="v-data-table__empty-wrapper">
                <td colspan="3">
                  <span class="text-center">
                    {{ $t('no_data') }}
                  </span>
                </td>
              </tr>
            </tbody>
          </template>
          <!--body-->
          <template v-slot:item="{ item }">
            <tr>
              <td class="px-0">
                <v-menu offset-y absolute content-class="easyPlayer-custom-border">
                  <template v-slot:activator="{ on, attrs }">
                    <div
                      @click="setPlayerInfo(item.name)"
                      v-bind="attrs"
                      v-on="on"
                      class="d-flex justify-center cursor-pointer"
                    >
                      <v-badge
                        bordered
                        bottom
                        :color="item.onlines ? 'success' : 'offline'"
                        dot
                        offset-x="24"
                        offset-y="20"
                      >
                        <v-list-item-avatar>
                          <v-img :src="item.avator" contain @error="errorImgHandler(item)">
                            <template v-slot:placeholder>
                              <v-row class="fill-height ma-0" align="center" justify="center">
                                <v-img :src="defaultImg" contain />
                              </v-row>
                            </template>
                          </v-img>
                        </v-list-item-avatar>
                      </v-badge>
                    </div>
                  </template>
                  <easyPlayerInfo
                    tile
                    report
                    is-card
                    action-bar
                    only-coin
                    :player-info="playerInfo"
                    style="min-width: 300px"
                    badge-type="relation"
                  />
                </v-menu>
              </td>
              <td class="px-0" style="min-width: 180px">
                <v-menu offset-y absolute content-class="easyPlayer-custom-border">
                  <template v-slot:activator="{ on, attrs }">
                    <div
                      @click="setPlayerInfo(item.name)"
                      v-bind="attrs"
                      v-on="on"
                      class="px-4 h-100 d-flex align-center"
                    >
                      <span class="text-center custom-text-noto text-subtitle-1 primary--text">
                        {{ item.name }}
                      </span>
                    </div>
                  </template>
                  <easyPlayerInfo
                    tile
                    report
                    is-card
                    action-bar
                    only-coin
                    :player-info="playerInfo"
                    style="min-width: 300px"
                    badge-type="relation"
                  />
                </v-menu>
              </td>
              <td class="px-0">
                <div class="d-flex justify-center px-4 py-3">
                  <v-btn class="mr-3" icon color="error" @click="onRejectCLick(item.name)">
                    <v-icon> mdi-close-circle </v-icon>
                  </v-btn>
                  <v-btn
                    icon
                    :color="$UIConfig.guildAcceptList.showAcceptNotyDialog ? 'primary' : 'success'"
                    @click="onAcceptClick(item)"
                  >
                    <span
                      v-if="$UIConfig.guildAcceptList.showAcceptNotyDialog"
                      class="material-symbols-outlined"
                    >
                      forward_to_inbox
                    </span>
                    <v-icon v-else> mdi-check-circle </v-icon>
                  </v-btn>
                </div>
              </td>
            </tr>
          </template>
          <!--body-->
          <template v-if="breakpoint.xsOnly" v-slot:footer="{ props: { pagination } }">
            <div class="v-data-footer">
              <v-row no-gutters>
                <v-col>
                  <div class="v-data-footer__select d-flex justify-start ml-3">
                    <span> {{ $t('items_per_page') }}</span>
                    <v-select
                      class="py-0 mt-3 mb-3"
                      v-model="select"
                      hide-details
                      height="32"
                      @input="onSelect"
                      :items="pagePaginationitem(itemsPerPageOptions)"
                    ></v-select>
                    <span class="v-data-footer__pagination">
                      {{ pagePagination(pagination) }}
                    </span>
                  </div>
                </v-col>
              </v-row>
              <v-row no-gutters>
                <v-col>
                  <v-btn
                    class="v-data-footer__icons-before"
                    icon
                    :disabled="pagination.pageStart === 0"
                    @click="currentPage = pagination.page - 1 === 0 ? 1 : pagination.page - 1"
                  >
                    <v-icon dark> mdi-chevron-left </v-icon>
                  </v-btn>
                  <v-btn
                    class="v-data-footer__icons-after"
                    icon
                    :disabled="pagination.pageStop === pagination.itemsLength"
                    @click="
                      currentPage =
                        pagination.page + 1 === pagination.pageCount
                          ? pagination.pageCount
                          : pagination.page + 1
                    "
                  >
                    <v-icon dark> mdi-chevron-right </v-icon>
                  </v-btn>
                </v-col>
              </v-row>
            </div>
          </template>
        </v-data-table>
      </template>
    </v-col>
    <guildAcceptDialog
      v-if="showGuildAcceptDialogStatus"
      :player-info="playerInfo"
      :show-guild-accept-dialog-status.sync="showGuildAcceptDialogStatus"
    />
  </v-row>
</template>

<script>
  import images from '~/mixins/images'
  import guildMgr from '~/mixins/guildMgr'
  import cloneDeep from 'lodash/cloneDeep'
  import scssLoader from '~/mixins/scssLoader.js'
  import relationship from '~/mixins/relationship'
  export default {
    name: 'acceptList',
    components: {
      easyPlayerInfo: () => import('~/components/player_info/easyPlayerInfo'),
      guildAcceptDialog: () => import('~/components/guild/guildAcceptDialog')
    },
    props: {
      isCard: {
        type: Boolean,
        default: true
      },
      isInfoPage: {
        type: Boolean,
        default: false
      },
      height: {
        type: [Number, String],
        default: 288
      },
      itemsPerPage: {
        type: Number,
        default: 15
      },
      itemsPerPageOptions: {
        type: Array,
        default: () => [10, 20, 30, -1]
      }
    },
    mixins: [scssLoader, guildMgr, images, relationship],
    data() {
      return {
        defaultImg: process.env.IMAGE_URL + '/photo_stickers/default.png',
        guildInfoShowList: [],
        select: this.itemsPerPage,
        itemsPage: this.itemsPerPage,
        currentPage: 1,
        showGuildAcceptDialogStatus: false,
        guildListHeaders: [
          {
            text: this.$t('avatar'),
            value: 'avatar',
            align: 'center',
            width: '20%',
            class: 'grey-4 primary--text px-4 py-3',
            sortable: false
          },
          {
            text: this.$t('nickname'),
            value: 'name',
            width: '40%',
            class: 'grey-4 primary--text px-4 py-3',
            sortable: false
          },
          {
            text: this.$t('guild_acccept_operation'),
            value: 'operation',
            align: 'center',
            width: '40%',
            class: 'grey-4 primary--text px-4 py-3',
            sortable: false
          }
        ],
        playerInfo: {
          username: '',
          level: 0,
          levelVip: 0,
          money: 0,
          thumbUrl: '',
          online: false,
          guildName: ''
        }
      }
    },
    watch: {
      guildAcceptList: {
        async handler() {
          this.guildInfoShowList = this.guildAcceptList
          for (var item of this.guildInfoShowList) {
            const avatarThumb = await this.getUserUrl(item)
            const userDetal = await this.$store.dispatch('social/getUserDetail', item.name)
            item.onlines = userDetal.online
            item.avator = avatarThumb
          }
        }
      }
    },
    computed: {
      friendList({ $store }) {
        return $store.getters['social/friendList']
      },
      ownName({ $store }) {
        return $store.getters['role/userName']
      },
      selfGuildName({ $store }) {
        return $store.getters['guild/guildName']
      },
      guildAcceptList({ $store }) {
        const defaultAcceptList = $store.getters['guild/guildAcceptList']
        const guildMemberList = defaultAcceptList.map((item) => {
          const defaultAvator = process.env.IMAGE_URL + '/photo_stickers/default.png'
          const username = item
          const userOnline = false
          return { name: username, avator: defaultAvator, onlines: userOnline }
        })
        return guildMemberList
      },
      tableHeightXS() {
        return `${window.innerHeight - 165}px`
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    async created() {
      this.guildInfoShowList = this.guildAcceptList
      for (var item of this.guildInfoShowList) {
        const avatarThumb = await this.getUserUrl(item)
        const userDetal = await this.$store.dispatch('social/getUserDetail', item.name)

        item.onlines = userDetal.online
        item.avator = avatarThumb
      }
    },
    methods: {
      pagePaginationitem(itemArray) {
        let newItemArray = cloneDeep(itemArray)
        newItemArray[newItemArray.findIndex((x) => x === -1)] = this.$t('all')
        return newItemArray
      },
      onSelect() {
        if (this.select === this.$t('all')) this.itemsPage = -1
        else this.itemsPage = this.select
      },
      scrollToTop() {
        try {
          window.scrollTo({ top: 0, behavior: 'auto' })
        } catch (error) {
          console.log(error)
        }
      },
      handlePageChange(page) {
        this.currentPage = page
      },
      pagePagination(pagination) {
        return pagination.pageCount === 0
          ? '-'
          : `${pagination.pageStart + 1}-${pagination.pageStop} ${this.$t('total_page')}
                ${pagination.itemsLength} ${this.$t('quantity')}`
      },
      async onRejectCLick(userName) {
        this.rejectGuildMembership(userName)
      },
      async onAcceptClick(item) {
        if (this.$UIConfig.guildAcceptList.showAcceptNotyDialog) {
          const res = await this.getPlayerGuild(item.name)
          if (res.hasGuild) {
            this.$notify.warning(this.$t('lang_82', { 0: item.name }))
          } else {
            this.playerInfo = item
            this.showGuildAcceptDialogStatus = true
          }
        } else {
          this.playerInfo.usrname = item.name
          await this.acceptGuildMembership(item.name, '')
        }
      },
      errorImgHandler(item) {
        item.avator = this.defaultImg
      },
      async getUserUrl(item) {
        const index = this.friendList.findIndex((item) => item.username === item.name)
        const userData = { userName: item.name }
        return index !== -1
          ? this.friendList[index].thumbUrl
          : await this.$store.dispatch('role/getThumbUrl', userData)
      },
      async setPlayerInfo(userName) {
        // 先獲取空數據，以免顯示上一個用戶資訊
        this.playerInfo = {
          username: '',
          level: 0,
          levelVip: 0,
          money: 0,
          thumbUrl: '',
          online: false,
          guildName: ''
        }
        const role = await this.getPlayerData(userName)
        this.playerInfo.username = role.username
        this.playerInfo.level = role.level
        this.playerInfo.levelVip = role.levelVip
        this.playerInfo.money = role.money
        this.playerInfo.thumbUrl = role.thumbUrl
        this.playerInfo.online = role.online
        this.playerInfo.guildName = role.guildName
        this.updatePlayerInfo(role)
      },
      async updatePlayerInfo(userdata) {
        await this.$store.dispatch('social/setSingleFriendStatus', userdata)
      }
    }
  }
</script>

<style lang="scss">
  $grey-4: map-get($colors, 'grey-4');
  $grey-6: map-get($colors, 'grey-6');
  #guild-list-data-table {
    .character-info-avatar-badge {
      position: relative;
      .badge-border {
        position: absolute;
        top: 0;
      }
    }
    .v-data-table-header-mobile {
      th {
        background: $grey-4 !important;
      }
    }
    .v-data-table {
      tbody {
        tr {
          &:hover {
            background: $grey-4 !important;
          }
        }
      }
    }
    .v-data-table__wrapper {
      min-height: 401px;
      overflow: hidden !important;
    }
    @media screen and (max-width: 599px) {
      .custom-table {
        thead {
          display: table;
          width: 100%;
        }
        tbody {
          display: block;
          overflow-y: auto;
          width: 100%;
          max-height: calc(100vh - 193px);
          tr {
            width: 100%;
            display: inline-table;
            td:first-child {
              width: 20%;
            }
            td:nth-child(2) {
              width: 40%;
            }
            td:last-child {
              width: 40%;
            }
          }
        }
      }
    }
  }
</style>
