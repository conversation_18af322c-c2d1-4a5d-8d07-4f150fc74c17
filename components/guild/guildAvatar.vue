<template>
  <v-row
    no-gutters
    justify="center"
    align="center"
    class="guild-avatar-layout"
    :style="{
      width: contentSize + 'px',
      height: contentSize + 'px'
    }"
  >
    <v-file-input id="choosePhoto" accept="image/*" @change="setPhoto(image)" v-model="image">
    </v-file-input>

    <div>
      <v-img
        contain
        aspect-ratio="1"
        :src="imgUrl"
        :max-width="avatarSize"
        :width="avatarSize"
        :height="avatarSize"
        class="avatar rounded-pill"
        :class="{ 'mb-3': !middle && !small, 'mb-1': middle, 'cursor-pointer': badge }"
        @click="choosePhoto"
      >
        <template v-slot:placeholder>
          <v-img
            contain
            aspect-ratio="1"
            :src="defaultImg"
            :max-width="avatarSize"
            :width="avatarSize"
            :height="avatarSize"
            class="avatar rounded-pill"
            :class="{ 'cursor-pointer': badge }"
            @click="choosePhoto"
          />
        </template>
      </v-img>

      <v-img
        contain
        :src="getImage('guild/badge_frame.png')"
        :width="borderSize"
        :height="borderSize"
        class="avatar-border"
        :class="{ 'cursor-pointer': badge }"
        @click="choosePhoto"
      >
        <badge v-if="badge" right="10px" bottom="10px" icon="photo_camera" :is-block="true"></badge>
      </v-img>
    </div>
    <uploadPhotoMethodDialog
      v-if="showUploadTotemMethodDialogStatus"
      :show-upload-totem-method-dialog-status.sync="showUploadTotemMethodDialogStatus"
    />
  </v-row>
</template>

<script>
  import images from '~/mixins/images'
  import { debounce } from 'lodash'
  export default {
    components: {
      badge: () => import(`~/components/badge`),
      uploadPhotoMethodDialog: () => import('~/components/guild/uploadTotemMethodDialog')
    },

    name: 'guildAvatar',
    mixins: [images],
    props: {
      small: { type: Boolean, default: false },
      middle: { type: Boolean, default: false },
      noUpload: { type: Boolean, default: false },
      guildMail: { type: Boolean, default: false },
      frame: {
        type: Boolean,
        default: false
      },
      guildId: {
        type: Number,
        default: 0
      },
      badge: {
        type: Boolean,
        default: false
      },
      edit: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        defaultImg: require('~/assets/image/guild/badge_preset.png'),
        imgUrl: '',
        showUploadTotemMethodDialogStatus: false,
        //用於選擇圖片
        image: null
      }
    },
    watch: {
      guildId: {
        async handler(val) {
          this.imgUrl = await this.getGuildUrl(val)
        }
      },
      updateGuildAvatar: {
        async handler() {
          this.imgUrl = await this.getGuildUrl(this.guildId)
        }
      }
    },
    computed: {
      borderSize({ small, middle, guildMail }) {
        let size = 120
        if (small) {
          size = 20
        }
        if (guildMail) {
          size = 30
        }
        if (middle) {
          size = 40
        }
        return size
      },
      avatarSize({ small, middle, guildMail }) {
        let size = 106
        if (small) {
          size = 17
        }
        if (guildMail) {
          size = 28
        }
        if (middle) {
          size = 34
        }
        return size
      },
      contentSize() {
        let size = this.borderSize
        if (!this.frame) {
          size = this.avatarSize
        }
        return size
      },

      updateGuildAvatar({ $store }) {
        return $store.getters['guild/updateGuildAvatar']
      },
      guildTotemTemp({ $store }) {
        return $store.getters['guild/guildTotemTemp']
      },
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      }
    },
    async mounted() {
      this.imgUrl = await this.getGuildUrl(this.guildId)
      this.debouncedChoosePhoto = debounce(this.showFilePicker, 500)
    },
    methods: {
      async getGuildUrl(guildId) {
        if (this.guildTotemTemp && this.edit) return this.guildTotemTemp
        const guildThumbName = `星城公會_${guildId}`
        const userData = { userName: guildThumbName, getCache: false }
        const userUrl = process.env.IMAGE_URL + '/photo_stickers/default.png'
        const thumbUrl = await this.$store.dispatch('role/getThumbUrl', userData)
        return userUrl === thumbUrl ? this.defaultImg : thumbUrl
      },
      showEditPhotoDialog() {
        this.$nuxt.$emit('root:showEditTotemDialogStatus', true)
      },
      async setPhoto(img) {
        // 上傳文件類型是否為圖片
        if (this.maintainSystem[0].maintaining) return

        const maxSizeInMB = 10
        const maxSizeInBytes = maxSizeInMB * 1024 * 1024
        let hasError = false

        // 檢查檔案格式
        if (!img.type.startsWith('image/') || img.type.endsWith('gif')) {
          this.$notify.error(this.$t('file_error'))
          hasError = true
        }

        // 檢查檔案大小
        if (img.size > maxSizeInBytes) {
          this.$notify.error(this.$t('size_limit', { size: maxSizeInMB + 'MB' }))
          hasError = true
        }

        // 只有所有檢查都通過才進入上傳流程
        if (!hasError) {
          this.$store.commit('role/SET_SELECT_PHOTO', img)
          this.showEditPhotoDialog()
        }
      },
      //選擇圖片
      async choosePhoto() {
        if (this.maintainSystem[0].maintaining) return
        if (this.badge) {
          this.debouncedChoosePhoto()
        }
      },
      showFilePicker() {
        document.getElementById('choosePhoto').value = ''
        document.getElementById('choosePhoto').click()
      }
    }
  }
</script>

<style lang="scss">
  .guild-avatar-layout {
    position: relative;
    height: 120px;
    width: 100%;

    .avatar-border {
      overflow: hidden;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);

      .v-responsive__content {
        display: flex;
        justify-items: center;
        align-items: center;
      }

      &:not(.no-upload) {
        .upload-icon {
          display: none;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
        }
      }
    }

    .avatar {
      &.brand {
        top: 50%;
        left: 50%;
        overflow: hidden;
        position: absolute;
        transform: translate(-50%, -50%);

        .v-responsive__content {
          position: absolute;
          height: 100%;
          top: 0;
          right: 0;
        }

        &:not(.no-upload) {
          .upload-icon {
            display: none;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
          }
        }
      }
    }

    &.small {
      width: 50px;
      height: 50px;
      overflow: hidden;
    }

    &.middle {
      width: 100px;
      height: 100px;
      overflow: hidden;
    }

    .v-file-input {
      display: none;
    }
  }
</style>
