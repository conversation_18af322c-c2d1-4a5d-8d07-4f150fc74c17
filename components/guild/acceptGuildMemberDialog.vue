<template>
  <div>
    <v-dialog
      v-model="showGuildAcceptTmp"
      :fullscreen="breakpoint.xsOnly"
      width="600px"
      height="497px"
      persistent
      scrollable
      :content-class="breakpoint.xsOnly ? '' : 'rounded-lg'"
    >
      <v-card
        class="pa-0"
        elevation="0"
        :color="breakpoint.xsOnly ? 'grey-6' : 'dialog-fill'"
        :height="contentHeight"
        max-height="90vh"
      >
        <customDialogTitle :title="$t('accept_list').toUpperCase()" @closeDialog="closeDialog" />
        <v-card-text class="h-100-percent px-0 px-sm-6">
          <div class="pt-0 pt-sm-5">
            <acceptList
              is-card
              :height="tableHeight"
              :items-per-page="10"
              :items-per-page-options="[10, 20, 30, -1]"
              :guild-accept-list="guildMemberFilterList"
            />
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
  import hiddenScrollHtml from '@/mixins/hiddenScrollHtml.js'
  import relationship from '@/mixins/relationship.js'
  import scssLoader from '@/mixins/scssLoader.js'
  import images from '~/mixins/images'
  export default {
    name: 'acceptGuildMemberDialog',
    mixins: [relationship, scssLoader, images, hiddenScrollHtml],
    components: {
      acceptList: () => import('~/components/guild/acceptList'),
      customDialogTitle: () => import('~/components/customDialogTitle')
    },
    props: {
      showGuildAcceptDialogStatus: { type: Boolean, required: true, default: false }
    },
    data() {
      return {
        showGuildAcceptTmp: this.showGuildAcceptDialogStatus,
        guildMemberFilterList: [],
        rules: [
          (val) =>
            !/[^\u4e00-\u9fa5a-zA-Z0-9]/.test(val) || this.$t('no_special_characters_allowed')
        ]
      }
    },
    created() {},
    async mounted() {},
    async destroyed() {
      if (this.maintainSystem[0].maintaining) return
    },
    computed: {
      station({ $store }) {
        return $store.getters['station']
      },
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      },

      contentHeight() {
        return 560
      },
      tableHeight() {
        return 401
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    watch: {
      showGuildAcceptDialogStatus: {
        async handler(status) {
          if (this.maintainSystem[0].maintaining) {
            return
          }
          this.showGuildAcceptTmp = status
        }
      }
    },
    methods: {
      closeDialog() {
        this.showGuildAcceptTmp = false
        this.$emit('update:showGuildAcceptDialogStatus', false)
      }
    }
  }
</script>

<style lang="scss" scoped>
  $primary-variant-3: map-get($colors, 'primary-variant-3');
  $dialog-fill: map-get($colors, 'dialog-fill');
  $primary: map-get($colors, 'primary');
  .v-data-table ::v-deep {
    tbody {
      tr:hover {
        background-color: map-get($colors, 'grey-4');
      }
    }
  }
  .guild-info-gackground {
    background: rgba(0, 0, 0, 0.12);
  }
  .guild-info-gackground-xs {
    background: $dialog-fill;
  }
  .v-tabs-items {
    background-color: transparent !important;
  }
  .a-link {
    color: rgba(255, 255, 255, 0.6) !important;
  }
  .v-tab--active {
    color: $primary !important;
  }
  .w-130-px {
    width: 130px !important;
  }
  .h-100-percent {
    height: 100% !important;
  }
  .w-150-px {
    width: 150px !important;
  }
</style>
