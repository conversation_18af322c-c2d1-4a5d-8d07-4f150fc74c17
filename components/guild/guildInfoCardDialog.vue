<template>
  <div>
    <v-dialog
      v-model="showGuildInfoCardTmp"
      :fullscreen="breakpoint.xsOnly"
      width="800px"
      height="884px"
      persistent
      scrollable
      :content-class="breakpoint.xsOnly ? '' : 'rounded-lg'"
    >
      <v-card class="pa-0" elevation="0" color="dialog-fill" :max-height="contentHeight">
        <customDialogTitle :title="$t('guild_info').toUpperCase()" @closeDialog="closeDialog" />

        <v-card-text class="h-100-percent pa-4 pa-sm-6">
          <v-card
            :class="{
              'guild-info-gackground': !breakpoint.xsOnly,
              'guild-info-gackground-xs': breakpoint.xsOnly
            }"
            flat
          >
            <!-- character -->
            <guildLobbyCardInfo
              :guild-caption="showGuildInfoCardDialogStatus.info.guildCaption"
              :guild-name="showGuildInfoCardDialogStatus.info.guildName"
              :guild-power="showGuildInfoCardDialogStatus.info.guildPower"
              :guild-online-members="showGuildInfoCardDialogStatus.info.guildOnlineMembers"
              :guild-members="showGuildInfoCardDialogStatus.info.guildMembers"
              :guild-rank="showGuildInfoCardDialogStatus.info.guildRank"
              :guild-id="showGuildInfoCardDialogStatus.info.guildId"
              :guild-fund="showGuildInfoCardDialogStatus.info.guildFund"
              :has-guild="showGuildInfoCardDialogStatus.info.hasGuild"
            />
          </v-card>
          <v-divider class="my-7 mt-sm-4 mb-sm-8"></v-divider>
          <v-row no-gutters>
            <v-col class="mt-2" cols="12" sm="5" md="5" lg="5" xl="5">
              <span
                class="custom-text-noto text-subtitle-1 default-content--text font-weight-bold"
                >{{ $t('guild_member_list') }}</span
              >
            </v-col>
            <v-col cols="12" sm="7" md="7" lg="7" xl="7">
              <v-text-field
                v-model="searchWord"
                ref="searchWord"
                append-icon="mdi-magnify"
                clearable
                outlined
                rounded
                dense
                :rules="rules"
                :label="$t('find_member')"
                class="input-height"
                @click:clear="clearSearchEvent"
                @click:append="searchWordEvent"
                @keydown.enter="searchWordEvent"
              /> </v-col
          ></v-row>
          <!-- character-detail -->
          <guildInfoList
            is-card
            :items-per-page="10"
            :guild-info-name="showGuildInfoCardDialogStatus.info.guildName"
            :show-guild-rank="showGuildInfoCardDialogStatus.info.guildRank"
            :items-per-page-options="$UIConfig.guildInfoPage.infoListOptions"
            :guild-info-list="guildMemberFilterList"
          />
        </v-card-text>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
  import hiddenScrollHtml from '@/mixins/hiddenScrollHtml.js'
  import relationship from '@/mixins/relationship.js'
  import scssLoader from '@/mixins/scssLoader.js'
  import images from '~/mixins/images'
  const STATION = process.env.STATION
  export default {
    name: 'guildInfoCardDialog',
    mixins: [relationship, scssLoader, images, hiddenScrollHtml],
    components: {
      guildLobbyCardInfo: () => import(`~/components_station/${STATION}/guild/guildLobbyCardInfo`),
      guildInfoList: () => import(`~/components_station/${STATION}/guild/guildInfoList`),
      customDialogTitle: () => import('~/components/customDialogTitle')
    },
    props: {
      showGuildInfoCardDialogStatus: { type: Object, default: { show: false, info: {} } }
    },
    data() {
      return {
        showGuildInfoCardTmp: this.showGuildInfoCardDialogStatus.show,
        searchWord: '',
        guildMemberFilterList: [],
        rules: [
          (val) =>
            !/[^\u4e00-\u9fa5a-zA-Z0-9]/.test(val) || this.$t('no_special_characters_allowed')
        ]
      }
    },
    created() {},
    async mounted() {
      this.guildMemberFilterList = this.showGuildInfoCardDialogStatus.info.guildMemberInfoList
    },
    async destroyed() {
      if (this.maintainSystem[0].maintaining) return
    },
    computed: {
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      },
      station({ $store }) {
        return $store.getters['station']
      },
      contentHeight() {
        return 624
      },
      infoHeight() {
        if (this.$vuetify.breakpoint.xsOnly) return 325
        if (this.showGuildInfoCardDialogStatus.info.guildCaption !== '') return 280
        return 220
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    watch: {
      showGuildInfoCardDialogStatus: {
        async handler(status) {
          if (this.maintainSystem[0].maintaining) {
            return
          }
          this.showGuildInfoCardTmp = status.show
        }
      },
      'showGuildInfoCardDialogStatus.info.guildMemberInfoList': {
        async handler() {
          this.searchWordEvent()
        }
      }
    },
    methods: {
      closeDialog() {
        this.showGuildInfoCardTmp = false
        this.$nuxt.$emit('root:guildDialogStatus', { show: false, info: {} })
      },

      stringIsNullOrEmpty(str) {
        return str === '' || str === null || str === undefined
      },
      async searchWordEvent() {
        const ruleSuccess = this.$refs.searchWord.validate()
        if (!ruleSuccess) return
        if (this.stringIsNullOrEmpty(this.searchWord))
          this.guildMemberFilterList = this.showGuildInfoCardDialogStatus.info.guildMemberInfoList
        else {
          this.guildMemberFilterList =
            this.showGuildInfoCardDialogStatus.info.guildMemberInfoList.filter((item) => {
              return item.name.includes(this.searchWord)
            })
        }
      },
      async clearSearchEvent() {
        this.searchWord = ''
        this.guildMemberFilterList = this.showGuildInfoCardDialogStatus.info.guildMemberInfoList
      }
    }
  }
</script>

<style lang="scss" scoped>
  $primary-variant-3: map-get($colors, 'primary-variant-3');
  $dialog-fill: map-get($colors, 'dialog-fill');
  $primary: map-get($colors, 'primary');
  .v-data-table ::v-deep {
    tbody {
      tr:hover {
        background-color: map-get($colors, 'grey-4');
      }
    }
  }
  .guild-info-gackground {
    background: rgba(0, 0, 0, 0.12);
  }
  .guild-info-gackground-xs {
    background: $dialog-fill;
  }
  .v-tabs-items {
    background-color: transparent !important;
  }
  .a-link {
    color: rgba(255, 255, 255, 0.6) !important;
  }
  .v-tab--active {
    color: $primary !important;
  }
  .w-130-px {
    width: 130px !important;
  }
  .h-100-percent {
    height: 100% !important;
  }
  .w-150-px {
    width: 150px !important;
  }
</style>
