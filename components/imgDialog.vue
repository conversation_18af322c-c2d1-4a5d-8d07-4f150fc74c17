<template>
  <v-dialog v-model="localImgDialogStatus" persistent width="auto" content-class="img-dialog" eager>
    <div
      class="d-inline-flex"
      :class="{ 'img-default': imgResult === 'default', 'img-fail': imgResult === 'fail' }"
    >
      <img :src="localImg" class="img-class" :alt="altText" @error="setDefaultImg" />
    </div>
    <div
      class="rounded-pill cursor-pointer icon-layout d-flex justify-center align-center mt-2 mr-2"
      @click="closeDialog"
    >
      <span class="material-symbols-outlined default-content--text"> close </span>
    </div>
  </v-dialog>
</template>
<script>
  export default {
    name: 'ImgDialog',
    props: {
      imgDialogStatus: {
        type: Boolean,
        default: false
      },
      img: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        localImg: null,
        stickerDefaultWebp: require('~/assets/image/chatroom/sticker/chat_sticker_download_fail.webp'),
        stickerDefaultPng: require('~/assets/image/chatroom/sticker/chat_sticker_download_fail.png'),
        imgResult: 'origin', // 總共分三種：origin(原圖)、default(預設圖)、fail(無圖片)
        altText: ''
      }
    },
    watch: {
      imgDialogStatus: {
        handler(val) {
          if (val) {
            this.altText = this.$t('image_error')
            this.localImg = this.img
          }
        }
      }
    },
    computed: {
      localImgDialogStatus() {
        return this.imgDialogStatus
      }
    },
    methods: {
      async setDefaultImg() {
        if (this.localImg === this.img) {
          // 第一次錯誤，改用 WebP 預設圖
          this.imgResult = 'default'
          this.localImg = this.stickerDefaultWebp
        } else if (this.localImg === this.stickerDefaultWebp) {
          // 第二次錯誤，改用 PNG 預設圖
          this.imgResult = 'default'
          this.localImg = this.stickerDefaultPng
        } else {
          // 預設圖全部顯示失敗
          this.imgResult = 'fail'
        }
      },
      closeDialog() {
        // 關閉對話框並重置圖片，避免開關彈窗出現非預期畫面
        this.$emit('update:imgDialogStatus', false)
        this.altText = ''
        this.localImg = null
        this.imgResult = 'origin'
      }
    }
  }
</script>
<style scoped lang="scss">
  $dialog-fill: map-get($colors, 'dialog-fill');
  ::v-deep .img-dialog {
    position: relative;
    display: inline-flex;
    margin: 0 !important;
    background-color: rgba($dialog-fill, 0.6);
    .img-default,
    .img-fail {
      width: min(300px, 90vw, 90vh);
      justify-content: center;
      align-items: center;
      aspect-ratio: 1/1;
    }
    .img-default .img-class {
      width: 100px;
      height: 100px;
    }
    .img-fail .img-class {
      line-height: 1rem;
    }
    .img-class {
      max-width: 90vw;
      max-height: 90vh;
      object-fit: contain;
      @supports (width: 90dvw) {
        max-width: 90dvw;
      }
      @supports (height: 90dvh) {
        max-height: 90dvh;
      }
    }
    .icon-layout {
      height: 36px;
      width: 36px;
      background-color: rgba(0, 0, 0, 0.5);
      position: absolute;
      right: 0;
      top: 0;
    }
  }
</style>
