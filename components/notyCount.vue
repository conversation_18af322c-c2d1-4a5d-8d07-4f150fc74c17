<template>
  <!--個位數維持正圓，十位數起恢復原邏輯推算-->
  <v-sheet small color="error" class="rounded-pill" :class="{ 'circle-fix': noty < 10 }">
    <span class="pa-2">{{ noty > 99 ? '99+' : noty }}</span>
  </v-sheet>
</template>
<script>
  export default {
    name: 'notyCount',
    props: {
      noty: {
        type: Number,
        default: 0
      }
    }
  }
</script>
<style lang="scss" scoped>
  .rounded-pill.circle-fix {
    width: 20px;
    height: 20px;
    position: relative;
    span {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
</style>
