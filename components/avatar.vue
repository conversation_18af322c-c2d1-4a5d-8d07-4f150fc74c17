<template>
  <v-row
    no-gutters
    justify="center"
    align="center"
    class="components-avatar-layout"
    :class="{ small: small, middle: middle }"
    :style="{
      width: contentSize + 'px',
      height: contentSize + 'px'
    }"
  >
    <v-img
      contain
      aspect-ratio="1"
      :src="imgUrl"
      :max-width="avatarSize"
      :width="avatarSize"
      :height="avatarSize"
      class="avatar avatar-content text-center mx-auto"
    >
      <template v-slot:placeholder>
        <v-img
          contain
          aspect-ratio="1"
          :src="defaultImg"
          :max-width="avatarSize"
          :width="avatarSize"
          :height="avatarSize"
          class="avatar text-center mx-auto"
        />
      </template>
    </v-img>

    <v-img
      v-if="frame"
      contain
      :src="frameImageSrc"
      :width="borderSize"
      :height="borderSize"
      :class="[{ 'no-upload': noUpload }, 'avatar-border']"
    >
      <template v-slot:placeholder>
        <v-skeleton-loader type="image" height="100%" />
      </template>
      <v-icon v-if="!noUpload" color="primary" class="upload-icon"> mdi-upload </v-icon>
    </v-img>
  </v-row>
</template>

<script>
  import images from '~/mixins/images'
  import facebook from '~/mixins/facebook'
  export default {
    mixins: [images, facebook],
    props: {
      small: { type: Boolean, default: false },
      middle: { type: Boolean, default: false },
      noUpload: { type: Boolean, default: false },
      src: { type: String, default: '' },
      guest: { type: Boolean, default: false },
      frame: {
        type: Boolean,
        default: false
      },
      isCard: {
        type: Boolean,
        default: false
      },
      isInfoPage: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        defaultImg: process.env.IMAGE_URL + '/photo_stickers/default.png',
        imgUrl: '',
        currentVipLevel: 0
      }
    },
    computed: {
      vipLevel() {
        if (this.isCard) {
          return this.$store.getters['social/selectPlayer'].vipLevel
        } else if (this.isInfoPage) {
          return this.$store.getters['role/vipLevel']
        }
        return 0
      },
      frameImageSrc() {
        const imagePath = this.getImage(`avatar_frame/rankframe_${this.currentVipLevel}.png`)
        return imagePath
      },
      borderSize({ small, middle }) {
        let size = 180
        if (small) {
          size = 50
        }

        if (middle) {
          size = 100
        }

        return size
      },
      avatarSize({ small, middle }) {
        let size = 120
        if (small) {
          size = 40
        }

        if (middle) {
          size = 65
        }

        return size
      },
      contentSize() {
        let size = this.borderSize
        if (!this.frame) {
          size = this.avatarSize
        }

        return size
      },
      facebookId() {
        return this.$store.getters['role/facebookId']
      },
      accessToken() {
        return this.$store.getters['role/accessToken']
      },
      selectPlayer({ $store }) {
        return $store.getters['social/selectPlayer']
      },
      thumbUrl({ $store }) {
        return $store.getters['role/thumbUrl']
      },
      friendList({ $store }) {
        return $store.getters['social/friendList']
      },
      blockList({ $store }) {
        return $store.getters['social/blockList']
      }
    },
    mountend() {},
    methods: {},
    watch: {
      thumbUrl: {
        async handler(val) {
          let url = ''
          if (this.isCard) {
            url = this.selectPlayer.thumbUrl
          } else {
            //星城照片網址
            let xinUrl = val
            url = xinUrl
            //臉書登入 照片
            if (this.facebookId && this.facebookId.length > 0) {
              const stickerSize = this.small ? 'small' : this.middle ? 'normal' : 'large'
              url = await this.getUserAvatar(stickerSize)
            }
          }
          this.imgUrl = url
        },
        immediate: true
      },
      selectPlayer: {
        handler(val) {
          if (this.isCard) {
            let player = this.friendList.filter(function (value) {
              return value.username.toLowerCase() == val.username.toLowerCase()
            })
            if (player.length === 0) {
              player = this.blockList.filter(function (value) {
                return value.username.toLowerCase() == val.username.toLowerCase()
              })
            }

            this.imgUrl = player[0].thumbUrl
          }
        }
      },
      vipLevel: {
        handler(newVal) {
          this.currentVipLevel = newVal
        },
        immediate: true
      }
    }
  }
</script>
