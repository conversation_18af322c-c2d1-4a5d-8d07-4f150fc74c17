<template>
  <div>
    <v-list flat color="transparent">
      <v-expansion-panels accordion flat multiple v-model="openPanelList">
        <v-expansion-panel v-for="(chat, idx) in chatsFilter" :key="chat.name">
          <v-expansion-panel-header
            v-if="chat.showPanelHeader"
            class="py-0 px-4"
            disable-icon-rotate
          >
            <template v-slot:actions>
              <span
                class="panel-arrow material-symbols-rounded"
                :class="{ 'panel-open': isPanelOpen(idx) }"
              >
                arrow_right
              </span>
              <span class="ml-1 default-content--text custom-text-noto text-caption">
                {{ $t(chat.name) }}
              </span>
            </template>
          </v-expansion-panel-header>
          <v-expansion-panel-content>
            <v-list-item
              v-for="item in chat.list"
              v-show="!(item.id === 1 && !hasGuild)"
              :key="item.id"
              :value="item.id"
              :class="{ 'selected-list-item': item.id === selectedChat }"
              @click="selectedChatItem(item.id)"
            >
              <v-list-item-content
                :class="isWhisper(item.id, item.title) && !item.online ? 'friend-offline' : ''"
              >
                <!-- min-width: 0 是為了讓text-overflow在flex box當中可以生效 -->
                <v-row no-gutters align="center" class="flex-nowrap" style="min-width: 0">
                  <!-- icon -->
                  <div>
                    <template v-if="checkIsWhisper(item.id)">
                      <v-img width="40" height="40" :src="getImage('chatroom/' + item.img)" />
                    </template>
                    <template v-else-if="item.isOfficial">
                      <v-img width="40" height="40" :src="defaultImg" />
                    </template>
                    <template v-else>
                      <div v-show="item.isFriend">
                        <stopListGroup>
                          <v-menu
                            right
                            max-width="100%"
                            content-class="easyPlayer-custom-border"
                            z-index="300"
                          >
                            <template v-slot:activator="{ on, attrs }">
                              <v-badge
                                bordered
                                bottom
                                overlap
                                :color="item.online ? 'success' : 'offline'"
                              >
                                <template v-slot:badge>
                                  <v-icon size="12"> mdi-account-multiple </v-icon>
                                </template>
                                <v-img
                                  width="40"
                                  height="40"
                                  :src="item.img"
                                  v-bind="attrs"
                                  v-on="on"
                                  @click="setPlayerInfo(convertMessage(item.title))"
                                >
                                  <template v-slot:placeholder> <placeHolder /></template>
                                </v-img>
                              </v-badge>
                            </template>
                            <easyPlayerInfo
                              tile
                              report
                              is-card
                              action-bar
                              only-coin
                              :player-info="playerInfo"
                              style="min-width: 300px"
                              badge-type="relation"
                            />
                          </v-menu>
                        </stopListGroup>
                      </div>
                      <div v-show="!item.isFriend">
                        <stopListGroup>
                          <v-menu right max-width="100%">
                            <template v-slot:activator="{ on, attrs }">
                              <v-img
                                width="40"
                                height="40"
                                :src="item.img"
                                v-bind="attrs"
                                v-on="on"
                                @click="setPlayerInfo(convertMessage(item.title))"
                              >
                                <template v-slot:placeholder> <placeHolder /></template>
                              </v-img>
                            </template>
                            <easyPlayerInfo
                              tile
                              report
                              is-card
                              action-bar
                              only-coin
                              :player-info="playerInfo"
                              style="min-width: 300px"
                            />
                          </v-menu>
                        </stopListGroup>
                      </div>
                    </template>
                  </div>
                  <!-- title -->
                  <!-- ellipsis color -->
                  <div
                    class="pl-4 text-overflow"
                    :class="{
                      'default-content--text': checkIsWhisper(item.id),
                      'name-private--text': !checkIsWhisper(item.id)
                    }"
                  >
                    <span
                      class="text-subtitle-1 custom-text-noto"
                      :class="{
                        'default-content--text': checkIsWhisper(item.id),
                        'name-private--text': !checkIsWhisper(item.id)
                      }"
                    >
                      {{ convertMessage(item.title) }}
                    </span>
                  </div>
                </v-row>
              </v-list-item-content>
              <v-list-item-action>
                <!-- 群聊(ex:全頻、公會頻) -->
                <template v-if="item.isOfficial">
                  <notyCount v-if="item.noty > 0" :noty="item.noty" />
                </template>
                <!-- 非好友 -->
                <template v-else-if="!item.isFriend">
                  <v-row no-gutters justify="end">
                    <notyCount v-if="item.noty > 0" :noty="item.noty" />
                    <v-tooltip attach content-class="pinTooltip">
                      <template v-slot:activator="{ on, attrs }">
                        <v-icon
                          class="ml-3"
                          color="btn-disable"
                          v-bind="attrs"
                          v-on="{ ...on, click: ($event) => $event.stopPropagation() }"
                        >
                          mdi-pin-off
                        </v-icon>
                      </template>
                      <span>{{ $t('add_as_friend_before_pin') }}</span>
                    </v-tooltip>
                  </v-row>
                </template>
                <!-- 好友 -->
                <template v-else-if="item.pin !== null">
                  <v-row no-gutters justify="end">
                    <notyCount
                      v-if="item.noty > 0"
                      :noty="item.noty"
                      :class="item.online ? '' : 'friend-offline'"
                    />
                    <v-hover v-slot="{ hover }">
                      <v-icon
                        class="ml-3"
                        :color="getPinColor(item.pin, hover)"
                        @click.stop="setFriendPin(item.title, item.pin)"
                      >
                        {{
                          hover && item.pin && $vuetify.breakpoint.lgAndUp
                            ? 'mdi-pin-off'
                            : 'mdi-pin'
                        }}
                      </v-icon>
                    </v-hover>
                  </v-row>
                </template>
              </v-list-item-action>
            </v-list-item>
          </v-expansion-panel-content>
        </v-expansion-panel>
      </v-expansion-panels>
    </v-list>
  </div>
</template>
<script>
  import chat from '~/mixins/chatroom/chat'
  import relationship from '~/mixins/relationship'
  import converter from '~/mixins/converter'
  import images from '~/mixins/images'
  import guildMgr from '~/mixins/guildMgr'
  export default {
    name: 'chatList',
    mixins: [chat, relationship, images, guildMgr, converter],
    components: {
      placeHolder: () => import('~/components/imgPlaceholder'),
      notyCount: () => import('~/components/notyCount'),
      easyPlayerInfo: () => import('~/components/player_info/easyPlayerInfo'),
      stopListGroup: () => import('~/components/chatroom/stopListGroup')
    },
    props: {
      chats: {
        type: Array,
        default: () => []
      },
      keyword: {
        type: String,
        default: ''
      },
      showChatRoomDialogStatus: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        defaultImg: process.env.IMAGE_URL + '/photo_stickers/default.png',
        playerInfo: {
          username: '',
          level: 0,
          levelVip: 0,
          money: 0,
          thumbUrl: '',
          online: false,
          guildName: ''
        },
        openPanelList: []
      }
    },
    computed: {
      chatsFilter() {
        // 定義聊天對象類別(official:群聊、pin:已釘選、other:其他)
        const chatCategory = {
          official: [],
          pin: [],
          other: []
        }

        this.chats
          .map((chat) => ({
            ...chat,
            title: this.checkIsWhisper(chat.id) ? this.$t(chat.title) : chat.title
          }))
          .filter((chat) => {
            if (this.checkIsWhisper(chat.id)) return true
            if (chat.isBlock || (this.whisperOnlyFriend && !this.checkIsFriend(chat.key)))
              return false
            return chat.title.toLowerCase().includes(this.keyword.toLowerCase())
          })
          .forEach((chat) => {
            if (chat.isOfficial) {
              chatCategory.official.push(chat)
            } else if (chat.pin) {
              chatCategory.pin.push(chat)
            } else {
              chatCategory.other.push(chat)
            }
          })

        return [
          { name: 'official', showPanelHeader: false, list: chatCategory.official },
          {
            name: 'pinned_friend',
            showPanelHeader: chatCategory.pin.length > 0,
            list: chatCategory.pin
          },
          {
            name: 'chat_session',
            showPanelHeader: chatCategory.other.length > 0 && chatCategory.pin.length !== 0,
            list: chatCategory.other
          }
        ]
      },
      whisperOnlyFriend({ $store }) {
        return $store.state.chat.setting.whisper.onlyFriend
      },
      selectedChat({ $store }) {
        return $store.getters['chat/selectedChat']
      }
    },
    watch: {
      showChatRoomDialogStatus: {
        handler(val) {
          val && this.setAllPanelListOpen()
        },
        immediate: true
      }
    },
    methods: {
      getPinColor(pin, hover) {
        if (pin) {
          if (hover && this.$vuetify.breakpoint.lgAndUp) {
            return 'default-content'
          } else {
            return 'primary'
          }
        } else {
          if (hover && this.$vuetify.breakpoint.lgAndUp) {
            return 'default-content'
          } else {
            return 'btn-disable'
          }
        }
      },
      checkIsWhisper(id) {
        return id === 0 || id === 1
      },
      selectedChatItem(id) {
        if (this.$vuetify.breakpoint.smAndUp && this.selectedChat === id) return
        this.$store.commit('chat/SET_SELECTED_CHAT', id)
        const selectedItem = this.chatsFilter
          .flatMap((group) => group.list)
          .find((item) => item.id === id)
        if (selectedItem) this.$emit('selectedChatEvent', selectedItem.id)
        if (this.$vuetify.breakpoint.xsOnly) this.$emit('mobileClickedEvent')
      },
      isWhisper(id, title) {
        return !this.checkIsWhisper(id) && !this.isOfficial(title)
      },
      setFriendPin(username, pin) {
        this.$store.commit('chat/SET_FRIEND_PIN', { title: username, pin: !pin })
        this.$store.dispatch('chat/saveFriendsPinToLocalStroage')
        this.setAllPanelListOpen()
      },
      getFriendsPinFromLocalStroage() {
        const friendsPin = localStorage.getItem('friendsPin')
        return JSON.parse(friendsPin)
      },
      async setPlayerInfo(userName) {
        // 先獲取空數據，以免顯示上一個用戶資訊
        this.playerInfo = {
          username: '',
          level: 0,
          levelVip: 0,
          money: 0,
          thumbUrl: '',
          online: false,
          guildName: ''
        }
        const role = await this.getPlayerData(userName)
        this.playerInfo = role
        this.updatePlayerInfo(role)
      },
      async updatePlayerInfo(userdata) {
        await this.$store.dispatch('social/setSingleFriendStatus', userdata)
      },
      isPanelOpen(idx) {
        return this.openPanelList.includes(idx)
      },
      // 展開所有聊天類別
      setAllPanelListOpen() {
        if (this.openPanelList.length === this.chatsFilter.length) return
        this.openPanelList = this.chatsFilter.map((_, index) => index)
      }
    }
  }
</script>
<style lang="scss" scoped>
  $primary-color: map-get($colors, primary);
  .friend-offline {
    opacity: 0.3;
  }
  .selected-list-item {
    background: rgba($primary-color, 0.24);
  }
  .v-expansion-panels .v-expansion-panel::v-deep {
    background-color: transparent;
    .v-expansion-panel-header {
      min-height: 40px;
      .v-expansion-panel-header__icon {
        margin-left: 0;
        align-items: center;
        .panel-arrow {
          transition: transform 0.3s cubic-bezier(0.25, 0.8, 0.5, 1);
          &.panel-open {
            transform: rotate(90deg);
          }
        }
      }
    }
    .v-expansion-panel-content__wrap {
      padding: 0;
    }
  }
  .pinTooltip {
    width: max-content;
    position: absolute;
    top: calc(100% - 12px) !important;
    left: initial !important;
    right: 0;
  }
</style>
