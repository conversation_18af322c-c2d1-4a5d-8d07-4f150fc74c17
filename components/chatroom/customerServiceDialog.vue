<template>
  <div>
    <v-dialog
      v-model="showCustomerServiceDialogStatus"
      persistent
      :content-class="
        breakpoint.xsOnly
          ? 'customer-service-dialog-mobile transparent'
          : 'customer-service-dialog transparent rounded-lg'
      "
      max-width="560"
      :fullscreen="breakpoint.xsOnly || (breakpoint.smAndDown && orientation !== 0)"
    >
      <v-card class="dialog-fill fill-height bg-dialog">
        <customDialogTitle
          :title="$t('online_customer_service').toUpperCase()"
          @closeDialog="closeDialog"
          :class="['customerService-title', { 'notch-left': hasLeftNotch }]"
        />
        <v-card-text class="main-content d-flex flex-column px-0 pb-0">
          <div
            v-if="contactInfo !== ''"
            class="d-flex justify-center dialog-fill-2 py-2 px-xl-0 px-lg-0 px-md-0 px-sm-0 px-6 bg-dialog-medium"
          >
            <span
              class="text-subtitle-2 default-content--text custom-text-noto text-regular--text"
              :class="{ 'text-center': breakpoint.xsOnly }"
            >
              {{ contactInfo }}
            </span>
          </div>
          <div
            id="msg-box-customer-service"
            @scroll="handleScrollDebounced('msg-box-customer-service')"
            @click="updateReadNoty('msg-box-customer-service')"
            :class="{ 'notch-left': hasLeftNotch, 'notch-right': hasRightNotch }"
          >
            <msgBox
              :msg-ary="customerService"
              is-customer-service-dialog
              :container-px="'px-6'"
              @showImgDialog="showImgDialog"
            />
          </div>
          <!-- player semantic -->
          <v-expand-transition>
            <div v-show="showSemantic" class="py-2 px-4">
              <div class="semantic-bg chat-alert-fill pt-1 pb-3 px-3 bg-dialog-section">
                <v-row no-gutters justify="start">
                  <v-col cols="12" class="d-flex flex-nowrap align-center">
                    <span class="primary--text text-subtitle-2 font-weight-bold text-medium--text">
                      {{ $t('semantic_title') }}
                    </span>
                    <v-spacer></v-spacer>
                    <v-btn
                      icon
                      @click="
                        () => {
                          $store.commit('chat/SET_SHOW_SEMANTIC', false)
                        }
                      "
                    >
                      <v-icon :color="$UIConfig.replaceColor.btnRegular"> mdi-close </v-icon>
                    </v-btn>
                  </v-col>
                  <v-col cols="12" class="d-flex flex-nowrap" align="start">
                    <div>
                      <span class="primary--text text-body-2 font-weight-medium text-medium--text">
                        {{ $t('semantic_noty') }}
                      </span>
                    </div>
                  </v-col>
                </v-row>
              </div>
            </div>
          </v-expand-transition>
          <div>
            <msgBar
              ref="msgBar"
              customer-service
              only-text
              @isCustomStickerEvent="isCustomStickerEvent"
              @sendMessageEvent="sendMessageEvent"
              @sendForbiddenEvent="sendForbiddenEvent"
              :is-customer-service-msgbar="true"
            />
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
    <imgDialog :img-dialog-status.sync="imgDialog.show" :img.sync="imgDialog.img" />
  </div>
</template>

<script>
  import orientation from '~/mixins/orientation'
  import setupScrollHtml from '~/mixins/setupScrollHtml'
  import cloneDeep from 'lodash/cloneDeep'
  import debounce from 'lodash/debounce'
  const STATION = process.env.STATION

  export default {
    name: 'CustomerServiceDialog',
    mixins: [orientation, setupScrollHtml],
    components: {
      customDialogTitle: () => import('~/components/customDialogTitle'),
      msgBox: () => import('~/components/chatroom/msgBox'),
      msgBar: () => import('~/components/chatroom/msgBar'),
      imgDialog: () => import('~/components/imgDialog')
    },
    props: {
      showCustomerServiceDialogStatus: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        scrollTopStatus: false,
        handleScrollDebounced: null,
        imgDialog: {
          show: false,
          img: ''
        },
        isCustomSticker: false
      }
    },
    computed: {
      customerServiceHotline({ $store }) {
        return $store.getters[`${STATION}/companyInfo/customerServiceHotline`]
      },
      customerServiceFax({ $store }) {
        return $store.getters[`${STATION}/companyInfo/customerServiceFax`]
      },
      customerServiceMail({ $store }) {
        return $store.getters[`${STATION}/companyInfo/serviceMail`]
      },
      contactInfo() {
        const isEmpty = (str) => {
          return str === ''
        }
        const customerServiceHotlineEmpty = isEmpty(this.customerServiceHotline)
        const customerServiceMailEmpty = isEmpty(this.customerServiceMail)
        const customerServiceFaxEmpty = isEmpty(this.customerServiceFax)
        if (customerServiceHotlineEmpty && customerServiceFaxEmpty && customerServiceMailEmpty)
          return ''
        if (!customerServiceHotlineEmpty && !customerServiceFaxEmpty)
          return `${this.$t('customer_service_hotline')}：${this.customerServiceHotline}‧
                      ${this.$t('customer_service_fax')}：${this.customerServiceFax}`
        if (!customerServiceHotlineEmpty)
          return `${this.$t('customer_service_hotline')}：${this.customerServiceHotline}`
        if (!customerServiceFaxEmpty)
          return `${this.$t('customer_service_fax')}：${this.customerServiceFax}`
        if (!customerServiceMailEmpty) return `${this.$t('contact')}：${this.customerServiceMail}`
      },
      station({ $store }) {
        return $store.getters['station']
      },
      customerService({ $store }) {
        return $store.getters['chat/customerServiceMsg']
      },
      //在watch物件使用deep的屬性 會導致所取得的newVal與oldVal都是同一個物件 故使用cloneDeep
      customerServiceClone() {
        return cloneDeep(this.customerService)
      },
      userName({ $store }) {
        return $store.getters['role/userName']
      },
      showSemantic({ $store }) {
        return $store.getters['chat/showSemantic']
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      },
      reportInterval() {
        return this.$UIConfig.restriction.reportInterval
      }
    },
    watch: {
      showCustomerServiceDialogStatus: {
        handler(val) {
          if (val) {
            this.$nextTick(() => {
              requestAnimationFrame(() => {
                setTimeout(() => {
                  this.updateReadNoty('msg-box-customer-service')
                }, 50)
              })
            })
          } else {
            this.$refs.msgBar.clearMsg()
            this.$store.commit('chat/SET_IS_KEYBOARD_OPEN', false)
          }
          this.setupScrollActive(val)
        }
      },
      customerServiceClone: {
        async handler(newVal, oldVal) {
          const self = this
          const autoScroll = async function () {
            //確保元素已經被渲染
            const scrollToBottomDelay = (id) =>
              new Promise((resolve) => {
                setTimeout(async () => {
                  await self.scrollToBottom(id)
                  resolve()
                }, 50)
              })

            const id = 'msg-box-customer-service'
            const isNotScrollToBottom = self.isNotScrollToBottom(id)

            if (!isNotScrollToBottom) {
              await scrollToBottomDelay(id)
            }
            self.scrollTopStatus = isNotScrollToBottom
          }

          const isAddMsg = oldVal?.length < newVal?.length
          const isLastMsgDiff =
            oldVal?.[oldVal?.length - 1]?.id !== newVal?.[newVal?.length - 1]?.id
          if (this.showCustomerServiceDialogStatus && (isAddMsg || isLastMsgDiff)) autoScroll()
        },
        deep: true
      }
    },
    created() {
      const color = this.$UIConfig.customerService.announcementColor
      this.$store.commit('chat/UNSHIFT_CUSTOMER_SERVICE_MSG', {
        id: Date.now(),
        messageType: 1,
        user: { name: 'system', color: { red: 54, green: 191, blue: 54 } },
        date: new Date(),
        msg: {
          color: color,
          message: 'online_customer_service_noty',
          type: 3
        },
        observed: false,
        img: ''
      })
      this.handleScrollDebounced = debounce(this.checkShowScrollStatusAndUpdateReadNoty, 800) // 使用 debounce 创建防抖函数
    },

    beforeDestroy() {
      this.$store.commit('chat/SET_IS_KEYBOARD_OPEN', false)
    },
    methods: {
      closeDialog() {
        this.$store.commit('chat/SET_SHOW_SEMANTIC', false)
        this.$nuxt.$emit('root:showCustomerServiceDialogStatusEvent', false)
      },
      async sendMessageEvent(message) {
        await this.sendMessage(message)
        this.$nextTick(() => {
          setTimeout(() => {
            this.scrollToBottom()
          }, 100)
        })
      },
      sendForbiddenEvent(message) {
        this.sendForbiddenMessage(message)
        this.$nextTick(() => {
          setTimeout(() => {
            this.scrollToBottom()
          }, 300)
        })
      },
      sendForbiddenMessage(message) {
        this.$store.dispatch('chat/addForbiddenChat', {
          message: message,
          title: 'customer'
        })
        this.$refs.msgBar.clearMsg()
      },
      async sendMessage(message) {
        const systemCond = (data) =>
          data.type === 0 && data.commandId === this.$xinConfig.LOGIN_SERVICE.TYPE.SYSTEM_MESSAGE.ID
        const sendAndNotify = async (sendPacket) => {
          this.$wsClient.send(sendPacket)
          const res = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
            return (
              data.isFeature(this.$xinConfig.FEATURE.CUSTOMER_SERVICE.ID) ||
              data.isFeature(this.$xinConfig.LOGIN_SERVICE.TYPE.SYSTEM_MESSAGE.ID)
            )
          })
          const dmText = ['秒後再使用', '@lang_237']
          const isIncludeDMText = dmText.some((text) => {
            return res.message.includes(text)
          })
          if (systemCond(res)) this.$notify.warning(this.removeDash(res.message))
          else if (res?.message && !isIncludeDMText && !this.isCustomSticker) {
            this.$refs.msgBar.clearMsg()
          }
          // 重置 isCustomSticker 狀態
          this.isCustomStickerEvent(false)
        }
        await sendAndNotify(this.$wsPacketFactory.sendCustomerServiceMessage(message))
        const customerLocalStorage = this.$localStorage.get('customerTimeNoty').userArray
        const customerArray = customerLocalStorage ? JSON.parse(customerLocalStorage) : []
        const usercustomerData = customerArray.find((x) => x.userName === this.userName)
        const usercustomerExpired = usercustomerData ? usercustomerData.customerExpired : undefined

        const customerReport = {
          userName: this.userName,
          customerExpired: !usercustomerExpired
            ? this.$moment().add(this.reportInterval, 's').format()
            : this.$moment().isBefore(usercustomerExpired)
            ? usercustomerExpired
            : this.$moment().add(this.reportInterval, 's').format()
        }
        const usercustomerStorage = this.$localStorage.get('customerTimeNoty').userArray
        const usercustomerArray = usercustomerStorage ? JSON.parse(usercustomerStorage) : []
        const customerIndex = usercustomerArray.findIndex((x) => x.userName === this.userName)
        if (customerIndex >= 0) usercustomerArray[customerIndex] = customerReport
        else usercustomerArray.push(customerReport)
        this.$localStorage.set('customerTimeNoty', {
          userArray: JSON.stringify(usercustomerArray)
        })
      },
      async scrollToBottom() {
        this.clearNoty()
        let elementID = 'msg-box-customer-service'
        const element = document.getElementById(elementID)
        if (element)
          await this.$nextTick(() => {
            element.scrollTo({ top: element.scrollHeight, behavior: 'auto' })
          })
      },
      clearNoty() {
        this.$store.commit('chat/CLEAR_CUSTOMER_SERVICE_NOTY')
      },
      isNotScrollToBottom(id) {
        const element = document.getElementById(id)
        // 找不到該元素，視為不在底部
        if (!element) {
          this.scrollTopStatus = false
          return
        }
        const scrollTop = element.scrollTop
        const scrollHeight = element.scrollHeight
        const clientHeight = element.clientHeight

        //手機dpi存在誤差，故使用容許誤差的方式
        // 计算允许的最大误差和最小误差
        const maxTolerance = 1
        const minTolerance = -1

        //卷軸位置是否在底部
        return (
          scrollHeight > clientHeight &&
          !(
            scrollHeight - (scrollTop + clientHeight) >= minTolerance &&
            scrollHeight - (scrollTop + clientHeight) <= maxTolerance
          )
        )
      },
      checkShowScrollStatusAndUpdateReadNoty(id) {
        this.checkShowScrollStatus(id)
        this.updateReadNoty(id)
      },
      checkShowScrollStatus(id) {
        const self = this

        this.$nextTick(() => {
          const element = document.getElementById(id)
          if (element) self.scrollTopStatus = this.isNotScrollToBottom(id)
          else self.scrollTopStatus = false
        })
      },
      updateReadNoty(id) {
        const container = document.getElementById(id)
        if (!container) {
          return
        }

        const elements = container.children[0]?.children
        if (elements) {
          let lastVisibleElement = null
          const containerTop = container.getBoundingClientRect().top
          const containerBottom = containerTop + container.clientHeight
          if (containerTop != 0 && containerBottom != 0) {
            for (const element of elements) {
              const elementTop = element.getBoundingClientRect().top
              const elementBottom = elementTop + element.clientHeight
              if (elementBottom <= containerBottom) {
                lastVisibleElement = element
                continue
              }
              break
            }
            if (lastVisibleElement && lastVisibleElement?.id) {
              const numericPart = lastVisibleElement.id.match(/\d+/)[0]
              const messageList = this.customerService
              messageList.forEach((item) => {
                if (!item.isRead && item.id <= parseInt(numericPart)) {
                  this.setIsRead({ id: item.id, status: true })
                }
              })
              this.countAllIsNotReadMsg()
            }
          }
        }
      },
      countAllIsNotReadMsg() {
        const noty = this.customerService.filter((item) => !item.isRead).length
        this.$store.commit('chat/SET_CUSTOMER_SERVICE_NOTY', noty)
      },
      setIsRead({ id, status }) {
        this.$store.commit('chat/SET_CUSTOMER_SERVICE_IS_REAED', {
          status: status,
          id: id
        })
      },
      showImgDialog(img) {
        if (img.includes('chat_picture_default')) return
        this.imgDialog.show = true
        this.imgDialog.img = img
      },
      isCustomStickerEvent(isCustomSticker) {
        this.isCustomSticker = isCustomSticker
      }
    }
  }
</script>

<style lang="scss" scoped>
  $primary: map-get($colors, 'primary');
  .v-dialog__content::v-deep {
    //手機橫向
    @media screen and (max-width: 960px) and (orientation: landscape) {
      .customer-service-dialog {
        height: 100%;
        .main-content {
          height: calc(100% - 52px);
          #contact-bar {
            height: 54px;
          }
          #msg-box-customer-service {
            flex: 1;
            overflow: auto;
          }
        }
        .semantic-bg {
          border-radius: 4px;
          position: relative;
          z-index: 11;
        }
      }
    }

    //手機直向
    @media screen and (min-width: 960px),
      screen and (min-height: 600px) and (orientation: portrait) {
      .customer-service-dialog {
        height: 502px;
        .main-content {
          height: calc(100% - 52px);
          #contact-bar {
            height: 54px;
          }
          #msg-box-customer-service {
            flex: 1;
            overflow: auto;
          }
        }
        .semantic-bg {
          border-radius: 4px;
          position: relative;
          z-index: 11;
        }
      }
    }

    .customer-service-dialog-mobile {
      height: 100%;
      .main-content {
        height: calc(100% - 52px);
        #contact-bar {
          height: 54px;
        }
        #msg-box-customer-service {
          flex: 1;
          overflow: auto;
        }
      }
      .semantic-bg {
        border-radius: 4px;
        position: relative;
        z-index: 11;
      }
    }
  }
  @media (orientation: landscape) {
    .customerService-title {
      &.notch-left {
        padding-left: calc(24px + env(safe-area-inset-left)) !important;
      }
    }
    .notch-left {
      padding-left: env(safe-area-inset-left) !important;
    }
    .notch-right {
      padding-right: env(safe-area-inset-right) !important;
    }
  }
</style>
