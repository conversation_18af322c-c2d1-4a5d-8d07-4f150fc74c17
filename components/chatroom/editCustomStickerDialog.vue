<template>
  <v-dialog
    :value="localShowEditCustomStickerDialogStatus"
    draggable="true"
    width="498"
    persistent
    :fullscreen="$vuetify.breakpoint.xsOnly"
  >
    <v-card color="transparent">
      <customDialogTitle :title="$t('edit_photo').toUpperCase()" @closeDialog="closeDialog" />
      <v-card-text class="pt-6" :class="$vuetify.breakpoint.xsOnly ? 'px-6' : 'px-10'">
        <div class="img-content d-flex justify-center align-center">
          <vueAdvancedCropper
            :src="option.img"
            ref="cropper"
            class="cropper-content"
            background-class="grey-6"
            :resize-image="{
              touch: option.canScale,
              adjustStencil: option.canScale
            }"
            :move-image="{ touch: option.canMove, mouse: option.canMove }"
            image-restriction="fit-area"
            :stencil-props="{
              aspectRatio: option.aspectRatio,
              movable: true,
              resizable: true
            }"
            :canvas="{
              minHeight: 0,
              minWidth: 0,
              maxHeight: option.autoCropHeight,
              maxWidth: option.autoCropWidth
            }"
            :auto-zoom="option.canScale"
            @ready="imgLoad"
          ></vueAdvancedCropper>
          <v-overlay :absolute="absolute" :value="overlay">
            <v-progress-circular indeterminate size="64"></v-progress-circular>
          </v-overlay>
        </div>
      </v-card-text>
      <v-card-actions class="pt-0" :class="$vuetify.breakpoint.xsOnly ? 'px-6' : 'px-10'">
        <v-btn
          :disabled="!!loadTimer"
          class="mb-6 button-content--text w-100"
          :color="$UIConfig.defaultBtnColor"
          @click="prepareCustomSticker"
          depressed
        >
          <span class="text-buttom custom-text-noto">
            {{ $t('upload').toUpperCase() }}
          </span>
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
  import hiddenScrollHtml from '@/mixins/hiddenScrollHtml.js'

  import 'vue-advanced-cropper/dist/style.css'
  import 'vue-advanced-cropper/dist/theme.bubble.css'
  export default {
    name: 'EditCustomStickerDialog',
    components: {
      customDialogTitle: () => import('~/components/customDialogTitle')
    },
    mixins: [hiddenScrollHtml],
    props: {
      showEditCustomStickerDialogStatus: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        image: null,
        //圖片剪裁設定
        option: {
          img: '', // 圖片來源
          outputType: 'png', // 產生圖片的格式
          autoCrop: true, // 是否要有截圖框
          autoCropWidth: 600, // 截圖框寬
          autoCropHeight: 600, //截圖框高
          aspectRatio: 1 / 1, //截圖框比例
          canMove: false, //是否可移動圖片
          canScale: false, //是否可拉伸圖片
          fixed: true, //鎖定截圖框比例
          centerBox: true, //截圖框鎖定於圖片內
          maxImgSize: 450,
          mode: 'contain',
          high: true
        },
        absolute: true,
        opacity: 1,
        overlay: true,
        loadTimer: null // 新增計時器
      }
    },
    mounted() {
      // 啟動計時器
      this.startLoadTimer()

      if (this.$liff.isInClient() && this.$device.isIos) {
        const reader = new FileReader()
        reader.onload = (event) => {
          const imageUrl = event.target.result
          this.option.img = imageUrl
        }
        reader.readAsDataURL(this.originPhoto)
      } else {
        const img = URL.createObjectURL(this.originPhoto)
        this.option.img = img
      }
    },
    computed: {
      originPhoto({ $store }) {
        return $store.getters['customSticker/originPhoto']
      },
      localShowEditCustomStickerDialogStatus() {
        return this.showEditCustomStickerDialogStatus
      }
    },
    methods: {
      closeDialog() {
        this.clearLoadTimer()
        this.$emit('update:showEditCustomStickerDialogStatus', false)
      },
      prepareCustomSticker() {
        const { canvas } = this.$refs.cropper.getResult()
        const urlS = this.resizeCustomSticker(canvas, 128, 128)
        const urlL = this.resizeCustomSticker(canvas, 600, 600)
        this.$emit('prepareCustomSticker', urlS, urlL)
      },
      imgLoad() {
        this.clearLoadTimer()
        this.overlay = false
      },
      resizeCustomSticker(sticker, width, height) {
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        canvas.width = width
        canvas.height = height

        // 設定繪製的比例
        ctx.drawImage(sticker, 0, 0, width, height)

        // 將 Canvas 的內容轉為 Data URL
        return canvas.toDataURL()
      },
      // 啟動計時器
      startLoadTimer() {
        this.loadTimer = setTimeout(() => {
          // 10秒後如果計時器還存在，說明圖片加載超時
          if (this.loadTimer) {
            this.$notify.error(this.$t('load_timeout'))
            this.closeDialog()
          }
        }, 10000)
      },
      // 清除計時器
      clearLoadTimer() {
        if (this.loadTimer) {
          clearTimeout(this.loadTimer)
          this.loadTimer = null
        }
      }
    },
    beforeDestroy() {
      this.clearLoadTimer()
      this.$store.commit('customSticker/SET_ORIGIN_PHOTO', null)
    }
  }
</script>

<style scoped>
  .vue-cropper {
    background-image: none !important;
  }
  .img-content {
    overflow: hidden;
    position: relative;
    height: 300px;
  }
  .cropper-content {
    width: 412px;
    height: 300px;
  }
</style>
