<template>
  <div class="msg-custom-sticker scrollbar-thin">
    <!-- 選擇相簿 or 相機(預設隱藏) -->
    <input
      type="file"
      ref="fileInput"
      class="d-none"
      accept="image/*"
      :capture="captureType"
      @change="checkCustomStickerFormat"
    />
    <v-overlay :value="$vuetify.breakpoint.xsOnly && customStickerMenuStatus"></v-overlay>
    <v-menu
      v-model="customStickerMenuStatus"
      :close-on-content-click="false"
      class="custom-sticker-menu"
      :content-class="menuContentClass"
      attach
      eager
      :close-on-click="closeOnClickOutside"
    >
      <template v-slot:activator="{ attrs }">
        <v-btn
          v-bind="attrs"
          icon
          :color="$UIConfig.replaceColor.textRegular"
          :width="customStickerBtnWidth"
          :disabled="customStickerDisabled"
          @mousedown="switchCustomStickerMenu"
        >
          <v-icon>mdi-image-outline</v-icon>
        </v-btn>
      </template>
      <v-card flat class="px-6 pt-3 pb-8 pb-sm-6 rounded-0" color="dialog-fill">
        <v-card-title class="pa-0 pb-3 subtitle-1 custom-text-noto">
          {{ $t('custom_sticker') }}
        </v-card-title>
        <div class="custom-sticker-container rounded overflow-hidden">
          <div class="custom-sticker-box px-4 py-3 overflow-auto">
            <div class="custom-sticker-items" :style="customStickerItemsStyle">
              <div
                v-if="this.customStickerDownloadStatus === 'loading'"
                :style="downloadStatusStyle"
              ></div>
              <v-row
                v-else-if="this.customStickerDownloadStatus === 'success'"
                class="gutters-16"
                :class="[
                  { 'flex-nowrap': isMobileAndLandscape },
                  { 'justify-center': !isMobileAndLandscape }
                ]"
              >
                <v-col
                  v-for="customSticker in localCustomStickerList"
                  :key="customSticker.id"
                  :cols="$vuetify.breakpoint.xsOnly || isMobileAndLandscape ? 'auto' : 4"
                >
                  <!-- 圖片載入失敗，顯示重試按鈕 -->
                  <v-btn
                    v-if="errorList[customSticker.id]"
                    class="add-btn rounded-0 pa-0"
                    height="auto"
                    width="80"
                    elevation="0"
                    color="rgba(255, 255, 255, 0.08)"
                    @click="setRetryList(customSticker.id)"
                  >
                    <span class="material-symbols-rounded"> forward_media </span>
                  </v-btn>
                  <!-- 存在自訂貼圖資料，顯示圖片 -->
                  <v-img
                    v-else-if="customSticker.name"
                    width="80"
                    aspect-ratio="1"
                    :src="getFullPath(customSticker)"
                    class="cursor-pointer"
                    :key="`${customSticker.id}-${retryList[customSticker.id] || 0}`"
                    @click="sendCustomSticker(customSticker)"
                    @error="setErrorList(customSticker.id)"
                    @load="setLoadList(customSticker.id)"
                  >
                    <template v-slot:placeholder>
                      <v-row class="fill-height ma-0" align="center" justify="center">
                        <v-skeleton-loader
                          tile
                          dark
                          width="80"
                          height="80"
                          type="card"
                        ></v-skeleton-loader>
                      </v-row>
                    </template>
                    <template v-if="loadList[customSticker.id]" v-slot:default>
                      <v-btn
                        class="badge-btn rounded-0 pa-0 align-start"
                        elevation="0"
                        color="transparent"
                        @click.stop="
                          () =>
                            selectCustomSticker({
                              type: 'change',
                              payload: { name: customSticker.name, id: customSticker.id }
                            })
                        "
                      >
                        <badge
                          top="0"
                          left="0"
                          badge-width="28px"
                          badge-icon-size="16px"
                          icon="photo_camera"
                        ></badge>
                      </v-btn>
                    </template>
                  </v-img>
                  <!-- 不存在自訂貼圖資料，顯示新增按鈕 -->
                  <v-btn
                    v-else
                    class="add-btn rounded-0 pa-0"
                    height="auto"
                    width="80"
                    elevation="0"
                    color="rgba(255, 255, 255, 0.08)"
                    @click="
                      () =>
                        selectCustomSticker({ type: 'upload', payload: { id: customSticker.id } })
                    "
                  >
                    <span class="material-symbols-rounded"> add </span>
                  </v-btn>
                </v-col>
                <!-- 使用水平滾動的 flex 布局時，使容器的 padding-right 無效，額外添加一個佔位元素 -->
                <div v-show="isMobileAndLandscape" class="pr-2"></div>
              </v-row>
              <div
                v-else-if="this.customStickerDownloadStatus === 'fail'"
                class="d-flex flex-column justify-center align-center"
                :style="downloadStatusStyle"
              >
                <div>
                  <v-img
                    width="60"
                    aspect-ratio="1"
                    :src="downloadFailImg"
                    @error="setOtherFormat"
                  ></v-img>
                </div>
                <span class="custom-text-noto text-caption btn-disable--text">{{
                  $t('download_fail')
                }}</span>
              </div>
            </div>
          </div>
        </div>
        <v-divider class="my-3"></v-divider>
        <v-card-title class="pa-0 pb-3 subtitle-1 custom-text-noto">
          {{ $t('send_image') }}
        </v-card-title>
        <v-row class="gutters-16">
          <v-col>
            <v-btn
              elevation="0"
              outlined
              color="primary"
              class="w-100"
              @click="openCameraOrGallery(null, { type: 'send' })"
            >
              <span class="material-symbols-rounded mr-2"> add </span>
              <span> {{ $t('choosse_photo') }} </span>
            </v-btn>
          </v-col>
          <v-col v-if="isTouchDevice">
            <v-btn
              elevation="0"
              outlined
              color="primary"
              class="w-100"
              @click="openCameraOrGallery('environment', { type: 'send' })"
            >
              <span class="material-symbols-outlined mr-2"> photo_camera </span>
              <span> {{ $t('open_camera') }} </span>
            </v-btn>
          </v-col>
        </v-row>
      </v-card>
    </v-menu>
    <selectCustomStickerMethodDialog
      v-if="showSelectCustomStickerMethodDialogStatus"
      :title="$t('custom_sticker').toUpperCase()"
      :show-select-custom-sticker-method-dialog-status.sync="
        showSelectCustomStickerMethodDialogStatus
      "
      @showEditCustomStickerDialog="showEditCustomStickerDialog"
    />
    <editCustomStickerDialog
      v-if="showEditCustomStickerDialogStatus"
      :show-edit-custom-sticker-dialog-status.sync="showEditCustomStickerDialogStatus"
      @prepareCustomSticker="prepareCustomSticker"
    />
  </div>
</template>

<script>
  import customSticker from '@/mixins/chatroom/customSticker.js'

  export default {
    name: 'MsgCustomSticker',
    mixins: [customSticker],
    components: {
      badge: () => import('~/components/badge'),
      selectCustomStickerMethodDialog: () =>
        import('~/components/chatroom/selectCustomStickerMethodDialog'),
      editCustomStickerDialog: () => import('~/components/chatroom/editCustomStickerDialog')
    },
    props: {
      customStickerDisabled: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        customStickerBtnWidth: 36,
        customStickerMenuStatus: false,
        isTouchDevice: true,
        showSelectCustomStickerMethodDialogStatus: false,
        showEditCustomStickerDialogStatus: false,
        captureType: null,
        loadList: {}, // 記錄已載入的圖片
        errorList: {}, // 記錄哪些貼圖載入失敗
        retryList: {}, // 記錄重試次數，改變 key 來刷新載入失敗的圖片
        downloadFailImg: require('~/assets/image/chatroom/sticker/chat_sticker_download_fail.webp'),
        downloadFailPng: require('~/assets/image/chatroom/sticker/chat_sticker_download_fail.png')
      }
    },
    computed: {
      menuContentClass() {
        return this.$vuetify.breakpoint.xsOnly
          ? `w-100 elevation-0 ${this.isMobileAndLandscape ? ' isMobileAndLandscape' : ''}`
          : `elevation-24 ${this.isMobileAndLandscape ? ' isMobileAndLandscape' : ''}`
      },
      orientation({ $store }) {
        return $store.getters['deviceManagement/getOrientation']
      },
      isMobileAndLandscape() {
        return this.$device.isMobile && this.orientation !== 0
      },
      customStickerItemsStyle() {
        return {
          'max-width': this.$vuetify.breakpoint.xsOnly && this.isMobileAndLandscape ? 'initial' : ''
        }
      },
      closeOnClickOutside() {
        return !(
          this.showSelectCustomStickerMethodDialogStatus || this.showEditCustomStickerDialogStatus
        )
      },
      downloadStatusStyle() {
        return {
          height: this.isMobileAndLandscape ? '84px' : '176px',
          width: this.$vuetify.breakpoint.xsOnly ? 'auto' : '272px'
        }
      }
    },
    mounted() {
      this.$emit('customStickerBtnWidth', this.customStickerBtnWidth)
      this.checkDevice()
      this.initCustomSticker()
    },
    methods: {
      switchCustomStickerMenu(status) {
        this.customStickerMenuStatus = status ?? !this.customStickerMenuStatus
        // 若此動作為關閉，則不執行以下步驟
        if (!this.customStickerMenuStatus) return
        // 若自訂貼圖清單下載失敗，則嘗試重新下載
        if (this.customStickerDownloadStatus === 'fail') {
          this.$store.commit('customSticker/SET_CUSTOM_STICKER_DOWNLOAD_STATUS', 'loading')
          this.reloadCustomSticker()
        }
      },
      // 用來判斷是否為觸控裝置
      checkDevice() {
        this.isTouchDevice = 'ontouchend' in document
      },
      sendCustomSticker(customSticker) {
        const paths = { thumbPath: customSticker.filePathThumb, imgPath: customSticker.filePath }
        this.$emit('sendCustomStickerEvent', paths)
        this.switchCustomStickerMenu(false)
      },
      // 參數 purpose 用於註明目的：: send(直接選擇圖片到聊天室)、change(選新圖片更換自訂貼圖清單)、upload(選擇圖片上傳到自訂貼圖清單)
      selectCustomSticker(purpose) {
        this.$store.commit('customSticker/SET_SELECT_PHOTO_PURPOSE', purpose)
        this.isTouchDevice
          ? this.showSelectCustomStickerMethodDialog(true)
          : this.openCameraOrGallery(null)
      },
      showSelectCustomStickerMethodDialog(val) {
        this.showSelectCustomStickerMethodDialogStatus = val
      },
      showEditCustomStickerDialog(val) {
        this.showEditCustomStickerDialogStatus = val
      },
      openCameraOrGallery(type, purpose) {
        if (purpose) this.$store.commit('customSticker/SET_SELECT_PHOTO_PURPOSE', purpose)
        this.captureType = type
        this.$nextTick(() => {
          this.$refs.fileInput.click()
        })
      },
      checkCustomStickerFormat(e) {
        const img = e.target.files[0]
        const maxSizeInMB = 10
        const maxSizeInBytes = maxSizeInMB * 1024 * 1024
        let hasError = false

        // 重置 input value，確保下一次選擇同樣的檔案時能觸發 change
        e.target.value = ''

        // 檢查檔案格式
        if (!img.type.startsWith('image/') || img.type.endsWith('gif')) {
          this.$notify.error(this.$t('file_error'))
          hasError = true
        }

        // 檢查檔案大小
        if (img.size > maxSizeInBytes) {
          this.$notify.error(this.$t('size_limit', { size: maxSizeInMB + 'MB' }))
          hasError = true
        }

        // 只有所有檢查都通過才進入上傳流程
        if (!hasError) {
          this.$store.commit('customSticker/SET_ORIGIN_PHOTO', img)
          this.showEditCustomStickerDialog(true)
        }
      },
      setErrorList(id) {
        this.$delete(this.loadList, id)
        this.$set(this.errorList, id, true)
      },
      setRetryList(id) {
        this.$delete(this.errorList, id) // 移除該圖片錯誤記錄
        this.$set(this.retryList, id, (this.retryList[id] || 0) + 1) // 更新 key 來強制刷新 v-img
      },
      setLoadList(id) {
        this.$set(this.loadList, id, true)
      },
      setOtherFormat() {
        this.downloadFailImg = this.downloadFailPng
      },
      async prepareCustomSticker(urlS, urlL) {
        this.$nuxt.$loading.start()
        const resUpload = await this.uploadCustomSticker(urlS, urlL)
        if (resUpload.status === 'success') {
          this.selectPhotoPurpose.type === 'send'
            ? this.sendCustomSticker(resUpload.param)
            : this.$notify.success(this.$t('upload_success'))
        } else {
          this.$notify.error(this.$t('file_error'))
        }
        this.showEditCustomStickerDialog(false)
        this.$nuxt.$loading.finish()
      }
    }
  }
</script>

<style lang="scss" scoped>
  $grey-5: map-get($colors, 'grey-5');
  $btn-disable-color: map-get($colors, btn-disable);

  .custom-sticker-menu {
    display: initial;
    > .v-menu__content::v-deep {
      top: initial !important;
      bottom: 0;
      left: initial !important;
      right: 0;
      border-radius: 12px 12px 0px 0px;
      @media screen and (min-width: 600px) {
        bottom: 100%;
        border-radius: 8px;
        &.isMobileAndLandscape {
          bottom: 0;
          right: 16px;
        }
      }
    }

    .custom-sticker-container {
      background-color: $grey-5;
      .custom-sticker-box {
        max-height: 200px;
        .custom-sticker-items {
          max-width: 460px;
          margin: 0 auto;
          @media screen and (min-width: 600px) {
            max-width: 272px;
          }
          .badge-btn {
            height: 32px;
            width: 32px;
            min-width: auto;
            position: absolute;
            bottom: 0;
            right: 0;
            &:hover::before {
              opacity: 0;
            }
          }
          .add-btn {
            aspect-ratio: 1;
            min-width: auto;
            max-width: 100%;
          }
        }
      }
    }
  }

  .gutters-16 {
    margin: -8px;
    .col {
      padding: 8px;
    }
  }
</style>
