<template>
  <div>
    <!-- 自訂貼圖：聊天室、線上客服 -->
    <template v-if="isImgMsg">
      <div
        :class="[
          {
            'cursor-pointer': imgLoaded && !localMsg.msg.thumbUrl.includes('chat_picture_default')
          }
        ]"
        class="message-img"
        @click="showImgDialog(localMsg.msg.imageUrl)"
      >
        <v-img
          :src="localMsg.msg.thumbUrl"
          width="128"
          height="128"
          @error="errorCustomImgHandler(localMsg)"
          @load="loadCustomImgHandler"
        ></v-img>
        <div
          v-if="imgLoaded && !localMsg.msg.thumbUrl.includes('chat_picture_default')"
          class="message-icon rounded-pill d-flex justify-center align-center"
        >
          <span class="mdi mdi-arrow-expand-all"></span>
        </div>
      </div>
    </template>
    <!-- 文字訊息：聊天室、線上客服 -->
    <template v-else-if="localMsg.msg.type === 3">
      <v-sheet :color="$UIConfig.replaceColor.bgDialogMedium" class="pa-2 rounded-lg">
        <pre
          v-if="isCustomerServiceLink"
          class="text-body-1 custom-text-noto word-break-all chat-text-wrap"
          :style="{
            color: getTextColor(
              localMsg.msg.color.red,
              localMsg.msg.color.green,
              localMsg.msg.color.blue
            )
          }"
          v-html="localMsg.msg.linkMessage"
        />
        <pre
          v-else
          class="text-body-1 custom-text-noto word-break-all chat-text-wrap"
          :style="{
            color: getTextColor(
              localMsg.msg.color.red,
              localMsg.msg.color.green,
              localMsg.msg.color.blue
            )
          }"
          v-text="convertMessage(localMsg.msg.message)"
        />
      </v-sheet>
    </template>
    <!-- 官方貼圖：聊天室 -->
    <template v-else-if="localMsg.msg.type === 4">
      <v-img
        :src="
          localMsg.msg.errorStatus
            ? errorSticker
            : `${imgUrl + padZero(localMsg.msg.stickerId, 6)}.webp`
        "
        width="100"
        height="100"
        @error="errorStickerHandler(localMsg)"
      />
    </template>
    <!-- 語音傳送：聊天室 -->
    <template v-else-if="localMsg.msg.type === 8 && localMsg.msg.dataType === 2">
      <v-sheet color="dialog-fill-2" class="music-player-box pa-2 rounded-lg">
        <v-row
          no-gutters
          align="center"
          class="flex-nowrap px-2"
          :class="{
            'music-player': $vuetify.breakpoint.smAndUp,
            'music-player-mobile':
              ($vuetify.breakpoint.smAndDown && orientation !== 0) || $vuetify.breakpoint.xsOnly
          }"
        >
          <template v-if="!localMsg.msg.getAudioBufferStatus">
            <v-icon size="24" class="mr-2"> mdi-play </v-icon>
            <v-progress-linear
              :value="0"
              color="primary"
              background-color="primary-variant-3-opacity-4"
              class="mr-2"
            ></v-progress-linear>
            <v-progress-circular indeterminate color="grey-3"></v-progress-circular>
          </template>
          <template v-else>
            <v-icon size="24" class="mr-2" @click="playAudioEvent(localMsg)">
              {{ localMsg.msg.isPlaying ? 'mdi-stop ' : 'mdi-play' }}
            </v-icon>
            <v-progress-linear
              :value="localMsg.msg.isPlaying ? countProgress : 0"
              color="primary"
              background-color="primary-variant-3-opacity-4"
              class="mr-2"
            ></v-progress-linear>
            <span class="countdown-text text-body-1 custom-text-noto text-no-wrap">
              {{
                `${
                  localMsg.msg.isPlaying
                    ? Math.ceil(countdown) < 10
                      ? '0' + Math.ceil(countdown)
                      : Math.ceil(countdown)
                    : localMsg.msg.duration < 10
                    ? '0' + localMsg.msg.duration
                    : localMsg.msg.duration
                }"`
              }}
            </span>
          </template>
        </v-row>
      </v-sheet>
    </template>
  </div>
</template>
<script>
  import orientation from '~/mixins/orientation'
  import scssLoader from '@/mixins/scssLoader.js'
  import converter from '~/mixins/converter'
  const NUXT_ENV = process.env.NUXT_ENV
  const loadConfig = require(`~/station/${process.env.STATION}/${NUXT_ENV}.js`).default

  export default {
    name: 'messageType',
    mixins: [orientation, scssLoader, converter],
    props: {
      msg: {
        type: Object,
        required: true
      },
      msgKey: {
        type: String,
        default: ''
      },
      countdown: {
        type: Number,
        default: 0
      },
      countProgress: {
        type: Number,
        default: 0
      }
    },
    data() {
      return {
        imgUrl: `${process.env.IMAGE_URL}/chat_stickers/sticker_img/${loadConfig.client_id}/`,
        errorSticker: require('~/assets/image/chat_sticker_default.webp'),
        isCustomerServiceLink: false,
        isCustomerServiceImg: false,
        imgLoaded: false
      }
    },
    computed: {
      localMsg() {
        // 僅針對線上客服訊息進行特殊格式處理
        if (!this.msg.messageType) {
          const isFromCustomerService = this.msg.user.name !== this.userName
          // 檢查是否包含連結標籤，若檢查過（有 linkMessage 欄位）就跳過
          if (isFromCustomerService && !this.msg.msg.linkMessage) {
            this.checkCustomerServiceLink()
          }
          // 檢查是否包含圖片標籤
          this.checkCustomerServiceImg()
        }
        return this.msg
      },
      userName({ $store }) {
        return $store.getters['role/userName']
      },
      isImgMsg() {
        return (
          // 是否為線上客服的自訂貼圖或聊天室的自訂貼圖
          (this.localMsg.msg.type === 3 && this.isCustomerServiceImg) ||
          (this.localMsg.msg.type === 8 && this.localMsg.msg.dataType === 1)
        )
      }
    },
    methods: {
      getTextColor(r, g, b) {
        return `rgb(${r},${g},${b})`
      },
      padZero(number, size) {
        let result = number.toString() // 将数字转换为字符串

        while (result.length < size) {
          result = '0' + result // 在数字前面添加零
        }

        return result
      },
      showImgDialog(url) {
        if (this.imgLoaded) this.$emit('showImgDialog', url)
      },
      errorCustomImgHandler(item) {
        const errorImg = require('~/assets/image/chatroom/chat_picture_default.webp')
        if (this.isCustomerServiceImg) {
          this.$store.commit('chat/SET_CUSTOMER_SERVICE_MSG', {
            id: item.id,
            payload: [
              { key: 'imageUrl', value: errorImg },
              { key: 'thumbUrl', value: errorImg }
            ]
          })
        } else {
          this.$store.commit('chat/SET_CUSTOM_IMG', {
            key: this.msgKey,
            id: item.id,
            imageUrl: errorImg,
            thumbUrl: errorImg
          })
        }
      },
      errorStickerHandler(item) {
        this.$store.commit('chat/SET_STICKER_ERROR', {
          key: this.msgKey,
          id: item.id,
          errorStatus: true
        })
      },
      playAudioEvent() {
        this.$emit('playAudioEvent')
      },
      loadCustomImgHandler() {
        this.imgLoaded = true
      },
      checkCustomerServiceLink() {
        const originalMessage = this.msg.msg.message
        const linkRegex = /<style="Link"><link="(.*?)"><u>(.*?)<\/u><\/link><\/style>/g

        const linkMessage = originalMessage.replace(linkRegex, (_, href, label) => {
          return `<a href="${href}" target="_blank" color="primary">${label} </a>`
        })

        if (linkMessage !== originalMessage) {
          this.$store.commit('chat/SET_CUSTOMER_SERVICE_MSG', {
            id: this.msg.id,
            payload: [{ key: 'linkMessage', value: linkMessage }]
          })
          this.isCustomerServiceLink = true
        }
      },
      checkCustomerServiceImg() {
        // 檢查是否包含 <ImageUrl> 標籤
        const imgUrlRegex = /<ImageUrl>(.*?)<\/ImageUrl>/
        const imgUrlMatch = this.msg.msg.message?.match(imgUrlRegex)
        if (imgUrlMatch && imgUrlMatch[1] && !this.msg.msg.imageUrl) {
          try {
            // 嘗試解析 JSON 字串
            const imgUrlData = JSON.parse(imgUrlMatch[1])
            // 確認是否包含 Normal、Thumbnail
            if (imgUrlData && imgUrlData.Normal && imgUrlData.Thumbnail) {
              // 添加新的 imageUrl 和 thumbUrl 属性
              this.$store.commit('chat/SET_CUSTOMER_SERVICE_MSG', {
                id: this.msg.id,
                payload: [
                  { key: 'imageUrl', value: imgUrlData.Normal },
                  { key: 'thumbUrl', value: imgUrlData.Thumbnail }
                ]
              })
              this.isCustomerServiceImg = true
            }
          } catch (error) {
            // JSON 解析失敗
            console.warn('Failed to parse JSON:', error)
          }
        }
      }
    }
  }
</script>
<style lang="scss" scoped>
  $primary-variant-3: map-get($colors, 'primary-variant-3');
  $primary-variant-3-opacity-4: rgba($primary-variant-3, 0.4);
  .message-img {
    height: 128px;
    width: 128px;
    position: relative;
    .message-icon {
      position: absolute;
      right: 0px;
      bottom: 0px;
      background-color: rgba(0, 0, 0, 0.5);
      height: 28px;
      width: 28px;
      .mdi-arrow-expand-all {
        font-size: 20px;
      }
    }
  }
  //調整child含有錄音bar的div
  div:has(> .music-player-box) {
    width: 50%;
    @media (max-width: 600px) {
      width: 100%;
    }
  }
  .music-player-box {
    width: 100%;

    .countdown-text {
      width: 19px;
    }
  }

  .chat-text-wrap {
    text-wrap: wrap;
    white-space: pre-line;
    word-break: break-word;
  }

  .v-progress-linear::v-deep {
    .primary-variant-3-opacity-4 {
      background-color: $primary-variant-3-opacity-4 !important;
      border-color: $primary-variant-3-opacity-4 !important;
    }
  }
</style>
