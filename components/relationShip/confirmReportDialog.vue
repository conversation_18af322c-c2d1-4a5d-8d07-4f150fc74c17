<template>
  <div>
    <v-dialog
      v-model="confirmReportDialogStatus.show"
      persistent
      max-width="380px"
      content-class="rounded-lg"
    >
      <v-card elevation="0" tile color="dialog-fill" class="pa-4 pa-sm-6">
        <v-card-title
          class="d-flex justify-center align-center text-h6 font-weight-regular custom-text-noto grey-1--text pa-0"
        >
          {{
            confirmReportDialogStatus.confirm
              ? $t('hint').toUpperCase()
              : $t('reminder').toUpperCase()
          }}
        </v-card-title>
        <v-card-text class="default-content--text text-body-2 custom-text-noto px-0 py-6">
          <div v-if="!confirmReportDialogStatus.confirm">
            <span>
              <i18n path="report_success_text1">
                <template v-slot:player>
                  <span class="primary--text custom-text-noto">
                    {{ confirmReportDialogStatus.name }}
                  </span>
                </template>
              </i18n>
            </span>
            <br />
            <span>{{ $t('report_success_text2') }}</span>
          </div>

          <span v-if="confirmReportDialogStatus.confirm">{{ $t('confirm_report_text') }}</span>
        </v-card-text>
        <v-card-actions class="pa-0">
          <v-row no-gutters justify="end">
            <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pr-2"
              ><v-btn
                v-if="confirmReportDialogStatus.confirm"
                :class="['text-button', breakpoint.xsOnly ? 'w-100' : '']"
                text
                @click="closeDialog"
              >
                {{ $t('cancel') }}
              </v-btn></v-col
            >
            <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pl-2">
              <v-btn
                :class="['button-content--text text-button', breakpoint.xsOnly ? 'w-100' : '']"
                :color="$UIConfig.defaultBtnColor"
                depressed
                @click="sendReportMessage"
              >
                {{ $t('sure') }}
              </v-btn></v-col
            >
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>
<script>
  export default {
    name: 'confirmReportDialog',
    props: {
      confirmReportDialogStatus: {
        type: Object,
        default: { show: false, name: '', confirm: '' }
      }
    },
    data() {
      return {
        disabledTopUpBtnStatus: false
      }
    },
    computed: {
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    methods: {
      closeDialog() {
        this.$emit('responseParentData', {
          confirm: false
        })
      },
      sendReportMessage() {
        this.$emit('responseParentData', {
          confirm: true
        })
      }
    }
  }
</script>
