<template>
  <v-card
    class="d-flex justify-space-between align-center rounded-b-0 py-2 px-6"
    :class="$UIConfig.replaceColor.bgDialogHeader"
    :style="cardStyle"
    flat
    tile
    elevation="0"
    :min-height="titleHeight"
    min-width="54px"
  >
    <!-- title -->
    <v-row v-if="!giftPackLayout" no-gutters align="center">
      <span
        class="button-content--text text-subtitle-1 custom-text-noto text-dialog-header-inverse--text"
      >
        {{ title.toUpperCase() }}
      </span>
      <notyCount v-if="noty && noty > 0" :noty="noty" class="ml-2" />
    </v-row>
    <!--禮包返回-->
    <v-row v-if="giftPackLayout" no-gutters justify="start" align="center" @click="closeDialog">
      <v-icon> mdi-chevron-left </v-icon>
      <span class="text-subtitle-1 custom-text-noto">
        {{ title.toUpperCase() }}
      </span>
    </v-row>
    <!-- icon -->
    <v-row v-if="!giftPackLayout" no-gutters justify="end" align="center">
      <span
        v-if="secondIcon?.length > 0"
        @click="secondIconAction"
        class="material-symbols-outlined button-content--text cursor-pointer mr-2"
      >
        {{ secondIcon }}
      </span>

      <v-btn icon @click="closeDialog" :disabled="disabled">
        <v-icon :color="$UIConfig.replaceColor.customDialogTitleCloseBtn"> mdi-close </v-icon>
      </v-btn>
    </v-row>
  </v-card>
</template>

<script>
  export default {
    name: 'customDialogTitle',
    props: {
      title: { type: String, default: '' },
      titleHeight: { type: String, default: '52px' },
      disabled: { type: Boolean, default: false },
      noty: { type: Number, default: 0 },
      secondIcon: {
        type: String,
        default: ''
      },
      secondIconAction: { type: Function, default: () => {} },
      giftPackLayout: { type: Boolean, default: false }
    },
    components: {
      notyCount: () => import('~/components/notyCount')
    },
    computed: {
      cardStyle() {
        if (this.giftPackLayout) {
          const { background, color } = this.$UIConfig.giftPackLayoutColor
          return `background: ${background}; color: ${color}; cursor: pointer;`
        }
      }
    },
    methods: {
      closeDialog() {
        this.$emit('closeDialog')
      }
    }
  }
</script>
