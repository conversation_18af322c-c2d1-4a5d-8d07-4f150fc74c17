<template>
  <v-dialog
    v-model="showPhoneNumBindingDialogStatusTmp"
    max-width="400px"
    persistent
    transition="dialog-transition"
    :fullscreen="breakpoint.smAndDown"
    :content-class="breakpoint.smAndDown ? '' : 'rounded-lg'"
  >
    <v-card class="rounded-lg rounded-b-0" color="transparent">
      <customDialogTitle
        :title="$t('phone_num_binding').toUpperCase()"
        @closeDialog="closeDialog"
      />
      <v-card-text
        id="phone-number-binding-card"
        :class="['pa-4 pa-sm-6', breakpoint.xsOnly ? 'scrollable-xs' : 'scrollable-sm']"
      >
        <v-row class="justify-center" no-gutters>
          <!-- 國家代碼 -->
          <v-col class="pb-0 pr-2" cols="5">
            <v-autocomplete
              ref="login_country_code"
              v-model="bindingForm.countryCode"
              v-validate="'required'"
              :no-data-text="$t('no_search_data')"
              :disabled="disableInputStatus"
              :error-messages="errors.first('binding.country_code')"
              :items="$phoneNumberValitator.codeList"
              :item-text="bindingForm.countryCode"
              :label="$t('country_code')"
              data-vv-name="country_code"
              data-vv-scope="binding"
              :menu-props="{ attach: false }"
              filled
              shaped
              prefix="+"
              class="mb-0"
              ><template v-slot:item="data">+{{ data.item }}</template>
            </v-autocomplete>
          </v-col>
          <!-- 電話號碼輸入 -->
          <v-col class="pb-0 pl-2" cols="7">
            <v-text-field
              ref="phone_number"
              v-model="bindingForm.phoneNumber"
              v-validate="'required|phone_number:login_country_code'"
              :disabled="disableInputStatus"
              type="number"
              :error-messages="errors.first('binding.phone_number')"
              :label="$t('phone_number') + '*'"
              data-vv-name="phone_number"
              data-vv-scope="binding"
              autofocus
              shaped
              filled
              class="mb-0"
              hide-spin-buttons
              @keydown.enter="sendVerifyCodeMessage"
            />
          </v-col>
          <v-col class="pt-0" cols="12">
            <!-- 傳送驗證碼按鈕 -->

            <v-btn
              :disabled="sendVerifyCodeBtnStatus"
              :color="$UIConfig.defaultBtnColor"
              class="mb-7 button-content--text"
              block
              depressed
              @click="sendVerifyCodeMessage"
            >
              {{ $t('send_verify_code') }}
            </v-btn>
            <!-- 驗證碼輸入 -->
            <v-text-field
              ref="verifyCode"
              v-model="bindingForm.verifyCode"
              v-validate="'required|validate_length:5'"
              :disabled="showVerifyCodeInputStatus"
              type="number"
              :error-messages="errors.first('binding.verify_code')"
              :label="$t('verify_code') + '*'"
              data-vv-name="verify_code"
              data-vv-scope="binding"
              autocomplete="off"
              filled
              shaped
              hide-spin-buttons
            />
            <!-- 如未收到簡訊驗證碼，....文字 -->
            <p v-if="companyPhoneNumber !== ''" class="text-sm-body-2 default-content--text">
              {{ $t('device_white_noty1', { phoneNumber: companyPhoneNumber }) }}
            </p>
            <p v-if="serviceMail !== ''" class="text-sm-body-2 default-content--text">
              {{ $t('device_white_noty3', { mail: serviceMail }) }}
            </p>
            <!-- //綁定 -->

            <v-btn
              :loading="loadingLoginBtnStatus"
              :disabled="disableLoginBtnStatus"
              :color="$UIConfig.defaultBtnColor"
              class="button-content--text"
              depressed
              block
              @click="bindingHandler"
            >
              {{ $t('sure').toUpperCase() }}
            </v-btn>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
  import hiddenScrollHtml from '@/mixins/hiddenScrollHtml.js'
  const STATION = process.env.STATION
  export default {
    name: 'PhoneNumBindingDialog',
    mixins: [hiddenScrollHtml],
    props: {
      showPhoneNumBindingDialogStatus: { type: Boolean, default: false }
    },
    components: {
      customDialogTitle: () => import('~/components/customDialogTitle')
    },

    data() {
      const bindingForm = {
        countryCode: '+886',
        phoneNumber: '',
        verifyCode: ''
      }

      return {
        bindingForm,
        showPhoneNumBindingDialogStatusTmp: this.showPhoneNumBindingDialogStatus,
        sendVerifyCodeBtnStatus: false,
        disableLoginBtnStatus: false,
        disableInputStatus: false,
        showVerifyCodeInputStatus: true,
        loadingLoginBtnStatus: false
      }
    },
    computed: {
      countryCode() {
        const ct = require('countries-and-timezones')
        const timezone = ct.getTimezone(Intl.DateTimeFormat().resolvedOptions().timeZone)
        const countryCode = this.$phoneNumberValitator.contryMap[timezone.countries[0]] || ''
        return countryCode
      },
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      },
      isLogin({ $store }) {
        return $store.getters['role/isLogin']
      },
      companyPhoneNumber({ $store }) {
        return $store.getters[`${STATION}/companyInfo/phoneNumber`]
      },
      serviceMail({ $store }) {
        return $store.getters[`${STATION}/companyInfo/serviceMail`]
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    watch: {
      showPhoneNumBindingDialogStatus: {
        async handler(status) {
          this.showPhoneNumBindingDialogStatusTmp = status
          this.bindingForm = {
            countryCode: this.countryCode,
            phoneNumber: '',
            verifyCode: ''
          }
          this.$validator.reset('binding.*')
        },
        immediate: true
      },
      bindingForm: {
        handler(val) {
          if (val.verifyCode.length === 5) {
            this.bindingHandler()
          }
        },
        deep: true
      },
      maintainSystem: {
        handler(val) {
          if (val[0].maintaining) {
            this.closeDialog()
            this.$nuxt.$emit('root:showLoginDialogStatus', {
              show: false,
              onCancelNotify: () => {}
            })
          }
        },
        deep: true
      }
    },
    mounted() {},
    methods: {
      //關閉dialog
      closeDialog() {
        const isInPlayerStatus = this.$route.name.includes('player_status')
        if (!isInPlayerStatus)
          this.showNotyDialog(
            this.$t('reminder'),
            this.$t('in_player_status_bind_phone_num_dialog')
          )
        this.resetDialog()
        this.$nuxt.$emit('root:showPhoneNumBindingDialogStatus', false)
      },
      //重設dialog資料
      resetDialog() {
        this.bindingForm = {
          countryCode: this.countryCode,
          phoneNumber: '',
          verifyCode: ''
        }
        this.$validator.reset('binding.verifyCode')
        this.disableInputStatus = false
        this.sendVerifyCodeBtnStatus = false
        this.disableLoginBtnStatus = false
        this.showVerifyCodeInputStatus = true
        this.loadingLoginBtnStatus = false
      },
      //取得驗證碼
      async sendVerifyCodeMessage() {
        // 取得維護
        const validate = await Promise.all([
          this.$validator.validate('binding.country_code'),
          this.$validator.validate('binding.phone_number')
        ])
        if (validate.every((val) => val)) {
          let phoneNumber = ''
          let countryCallingCode = ''
          phoneNumber = Number(this.bindingForm.phoneNumber)
          countryCallingCode = this.bindingForm.countryCode
          const fullPhoneNumber = `+${countryCallingCode}${phoneNumber}`
          //向WebSocket發送訊息
          this.$wsClient.send(this.$wsPacketFactory.bindPhone(fullPhoneNumber))
          //等待WebSocket回傳
          const res = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
            return data.isFeature(this.$xinConfig.FEATURE.OTP.BIND)
          })
          // 判斷有無發送過簡訊
          if (res.isSuccess) {
            //+886...和電話號碼 disable
            this.disableInputStatus = true
            // 驗證碼輸入框打開
            this.showVerifyCodeInputStatus = false
            //show msg
            this.$notify.info(res.phoneNumber)
            //傳送驗證碼btn disable
            this.sendVerifyCodeBtnStatus = true
          } else {
            if (this.$UIConfig.phoneNumber.clearPhoneNumber) {
              this.bindingForm.phoneNumber = ''
              this.$validator.reset('binding.phone_number')
            }
            this.$notify.error(res.message)
          }

          this.$store.commit('role/SET_PHONENUMBER', fullPhoneNumber)
          this.$refs.verifyCode.focus()
        }
      },
      //確定
      async bindingHandler() {
        // 取得維護
        const validate = await this.$validator.validate('binding.*')
        if (validate) {
          this.disableLoginBtnStatus = true
          this.loadingLoginBtnStatus = true
          let phoneNumber = ''
          let countryCallingCode = ''
          phoneNumber = Number(this.bindingForm.phoneNumber)
          countryCallingCode = this.bindingForm.countryCode
          const fullPhoneNumber = `+${countryCallingCode}${phoneNumber}`
          const verifyCode = this.bindingForm.verifyCode
          const xAgent = this.$cookies.get('xAgent')
          const reqData = this.$wsPacketFactory.bindVerify({
            phoneNumber: fullPhoneNumber,
            otp: verifyCode,
            device: xAgent
          })
          this.$wsClient.send(reqData)
          try {
            const res = await this.$xinUtility.waitEvent(
              this.$wsClient.receivedListeners,
              (data) => {
                return data.isFeature(this.$xinConfig.FEATURE.BIND.ID)
              }
            )
            //綁定失敗
            if (res.isSuccess === false) {
              this.$notify.error(res.message)
              this.$validator.reset('binding.verifyCode')
              //確定按鈕 disable false、loading false
              this.disableLoginBtnStatus = false
              this.loadingLoginBtnStatus = false
              // 按下確定按鈕，解開disable
              //+886...和電話號碼 disable解開
              this.disableInputStatus = false
              //傳送驗證碼btn disable解開
              this.sendVerifyCodeBtnStatus = false
              // 驗證碼輸入框清空
              if (this.$UIConfig.phoneNumber.clearPhoneNumber) this.bindingForm.phoneNumber = ''
              this.bindingForm.verifyCode = ''
            } else {
              //綁定成功
              this.$notify.info(this.$t('bind_success'))
              this.$store.dispatch('role/updateUserDetail')
              this.closeDialog()
            }
          } catch (error) {
            console.log('phoneNumber Page', error)
          }
        }
      },
      showNotyDialog(title, message) {
        this.$store.dispatch('easyDialog/setDialog', {
          title: title,
          message: message
        })
        this.$nuxt.$emit('root:showNotyDialogStatus', true)
      }
    }
  }
</script>
<style scoped lang="scss">
  #phone-number-binding-card {
    &.scrollable-sm {
      max-height: calc(90vh - 52px);
      overflow-y: auto;
    }
    &.scrollable-xs {
      max-height: calc(100vh - 52px);
      overflow-y: auto;
    }
    @supports (height: 90svh) {
      &.scrollable-sm {
        max-height: calc(90svh - 52px);
      }
      &.scrollable-xs {
        max-height: calc(100svh - 52px);
      }
    }
  }
</style>
