<template>
  <v-btn
    fab
    min-width="0"
    :small="$vuetify.breakpoint.xsOnly || $device.isMobile"
    class="mb-2 pa-0 button-content--text"
    color="gradient-button"
    @click="scollToTop"
  >
    <span class="material-symbols-outlined"> arrow_upward </span>
  </v-btn>
</template>

<script>
  import scroll from '~/mixins/scroll'

  export default {
    name: 'ToTop',
    mixins: [scroll],
    computed: {
      target() {
        return 'body'
      }
    }
  }
</script>
