<template>
  <div class="badge d-flex align-center justify-center rounded-xl" :style="badgeStyle">
    <div
      class="badge-icon-content d-flex align-center justify-center rounded-xl"
      :class="[$UIConfig.badge.classSetting]"
    >
      <span
        class="badge-icon material-symbols-rounded default-content--text"
        :style="badgeIconStyle"
      >
        {{ icon }}
      </span>
    </div>
  </div>
</template>

<script>
  import scssLoader from '@/mixins/scssLoader.js'
  export default {
    props: ['top', 'right', 'bottom', 'left', 'icon', 'badgeWidth', 'badgeIconSize'],
    mixins: [scssLoader],
    data() {
      return {}
    },
    computed: {
      badgeStyle() {
        return {
          top: this.top,
          right: this.right,
          bottom: this.bottom,
          left: this.left,
          width: this.badgeWidth || '24px',
          height: this.badgeWidth || '24px'
        }
      },
      badgeIconStyle() {
        return {
          fontSize: this.badgeIconSize || '12px'
        }
      }
    },
    methods: {}
  }
</script>

<style lang="scss" scoped>
  $card-fill: map-get($colors, 'card-fill');
  .badge {
    position: absolute;
    padding: 2px;
    background: linear-gradient(to bottom, #e9b94f, #ffffff, #e9b94f);
    .badge-icon-content {
      height: 100%;
      width: 100%;
    }
  }
</style>
