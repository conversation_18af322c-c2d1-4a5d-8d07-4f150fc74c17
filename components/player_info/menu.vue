<template>
  <!-- 站台差異註解 -->
  <v-card :color="$UIConfig.menu.backGround" :style="cardStyle">
    <v-list :class="[bgColor]">
      <div v-for="(item, index) in playerProfile" :key="item.title">
        <v-divider v-if="item.title == 'logout'" class="pb-2"></v-divider>
        <div class="px-2">
          <v-list
            rounded
            :class="['pa-0', bgColor, { 'pb-2': index !== playerProfile.length - 1 }]"
          >
            <!-- group -->
            <v-list-group
              v-if="item.title === 'player_info'"
              :value="item.active"
              :append-icon="arrowUp ? 'mdi-chevron-up' : 'mdi-chevron-down'"
              :class="{ 'remove-hover-bg': isTouchDevice }"
              @click="setTitle(item.title)"
            >
              <template v-slot:prependIcon>
                <span class="material-symbols-outlined">
                  {{ item.action }}
                </span>
              </template>
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title> {{ $t(item.title) }}</v-list-item-title>
                </v-list-item-content>
              </template>
              <v-list-item-group
                :mandatory="item.currentIndex !== null && item.title == title"
                v-model="item.currentIndex"
              >
                <v-list-item
                  v-for="(child, index) in item.items"
                  :key="child.title"
                  :class="{ 'mt-2': index === 0 }"
                  @click="toPage(child.link)"
                >
                  <!-- 卡位 -->
                  <v-list-item-icon class="h-24-px" />
                  <v-list-item-content>
                    <v-list-item-title>
                      {{ $t(child.title) }}
                    </v-list-item-title>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- friends -->
            <v-list-item
              v-else-if="item.title == 'friend'"
              :class="{ 'v-list-item--active primary--text': isFriendPage }"
              @click="toPage(item.link)"
            >
              <v-list-item-icon>
                <span class="material-symbols-rounded">
                  {{ item.action }}
                </span>
              </v-list-item-icon>
              <v-list-item-content>
                <v-list-item-title>
                  {{ $t(item.title) }}
                </v-list-item-title>
              </v-list-item-content>
            </v-list-item>
            <!-- logOut -->
            <v-list-item v-else @click="logOut()">
              <v-list-item-icon>
                <span class="material-symbols-outlined">
                  {{ item.action }}
                </span>
              </v-list-item-icon>
              <v-list-item-content>
                <v-list-item-title>
                  {{ $t(item.title) }}
                </v-list-item-title>
              </v-list-item-content>
            </v-list-item>
          </v-list>
        </div>
      </div>
    </v-list>
  </v-card>
</template>

<script>
  export default {
    name: 'PlayerMenu',
    props: {
      arrowUp: {
        type: Boolean,
        default: false
      },
      bgColor: {
        type: String,
        default: 'dialog-fill'
      }
    },
    data() {
      return {
        currentPage: null,
        isFriendPage: false,
        playerProfile: [],
        title: '',
        isTouchDevice: true
      }
    },
    computed: {
      getPlayerProfile() {
        return this.$store.getters['role/playerProfile']
      },
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      },
      cardStyle() {
        return this.$vuetify.breakpoint.mdAndUp
          ? { 'max-height': 'calc(100vh - 64px)' }
          : { 'max-height': 'calc(100vh - 68px)' }
      }
    },
    watch: {
      $route: {
        handler(to) {
          this.playerProfile = this.getCurrentState(to.path)
        }
      }
    },
    created() {
      this.playerProfile = this.getCurrentState(this.$route.fullPath)
      // 預設展開個人資訊下拉選單
      this.setTitle('player_info')
    },
    mounted() {
      this.checkDevice()
    },
    methods: {
      getCurrentState(currentPath) {
        const pathSplit = currentPath.split('/')
        this.title = currentPath !== '/' ? pathSplit[1] : ''
        let storeData = JSON.parse(JSON.stringify(this.getPlayerProfile))
        const storeItem = storeData.find((item) => item.title.includes(this.title))
        this.isFriendPage = currentPath.includes('friend')
        this.title = this.isFriendPage ? 'friend' : this.title
        const currentIdx =
          storeItem === undefined
            ? undefined
            : storeItem.items.findIndex(
                (item) => currentPath !== '/' && currentPath.includes(item.link)
              )
        storeData = storeData.map((item) => {
          if (item.title.includes(this.title) && item.active !== undefined) {
            const hasChild = item.items.filter((item) => currentPath.includes(item.link)).length > 0
            item.active = hasChild
            if (currentIdx !== undefined && currentIdx >= 0)
              return { ...item, currentIndex: currentIdx }
            else return item
          } else if (!item.title.includes(this.title) && item.active !== undefined) {
            return { ...item, currentIndex: null }
          }
          return item
        })
        return storeData
      },
      toPage(link) {
        //換頁時關閉menu
        this.$emit('item-click', '')
        //換頁
        this.$router.push({ path: this.localePath(link) })
      },
      setTitle(title) {
        this.playerProfile = this.playerProfile.map((item) => {
          if (item.title === title) item.active = true
          else if (item.active !== undefined) item.active = false
          return item
        })
      },
      async logOut() {
        //登出時關閉menu
        if (this.maintainSystem[0].maintaining) return
        this.$emit('item-click', '')
        this.$nuxt.$emit('root:showLogoutDialogStatus', true)
      },
      // 用來判斷是否為觸控裝置
      checkDevice() {
        this.isTouchDevice = 'ontouchend' in document
      }
    }
  }
</script>

<style lang="scss" scoped>
  $default-content-color: map-get($colors, default-content);
  $primary: map-get($colors, 'primary');
  // 因下拉選單的子選項沒有icon，用來補足高度
  .h-24-px {
    height: 24px;
  }
  .v-list-group ::v-deep {
    & > .v-list-item {
      margin-bottom: 0 !important;
      .material-symbols-outlined,
      .v-icon {
        color: $default-content-color;
      }
      &--active {
        .material-symbols-outlined,
        .v-icon {
          color: $primary;
        }
      }
    }
    &.v-list-group--active > .v-list-item .material-symbols-outlined {
      color: $primary;
    }
    &.remove-hover-bg > .v-list-item:hover::before {
      opacity: 0;
      color: $default-content-color;
    }
  }

  .v-list-item {
    .v-list-item__title,
    .v-list-item__icon {
      color: $default-content-color;
    }

    &--active {
      // 避免因重複點擊相同選項，導致 active 狀態被移除
      pointer-events: none;
      .v-list-item__title,
      .v-list-item__icon {
        color: $primary;
      }
    }
  }
</style>
