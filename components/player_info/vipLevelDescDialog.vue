<template>
  <v-dialog
    v-model="showMemberLevelDescDialogStatusTmp"
    max-width="600"
    persistent
    :fullscreen="breakpoint.xsOnly"
    :content-class="breakpoint.xsOnly ? '' : 'rounded-lg'"
  >
    <v-card color="transparent">
      <customDialogTitle :title="$t('vip_level_desc').toUpperCase()" @closeDialog="closeDialog" />
      <v-card-text v-if="vipLevelTabs[tab]" class="pa-0">
        <!-- header -->
        <v-row
          no-gutters
          class="vipLevel-class pt-4 px-4 pt-sm-6 px-sm-6"
          justify="center"
          align="center"
          ref="imgRow"
        >
          <v-img :src="vipBgSource" @error="handleImageError" class="elevation-3"></v-img>
          <div class="flex-column vipLevel-class-title">
            <v-row no-gutters justify="center">
              <span class="custom-text-noto text-h5 font-weight-bold vip-title">{{
                $t(vipLevelTabs[vipLevelTmp].title).toUpperCase()
              }}</span>
            </v-row>
            <v-row no-gutters justify="center">
              <span class="custom-text-noto text-caption grey-3--text">{{ $t('your_rank') }}</span>
            </v-row>
          </div>
        </v-row>
        <!-- tab -->
        <v-tabs
          v-model="tab"
          align-with-title
          show-arrows
          background-color="transparent"
          color="primary"
          class="pt-4 px-4 px-sm-6"
        >
          <v-tab v-for="vipLevelItem in vipLevelTabs" :key="vipLevelItem.id">
            <v-row no-gutters align="center" justify="center">
              <div class="level-item">
                <v-img
                  :src="getImage3x(vipLevelItem)"
                  :srcset="getImageSrcset(vipLevelItem)"
                  width="24"
                  height="24"
                  :title="$UIConfig.vipLevelDescDialog.vipImgTitle"
                  class="flex-shrink-0"
                />
              </div>
              <span
                class="custom-text-noto font-weight-medium ml-2"
                style="font-size: 14px !important"
              >
                {{ $t(vipLevelItem.title) }}</span
              >
            </v-row>
          </v-tab>
        </v-tabs>
        <v-tabs-items
          active-class="transparent"
          v-model="tab"
          id="vip-level-desc-tabs"
          :class="[
            'pt-4 px-4 pb-4 px-sm-6 pb-sm-6',
            breakpoint.xsOnly ? 'scroll-tabs-xs' : 'scroll-tabs-sm'
          ]"
          :style="{ maxHeight: breakpoint.xsOnly ? contentHeight : 'calc(90vh - 265px)' }"
        >
          <v-tab-item :key="0">
            <nuxt-content :document="noneHelp" />
          </v-tab-item>
          <v-tab-item :key="1">
            <nuxt-content :document="bronzeHelp" />
          </v-tab-item>
          <v-tab-item :key="2">
            <nuxt-content :document="silverHelp" />
          </v-tab-item>
          <v-tab-item :key="3">
            <nuxt-content :document="goldHelp" />
          </v-tab-item>
          <v-tab-item :key="4">
            <nuxt-content :document="whiteGoldHelp" />
          </v-tab-item>
          <v-tab-item :key="5">
            <nuxt-content :document="platinumHelp" />
          </v-tab-item>
          <v-tab-item :key="6">
            <nuxt-content :document="diamondHelp" />
          </v-tab-item>
          <v-tab-item :key="7">
            <nuxt-content :document="fancyDiamondHelp" />
          </v-tab-item>
          <v-tab-item :key="8">
            <nuxt-content :document="moonDazzleHelp" />
          </v-tab-item>
          <v-tab-item :key="9">
            <nuxt-content :document="sunDazzleHelp" />
          </v-tab-item>
          <v-tab-item :key="10">
            <nuxt-content :document="starDazzleHelp" />
          </v-tab-item>
        </v-tabs-items>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
  const STATION = process.env.STATION
  import vipLevel from '@/mixins/vipLevel.js'
  import images from '~/mixins/images'
  export default {
    name: 'vipLevelDescDialog',
    components: {
      customDialogTitle: () => import('~/components/customDialogTitle')
    },
    mixins: [vipLevel, images],
    props: {
      showMemberLevelDescDialogStatus: {
        type: Boolean,
        default: false
      },
      vipLevel: {
        type: Number,
        default: 1
      }
    },
    data() {
      return {
        showMemberLevelDescDialogStatusTmp: this.showMemberLevelDescDialogStatus,
        tab: 0,
        noneHelp: {},
        bronzeHelp: {},
        silverHelp: {},
        goldHelp: {},
        platinumHelp: {},
        diamondHelp: {},
        fancyDiamondHelp: {},
        whiteGoldHelp: {},
        moonDazzleHelp: {},
        sunDazzleHelp: {},
        starDazzleHelp: {},
        vipLevelTmp: this.vipLevel,
        useWebp: true,
        contentHeight: 0
      }
    },
    async created() {
      const contentPath = await this.getContentPath()
      await this.getContent(contentPath)
    },
    watch: {
      showMemberLevelDescDialogStatus: {
        handler(val) {
          this.showMemberLevelDescDialogStatusTmp = val
        },
        immediate: true
      },
      vipLevel: {
        handler(val) {
          this.vipLevelTmp = this.getvipLevel(val)
          this.tab = this.vipLevelTmp
        },
        immediate: true
      }
    },
    mounted() {
      this.observeDivHeight()
    },
    computed: {
      vipLevelTitle({ $store }) {
        return $store.getters['role/vipLevelTitle']
      },
      vipLevelImgFileName({ $store }) {
        //移除非正式會員的圖片
        //deep copy
        let ary = JSON.parse(JSON.stringify($store.getters['role/vipLevelImgFileName']))
        return ary
      },
      vipLevelIconFileName({ $store }) {
        return $store.getters['role/vipLevelIconFileName']
      },
      vipLevelImg1xFileName({ $store }) {
        return $store.getters['role/vipLevelImgFileName']
      },
      vipLevelImg3xFileName({ $store }) {
        return $store.getters['role/vipLevelImg3xFileName']
      },
      vipLevelTabs() {
        return this.vipLevelTitle.map((item, index) => {
          return {
            id: index,
            title: item,
            icon: this.vipLevelImg1xFileName[index] + '.png',
            icon_3x: this.vipLevelImg3xFileName[index] + '.png'
          }
        })
      },
      vipBgSource() {
        const extension = this.useWebp ? 'webp' : 'jpg'
        return this.getImage(
          `vip_level_bg/${this.vipLevelTmp}_${this.vipLevelTabs[
            this.vipLevelTmp
          ].title.toLowerCase()}_bg.${extension}`
        )
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    methods: {
      async getContent(path) {
        try {
          Promise.all([
            this.$content(path.none_help).fetch(),
            this.$content(path.bronze_help).fetch(),
            this.$content(path.silver_help).fetch(),
            this.$content(path.gold_help).fetch(),
            this.$content(path.platinum_help).fetch(),
            this.$content(path.diamond_help).fetch(),
            this.$content(path.fancy_diamond_help).fetch(),
            this.$content(path.white_gold_help).fetch(),
            this.$content(path.moon_dazzle_help).fetch(),
            this.$content(path.sun_dazzle_help).fetch(),
            this.$content(path.star_dazzle_help).fetch()
          ]).then(
            ([
              noneHelp,
              bronzeHelp,
              silverHelp,
              goldHelp,
              platinumHelp,
              diamondHelp,
              fancyDiamondHelp,
              whiteGoldHelp,
              moonDazzleHelp,
              sunDazzleHelp,
              starDazzleHelp
            ]) => {
              this.noneHelp = noneHelp
              this.bronzeHelp = bronzeHelp
              this.silverHelp = silverHelp
              this.goldHelp = goldHelp
              this.platinumHelp = platinumHelp
              this.diamondHelp = diamondHelp
              this.fancyDiamondHelp = fancyDiamondHelp
              this.whiteGoldHelp = whiteGoldHelp
              this.moonDazzleHelp = moonDazzleHelp
              this.sunDazzleHelp = sunDazzleHelp
              this.starDazzleHelp = starDazzleHelp
            }
          )
        } catch (loadErr) {
          console.log(loadErr)
        }
      },
      getContentPath() {
        switch (STATION) {
          case 'india_01':
            return {
              none_help: `articles/${STATION}/${this.$nuxt.$i18n.locale}/none_help`,
              bronze_help: `articles/${STATION}/${this.$nuxt.$i18n.locale}/bronze_help`,
              silver_help: `articles/${STATION}/${this.$nuxt.$i18n.locale}/silver_help`,
              gold_help: `articles/${STATION}/${this.$nuxt.$i18n.locale}/gold_help`,
              platinum_help: `articles/${STATION}/${this.$nuxt.$i18n.locale}/platinum_help`,
              diamond_help: `articles/${STATION}/${this.$nuxt.$i18n.locale}/diamond_help`,
              fancy_diamond_help: `articles/${STATION}/${this.$nuxt.$i18n.locale}/fancy_diamond_help`,
              white_gold_help: `articles/${STATION}/${this.$nuxt.$i18n.locale}/white_gold_help`,
              moon_dazzle_help: `articles/${STATION}/${this.$nuxt.$i18n.locale}/moon_dazzle_help`,
              sun_dazzle_help: `articles/${STATION}/${this.$nuxt.$i18n.locale}/sun_dazzle_help`,
              star_dazzle_help: `articles/${STATION}/${this.$nuxt.$i18n.locale}/star_dazzle_help`
            }
          default:
            return {
              none_help: `articles/${this.$nuxt.$i18n.locale}/none_help`,
              bronze_help: `articles/${this.$nuxt.$i18n.locale}/bronze_help`,
              silver_help: `articles/${this.$nuxt.$i18n.locale}/silver_help`,
              gold_help: `articles/${this.$nuxt.$i18n.locale}/gold_help`,
              platinum_help: `articles/${this.$nuxt.$i18n.locale}/platinum_help`,
              diamond_help: `articles/${this.$nuxt.$i18n.locale}/diamond_help`,
              fancy_diamond_help: `articles/${this.$nuxt.$i18n.locale}/fancy_diamond_help`,
              white_gold_help: `articles/${this.$nuxt.$i18n.locale}/white_gold_help`,
              moon_dazzle_help: `articles/${this.$nuxt.$i18n.locale}/moon_dazzle_help`,
              sun_dazzle_help: `articles/${this.$nuxt.$i18n.locale}/sun_dazzle_help`,
              star_dazzle_help: `articles/${this.$nuxt.$i18n.locale}/star_dazzle_help`
            }
        }
      },
      closeDialog() {
        this.$emit('update:showMemberLevelDescDialogStatus', false)
      },
      handleImageError() {
        if (this.useWebp) {
          this.useWebp = false
        }
      },
      getImage3x(vipLevelItem) {
        return this.getImage('vip_level_icon/' + vipLevelItem.icon_3x)
      },
      getImageSrcset(vipLevelItem) {
        return `${this.getImage('vip_level_icon/' + vipLevelItem.icon)} 1x,
                ${this.getImage('vip_level_icon/' + vipLevelItem.icon_3x)} 2x`
      },
      updateHeight() {
        if (this.$refs.imgRow) {
          const rowHeight = this.$refs.imgRow.clientHeight
          // 檢查是否支援 svh
          const supportsSVH = CSS.supports('height', '90svh')
          if (supportsSVH) {
            this.contentHeight = `calc(100svh - ${rowHeight + 116}px)`
          } else {
            this.contentHeight = `calc(100vh - ${rowHeight + 116}px)`
          }
        }
      },
      //因第一次載入時會抓不到高度，故監聽高度變化
      observeDivHeight() {
        if (!this.$refs.imgRow) return
        this.resizeObserver = new ResizeObserver((entries) => {
          for (let entry of entries) {
            const newHeight = entry.contentRect.height
            if (newHeight > 0) {
              this.updateHeight()
              this.disconnectResizeObserver() // 高度變化後撤銷監聽
            }
          }
        })
        this.resizeObserver.observe(this.$refs.imgRow) // 監聽圖片外層 div
      },
      disconnectResizeObserver() {
        if (this.resizeObserver) {
          this.resizeObserver.disconnect()
          this.resizeObserver = null
        }
      }
    }
  }
</script>

<style scoped lang="scss">
  .vipLevel-class {
    position: relative;
    .vipLevel-class-title {
      position: absolute;
    }
  }
  // 因為只有這邊需要改成透明所以在這邊
  .v-application {
    .theme--dark.v-tabs-items {
      background: transparent;
    }
    .v-image {
      border-radius: 4px;
    }
  }
  // 因應字串只有一個字的階級，將整體固定寬度
  .level-item {
    width: 24px;
  }
  // 新增最小高度樣式
  :deep(.content-mh) {
    min-height: 250px;
  }
  #vip-level-desc-tabs {
    &.scroll-tabs-sm {
      overflow-y: auto;
    }
    &.scroll-tabs-xs {
      overflow-y: auto;
    }
  }
</style>
