<template>
  <v-dialog
    v-model="showUploadPhotoMethodDialogStatusTmp"
    width="358"
    persistent
    content-class="rounded-lg"
  >
    <v-card color="transparent">
      <customDialogTitle :title="$t('custom_avatar').toUpperCase()" @closeDialog="closeDialog" />
      <v-card-text class="pa-4 pa-sm-6">
        <!-- 選擇圖片 會隱藏 -->
        <v-file-input id="choosePhoto" accept="image/*" @change="setPhoto(image)" v-model="image">
        </v-file-input>
        <!-- 選擇圖片 -->
        <v-row no-gutters justify="center" align="center">
          <v-btn class="mb-2 w-100" outlined color="primary" @click="choosePhoto">
            <span class="material-symbols-outlined mr-3"> add </span>
            <span class="text-buttom custom-text-noto">
              {{ $t('choosse_photo') }}
            </span>
          </v-btn>
        </v-row>
        <!-- 選擇其他預設圖片 -->
        <v-row no-gutters justify="center" align="center">
          <v-btn class="mt-2 w-100" outlined color="primary" @click="openChooseLocalPhotoDialog">
            <span class="material-symbols-outlined mr-3"> image </span>
            <span class="text-buttom custom-text-noto">
              {{ $t('choose_other_preset_photo') }}
            </span>
          </v-btn>
        </v-row>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
  export default {
    name: 'uploadPhotoMethodDialog',
    components: {
      customDialogTitle: () => import('~/components/customDialogTitle')
    },

    props: {
      showUploadPhotoMethodDialogStatus: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        showUploadPhotoMethodDialogStatusTmp: this.showUploadPhotoMethodDialogStatus,
        //用於選擇圖片
        image: null
      }
    },

    watch: {
      showUploadPhotoMethodDialogStatus: {
        handler(val) {
          this.showUploadPhotoMethodDialogStatusTmp = val
        }
      }
    },
    computed: {},
    methods: {
      closeDialog() {
        this.$emit('update:showUploadPhotoMethodDialogStatus', false)
      },
      openChooseLocalPhotoDialog() {
        this.closeDialog()
        this.$nuxt.$emit('root:showChooseLocalPhotoDialogStatus', true)
      },
      showEditPhotoDialog() {
        this.closeDialog()
        this.$nuxt.$emit('root:showEditPhotoDialogStatus', true)
      },
      setPhoto(img) {
        const maxSizeInMB = 10
        const maxSizeInBytes = maxSizeInMB * 1024 * 1024
        let hasError = false

        // 檢查檔案格式
        if (!img.type.startsWith('image/') || img.type.endsWith('gif')) {
          this.$notify.error(this.$t('file_error'))
          hasError = true
        }

        // 檢查檔案大小
        if (img.size > maxSizeInBytes) {
          this.$notify.error(this.$t('size_limit', { size: maxSizeInMB + 'MB' }))
          hasError = true
        }

        // 只有所有檢查都通過才進入上傳流程
        if (!hasError) {
          this.$store.commit('role/SET_SELECT_PHOTO', img)
          this.showEditPhotoDialog()
        }
      },
      //選擇圖片
      choosePhoto() {
        document.getElementById('choosePhoto').value = ''
        document.getElementById('choosePhoto').click()
      }
    }
  }
</script>
<style scoped>
  .v-input {
    display: none;
  }
</style>
