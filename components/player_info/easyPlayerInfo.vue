<template>
  <v-card
    :color="$UIConfig.easyPlayerInfo.backGround"
    style="border-radius: 10px"
    id="easy-player-info"
    :style="$vuetify.breakpoint.smAndUp ? 'width: 340px' : 'width: 300px'"
  >
    <v-card-text>
      <v-row no-gutters class="d-flex flex-nowrap align-center" style="min-height: 0px">
        <v-col class="pr-4" style="min-width: 56px" cols="2">
          <div class="mr-4" v-if="badgeType === 'relation'">
            <!-- 黑名單 -->
            <v-badge bordered overlap bottom color="black" v-if="isBlock">
              <v-avatar :tile="tile" size="40">
                <v-img
                  :src="playerInfoTmp.thumbUrl"
                  contain
                  @error="errorImgHandler(playerInfoTmp)"
                >
                  <template v-slot:placeholder>
                    <v-row class="fill-height ma-0" align="center" justify="center">
                      <v-progress-circular
                        indeterminate
                        color="grey lighten-5"
                      ></v-progress-circular>
                    </v-row>
                  </template>
                </v-img>
              </v-avatar>
              <template v-slot:badge>
                <v-icon class="badge-icon-set">mdi-cancel</v-icon>
              </template>
            </v-badge>
            <!-- 好友 -->
            <v-badge bordered overlap bottom color="success" v-else-if="isFriend">
              <v-avatar :tile="tile" size="40">
                <v-img
                  :src="playerInfoTmp.thumbUrl"
                  contain
                  @error="errorImgHandler(playerInfoTmp)"
                >
                  <template v-slot:placeholder>
                    <v-row class="fill-height ma-0" align="center" justify="center">
                      <v-progress-circular
                        indeterminate
                        color="grey lighten-5"
                      ></v-progress-circular>
                    </v-row>
                  </template>
                </v-img>
              </v-avatar>
              <template v-slot:badge>
                <v-icon class="badge-icon-set">mdi-account-multiple</v-icon>
              </template>
            </v-badge>
            <v-avatar :tile="tile" size="40" v-else>
              <v-img :src="playerInfoTmp.thumbUrl" contain @error="errorImgHandler(playerInfoTmp)">
                <template v-slot:placeholder>
                  <v-row class="fill-height ma-0" align="center" justify="center">
                    <v-progress-circular indeterminate color="grey lighten-5"></v-progress-circular>
                  </v-row>
                </template>
              </v-img>
            </v-avatar>
          </div>
          <div class="mr-4" v-else-if="badgeType === 'dot'">
            <v-badge
              bottom
              :color="playerInfoTmp.online ? 'green' : 'grey-4'"
              dot
              bordered
              offset-x="9"
              offset-y="9"
            >
              <v-avatar size="40">
                <v-img
                  :src="playerInfoTmp.thumbUrl"
                  contain
                  @error="errorImgHandler(playerInfoTmp)"
                >
                  <template v-slot:placeholder>
                    <v-row class="fill-height ma-0" align="center" justify="center">
                      <v-progress-circular
                        indeterminate
                        color="grey lighten-5"
                      ></v-progress-circular>
                    </v-row>
                  </template>
                </v-img>
              </v-avatar>
            </v-badge>
          </div>
          <div class="mr-4" v-else>
            <v-avatar :tile="tile" size="40">
              <v-img :src="playerInfoTmp.thumbUrl" contain @error="errorImgHandler(playerInfoTmp)">
                <template v-slot:placeholder>
                  <v-row class="fill-height ma-0" align="center" justify="center">
                    <v-progress-circular indeterminate color="grey lighten-5"></v-progress-circular>
                  </v-row>
                </template>
              </v-img>
            </v-avatar>
          </div>
        </v-col>
        <v-col
          cols="1"
          style="min-width: 100px; max-width: 100%"
          class="flex-grow-1 flex-shrink-0 pr-4"
        >
          <span class="custom-text-noto text-subtitle-2 primary--text">
            {{ playerInfoTmp.username }}</span
          >
          <br v-if="$UIConfig.easyPlayerInfo.showLevel" />
          <span
            v-if="$UIConfig.easyPlayerInfo.showLevel"
            class="custom-text-noto text-body-2 grey-3--text"
            style="font-size: 14px !important"
          >
            LV {{ playerInfoTmp.online ? playerInfoTmp.level : '-' }}
          </span>
        </v-col>
        <v-col :cols="$vuetify.breakpoint.xsOnly ? 3 : 2" class="d-flex flex-nowrap justify-end">
          <template v-if="closeable">
            <v-icon color="grey-3" @click="closeMenu"> mdi-close-circle </v-icon>
          </template>
          <template v-if="report">
            <v-hover v-slot="{ hover }">
              <div
                class="cursor-pointer text-subtitle-2 custom-text-noto"
                :class="{ 'default-content--text': !hover, 'primary--text': hover }"
                @click="showReportDialog(playerInfo.username)"
              >
                <span class="friend-card-report"> {{ $t('report') }} </span>
              </div>
            </v-hover>
          </template>
        </v-col>
      </v-row>
      <v-divider class="mt-4 mb-1" />
      <v-list-item class="px-0">
        <v-list-item-content class="pb-0">
          <v-row no-gutters align="center" v-if="!onlyCoin">
            <div class="mr-2">
              <v-img :src="getImage3x()" :srcset="getImageSrcset()" width="30" height="30" />
            </div>
            <span class="custom-text-noto text-body-2 default-content--text">
              {{
                playerInfoTmp.vipLevel === 0 ? '-' : $t(vipLevelTitle[playerInfoTmp.vipLevel - 1])
              }}</span
            >
          </v-row>
          <v-row no-gutters align="center" class="mt-2" v-if="!onlyCoin">
            <div class="mr-2">
              <v-img
                :src="getImage('<EMAIL>')"
                :srcset="getSrcset('badge_preset')"
                width="30"
                height="30"
              />
            </div>
            <span class="custom-text-noto text-body-2 default-content--text">
              {{ playerInfoTmp.guildName }}
            </span>
          </v-row>
          <v-row no-gutters align="center" :class="{ 'mt-2': onlyCoin }">
            <div class="mr-2">
              <v-img
                :src="getImage('<EMAIL>')"
                :srcset="getSrcset('coin')"
                width="30"
                height="30"
              />
            </div>
            <span class="custom-text-noto text-body-2 default-content--text">
              {{ balance }}
            </span>
          </v-row>
        </v-list-item-content>
      </v-list-item>
      <v-divider class="mt-4 mb-1" v-if="actionBar" />
      <v-row no-gutters class="mt-4" v-if="actionBar">
        <!-- 開啟玩家資訊卡 -->
        <v-col cols="3" class="d-flex justify-center">
          <v-btn icon large @click="showInfoCardDialog()">
            <span class="material-symbols-outlined icon-style"> account_circle </span>
          </v-btn>
        </v-col>
        <!-- 開啟刪除好友dialog -->
        <v-col cols="3" class="d-flex justify-center" v-if="isFriend">
          <v-btn icon large @click="showConfirmDeleteFriendDialogInCard()">
            <v-icon class="icon-style"> mdi-account-remove </v-icon>
          </v-btn>
        </v-col>
        <!-- 新增好友 -->
        <v-col cols="3" class="d-flex justify-center" v-else>
          <v-btn icon large @click="addFriendInCard(playerInfo.username)">
            <v-icon class="icon-style"> mdi-account-plus </v-icon>
          </v-btn>
        </v-col>
        <!-- 新增黑名單 -->
        <v-col cols="3" class="d-flex justify-center" v-if="!isBlock">
          <v-btn icon large @click="addBlockInCard(playerInfo.username)">
            <v-icon class="icon-style"> mdi-account-cancel </v-icon>
          </v-btn>
        </v-col>
        <!-- 解除黑名單 -->
        <v-col cols="3" class="d-flex justify-center" v-else>
          <v-btn icon large @click="showConfirmDeleteBlockDialogInCard()">
            <v-icon class="icon-style"> mdi-account-check </v-icon>
          </v-btn>
        </v-col>
        <!-- 私訊 -->
        <v-col cols="3" class="d-flex justify-center">
          <v-btn
            icon
            large
            @click="createMessageInCard(playerInfoTmp)"
            :disabled="checkIsBlock(playerInfoTmp.username)"
          >
            <span class="material-symbols-outlined icon-style"> sms </span>
          </v-btn>
        </v-col>
      </v-row>
    </v-card-text>
  </v-card>
</template>

<script>
  import vipLevel from '@/mixins/vipLevel.js'
  import relationship from '@/mixins/relationship.js'
  import chat from '@/mixins/chatroom/chat.js'
  import images from '~/mixins/images'
  export default {
    name: 'EasyPlayerInfo',
    mixins: [vipLevel, relationship, chat, images],
    props: {
      playerInfo: {
        type: Object,
        default: {
          username: '',
          rank: '',
          balance: 0,
          level: 0,
          honor: 0,
          activeValue: 0,
          phoneNumber: '',
          isBind: false,
          platformId: 0,
          gameId: 0,
          isLogin: false,
          accountType: 0,
          vipLevel: 0,
          vipLevelTitle: [],
          vipLevelImgFileName: [],
          vipLevelImg3xFileName: [],
          money: 0,
          thumbUrl: '',
          online: false,
          guildName: ''
        }
      },
      closeable: {
        type: Boolean,
        default: false
      },
      report: {
        type: Boolean,
        default: false
      },
      actionBar: {
        type: Boolean,
        default: false
      },
      onlyCoin: {
        type: Boolean,
        default: false
      },
      //dot or relation or ''
      badgeType: {
        type: String,
        default: ''
      },
      tile: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        playerInfoTmp: JSON.parse(JSON.stringify(this.playerInfo)),
        defaultImg: process.env.IMAGE_URL + '/photo_stickers/default.png',
        isFriend: false,
        isBlock: false
      }
    },
    watch: {
      playerInfo: {
        handler(val) {
          this.playerInfoTmp = JSON.parse(JSON.stringify(val))
          this.chceckFriend()
          this.chceckBlock()
        },
        deep: true,
        immediate: true
      },
      friendList: {
        handler() {
          this.chceckFriend()
        },
        deep: true,
        immediate: true
      },
      blockList: {
        handler() {
          this.chceckBlock()
        },
        deep: true,
        immediate: true
      }
    },
    computed: {
      vipLevelTitle() {
        return this.$store.getters['role/vipLevelTitle']
      },
      vipLevelImgFileName() {
        return this.$store.getters['role/vipLevelImgFileName']
      },
      vipLevelImg3xFileName() {
        return this.$store.getters['role/vipLevelImg3xFileName']
      },
      selectPlayer({ $store }) {
        return $store.getters['social/selectPlayer']
      },
      selectFriend({ $store }) {
        return $store.getters['social/selectFriend']
      },
      getImage3x() {
        return this.getVipLevelImage(this.vipLevelImg3xFileName)
      },
      getImageSrcset() {
        const img1x = this.getVipLevelImage(this.vipLevelImgFileName)
        const img2x = this.getVipLevelImage(this.vipLevelImg3xFileName)
        return `${img1x} 1x,${img2x} 2x`
      },
      balance() {
        return this.playerInfoTmp.balance !== undefined
          ? this.$numberFormatter.station(this.playerInfoTmp.balance)
          : '-'
      }
    },
    methods: {
      closeMenu() {
        this.$emit('closeMenu')
      },
      //開啟玩家資訊卡
      async showInfoCardDialog() {
        this.$nuxt.$loading.start()
        let role = await this.getPlayerData(this.playerInfo.username)
        this.$nuxt.$loading.finish()
        this.setSelectPlayerInfo(role)
        this.updatePlayerInfo(role)
        this.$nuxt.$emit('root:guildDialogStatus', { show: false, info: {} })
        this.$nuxt.$emit('root:showPlayerInfoCardDialogStatus', true)
        this.closeMenu()
      },
      async updatePlayerInfo(userdata) {
        await this.$store.dispatch('social/setSingleFriendStatus', userdata)
      },
      //新增好友
      addFriendInCard(name) {
        this.checkAddFriend(name)
        this.closeMenu()
      },
      //新增黑名單
      addBlockInCard(name) {
        this.setSelectPlayerInfo(this.playerInfo)
        this.checkAddBlock(name)
        this.closeMenu()
      },
      showConfirmDeleteFriendDialogInCard() {
        this.setSelectPlayerInfo(this.playerInfo)
        this.showConfirmDeleteFriendDialog(this.playerInfo.username)
        this.closeMenu()
      },
      errorImgHandler(item) {
        item.thumbUrl = this.defaultImg
      },
      chceckFriend() {
        if (this.checkIsFriend(this.playerInfo.username)) {
          this.isFriend = true
        } else {
          this.isFriend = false
        }
      },
      chceckBlock() {
        if (this.checkIsBlock(this.playerInfo.username)) {
          this.isBlock = true
        } else {
          this.isBlock = false
        }
      },
      showConfirmDeleteBlockDialogInCard() {
        this.setSelectPlayerInfo(this.playerInfo)
        this.showConfirmDeleteBlockDialog(this.playerInfo.username)
      },
      createMessageInCard(player) {
        if (player.online) {
          this.createMessage(player.username)
        } else {
          this.$notify.warning(this.$t('player_offline', { player: player.username }))
        }
      },
      getVipLevelImage(fileArr) {
        const vipLevel = this.getvipLevel(this.playerInfoTmp.vipLevel)
        const fileName = `${fileArr[vipLevel]}.png`
        return this.getImage(`/vip_level_icon/${fileName}`)
      }
    }
  }
</script>

<!-- 若加scoped會有一些css吃不到，故最外層使用ID包覆避免汙染 -->
<style lang="scss">
  $dialog-fill: map-get($colors, 'dialog-fill');
  #easy-player-info {
    width: 340px;
    .badge-icon-set {
      font-size: 12px !important;
      display: flex !important;
      align-items: center !important;
      padding-bottom: 0px;
      padding-left: 0px;
    }
    .icon-style {
      font-size: 24px !important;
    }
    .user-name-width {
      flex: 1 2 100px;
    }
    .friend-card-report {
      display: block;
      width: 100%;
      text-align: right;
      white-space: nowrap;
    }
  }
  .theme--dark.v-badge .v-badge__badge::after {
    border-color: $dialog-fill !important;
  }
</style>
