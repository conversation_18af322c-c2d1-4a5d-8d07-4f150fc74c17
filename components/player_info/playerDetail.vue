<template>
  <v-row no-gutters id="player-detail-simple-table">
    <v-col cols="12">
      <v-simple-table class="grey-6">
        <template v-slot:default>
          <tbody>
            <tr v-for="item in userDetailProject" :key="item.title">
              <!-- 回饋金 -->
              <template v-if="item.id == '5'">
                <td
                  :style="{ width: $vuetify.breakpoint.xsOnly ? '50%' : '30%' }"
                  class="icon-cell"
                >
                  <v-tooltip
                    open-delay="50"
                    close-delay="25"
                    bottom
                    z-index="3"
                    :content-class="`tooltip-content-custom ${
                      $vuetify.breakpoint.smAndDown ? 'left-position' : ''
                    }`"
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <v-row no-gutters align="center" v-bind="attrs" v-on="on">
                        <span class="no-wrap default-content--text"> {{ item.title }}</span>
                        <v-icon class="ml-2 icon-size">mdi-help-circle-outline</v-icon>
                      </v-row>
                    </template>
                    <span>{{ $t('plz_use_xinstar_app') }}</span>
                  </v-tooltip>
                </td>
                <td
                  class="default-content--text"
                  :style="{ width: $vuetify.breakpoint.xsOnly ? '50%' : '70%' }"
                >
                  {{ item.text }}
                </td>
              </template>
              <!-- 手機號碼 -->
              <template v-else-if="item.id == '6'">
                <td
                  :style="{ width: $vuetify.breakpoint.xsOnly || isCard ? '50%' : '30%' }"
                  class="icon-cell"
                >
                  <template v-if="!item.text || item.text.length === 0">
                    <v-tooltip
                      v-if="isInfoPage"
                      open-delay="50"
                      close-delay="25"
                      bottom
                      z-index="3"
                      :content-class="`tooltip-content-custom ${
                        $vuetify.breakpoint.smAndDown ? 'full-position' : ''
                      }`"
                    >
                      <template v-slot:activator="{ on, attrs }">
                        <div
                          class="d-flex flex-nowrap justify-start align-center"
                          v-bind="attrs"
                          v-on="on"
                        >
                          <span v-if="isInfoPage" class="red-circle"></span>
                          <span class="no-wrap default-content--text"> {{ item.title }}</span>
                          <v-icon class="ml-2 icon-size">mdi-help-circle-outline</v-icon>
                        </div>
                      </template>
                      <span>{{ $t('personal_info_phone_number_tooltip') }}</span>
                    </v-tooltip>
                    <!-- 不是自介頁面但是未綁定只需顯示文字 -->
                    <template v-else>
                      <span class="no-wrap default-content--text"> {{ item.title }}</span>
                    </template>
                  </template>
                  <template v-else>
                    <span class="no-wrap default-content--text"> {{ item.title }}</span>
                  </template>
                </td>
                <td :style="{ width: $vuetify.breakpoint.xsOnly || isCard ? '50%' : '70%' }">
                  <template v-if="item.text && item.text.length !== 0">
                    <span v-if="isCard">{{ $t('is_bind') }}</span>
                    <span class="no-wrap default-content--text" v-else> {{ item.text }} </span>
                  </template>
                  <template v-else-if="isCard || userDetailData.online === false">
                    {{ $t('unBound') }}
                  </template>
                  <template v-else>
                    <v-btn
                      class="custom-text-noto text-body-1 button-content--text"
                      :color="buttonColor"
                      depressed
                      @click="openBindPhoneDialog"
                    >
                      {{ $t('bind_now') }}
                    </v-btn>
                  </template>
                </td>
              </template>
              <!-- 其他 -->
              <template v-else>
                <td :style="{ width: $vuetify.breakpoint.xsOnly ? '50%' : '30%' }">
                  <span class="no-wrap default-content--text"> {{ item.title }}</span>
                </td>
                <td :style="{ width: $vuetify.breakpoint.xsOnly ? '50%' : '70%' }">
                  <span class="no-wrap default-content--text"> {{ item.text }}</span>
                </td>
              </template>
            </tr>
          </tbody>
        </template>
      </v-simple-table>
    </v-col>
  </v-row>
</template>

<script>
  import scssLoader from '@/mixins/scssLoader.js'
  export default {
    props: {
      isCard: {
        type: Boolean,
        default: false
      },
      isInfoPage: {
        type: Boolean,
        default: false
      },
      userDetail: {
        type: Object,
        default: {}
      }
    },
    mixins: [scssLoader],
    async created() {
      //將game_lobby資料轉成map
      this.gameLobbyMap = this.convertGameLobbyMap(this.gameLobbyJson)
    },
    data() {
      return {
        userDetailData: this.userDetail,
        //轉為Map的gameLobby資料
        gameLobbyMap: new Map()
      }
    },
    methods: {
      getLoginTypeText(accountType) {
        let text = ''
        switch (accountType) {
          case 0:
            text = this.$t('login_msg_free_play')
            break
          case 2:
            text = 'facebook'
            break
          case 3:
            text = this.$t('login_msg_play_now')
            break
          case 5:
            text = 'Yahoo'
            break
          case 6:
            text = 'Google'
            break
          case 9:
            text = 'LINE'
            break
          case 10:
            text = 'APPLE'
            break
          case 100:
            text = this.$t('old_account')
            break
          case 101:
            text = 'moLo'
            break
          case 103:
            text = this.$t('cell_phone_number')
            break
        }
        return text
      },
      getLoginPlatform(platformNo) {
        switch (platformNo) {
          case 0:
            return this.$t('classicWeb')
          case 1:
            return this.$t('classicPC')
          case 2:
            return this.$t('classicAndroid')
          case 3:
            return 'Android'
          case 4:
            return 'PC'
          case 5:
            return 'iOS'
          case 6:
            return 'Web'
          case 7:
            return 'Android'
          case 8:
            return this.$t('Y5Game')
          case 9:
            return 'TV Box'
          case 10:
            return 'Android'
          case 255:
            return '-'
          default:
            return '-'
        }
      },
      openBindPhoneDialog() {
        this.$nuxt.$emit('root:showPhoneNumBindingDialogStatus', true)
      },
      //將game_lobby資料轉成Map
      convertGameLobbyMap(datas) {
        return new Map(datas.map((item) => [item.code.slice(10, 14), item.text]))
      },
      //轉換gameNo
      convertGameNo(gameNo) {
        return (parseInt(gameNo) + 2000).toString()
      },
      getCurrentPositionText() {
        if (this.userDetailData.online) {
          if (this.userDetailData.gameId == 0) {
            return this.$t('game_lobby')
          } else {
            return this.gameLobbyMap.get(this.convertGameNo(this.userDetailData.gameId))
          }
        } else {
          return '-'
        }
      }
    },
    computed: {
      buttonColor() {
        const station = this.$store.getters['station'].env.STATION
        return station === 'malaysia_01' || station === 'india_01' ? 'primary' : 'primary-variant-1'
      },
      userDetailProject({ $store }) {
        let userDetailProject = []
        const station = $store.getters['station']
        let phoneNumberText = ''
        try {
          phoneNumberText = this.userDetailData.isBind
            ? this.userDetailData.phoneNumber.slice(0, -4) + '****'
            : ''
        } catch {
          phoneNumberText = ''
        }
        if (this.isInfoPage) {
          switch (station.env.STATION) {
            case 'vietnam_01':
            case 'malaysia_01':
            case 'india_01':
              {
                userDetailProject = [
                  {
                    id: '1',
                    title: this.$t('player_status'),
                    text: this.userDetailData ? this.$t('is_online') : '-'
                  },
                  {
                    id: '2',
                    title: this.$t('login_platform'),
                    text: this.getLoginPlatform(this.userDetailData.platformId)
                  },
                  {
                    id: '3',
                    title: this.$t('current_position'),
                    text: this.getCurrentPositionText()
                  },
                  {
                    id: '4',
                    title: this.$t('login_by'),
                    text: this.getLoginTypeText(this.userDetailData.accountType)
                  },
                  {
                    id: '6',
                    title: this.$t('bind_phone_number_title'),
                    text: phoneNumberText
                  }
                ]
              }
              break
            default:
              {
                userDetailProject = [
                  {
                    id: '1',
                    title: this.$t('player_status'),
                    text: this.userDetailData ? this.$t('is_online') : '-'
                  },
                  {
                    id: '2',
                    title: this.$t('login_platform'),
                    text: this.getLoginPlatform(this.userDetailData.platformId)
                  },
                  {
                    id: '3',
                    title: this.$t('current_position'),
                    text: this.getCurrentPositionText()
                  },
                  {
                    id: '4',
                    title: this.$t('login_by'),
                    text: this.getLoginTypeText(this.userDetailData.accountType)
                  },
                  {
                    id: '5',
                    title: this.$t('your_rebate'),
                    text: this.userDetailData.referralMonany
                  },
                  {
                    id: '6',
                    title: this.$t('bind_phone_number_title'),
                    text: phoneNumberText
                  }
                ]
              }
              break
          }
        } else if (this.isCard) {
          userDetailProject = [
            {
              id: '1',
              title: this.$t('player_status'),
              text: this.userDetailData.online ? this.$t('is_online') : '-'
            },
            {
              id: '2',
              title: this.$t('login_platform'),
              text: this.userDetailData.online
                ? this.getLoginPlatform(this.userDetailData.platformId)
                : '-'
            },
            {
              id: '3',
              title: this.$t('current_position'),
              text: this.getCurrentPositionText()
            },
            {
              id: '6',
              title: this.$t('bind_phone_number_title'),
              text: phoneNumberText
            }
          ]
        }
        return userDetailProject
      },
      //原始gameLobby資料
      gameLobbyJson({ $store }) {
        return $store.getters['allStar/lobbys']
      }
    },
    watch: {
      userDetail: {
        handler() {
          this.userDetailData = this.userDetail
        },
        immediate: true
      }
    }
  }
</script>
<style lang="scss">
  $error: map-get($colors, 'error');
  $grey-4: map-get($colors, 'grey-4');
  $default-content-color: map-get($colors, default-content);
  #player-detail-simple-table {
    border-radius: 4px;
    overflow: hidden;
    .v-data-table {
      border-radius: inherit;
      tbody {
        tr {
          &:hover {
            background: $grey-4 !important;
          }
        }
      }
    }
    .default-content {
      color: $default-content-color;
    }

    .red-circle {
      display: inline-block;
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background-color: $error;
      margin-left: -12px;
      margin-right: 2px;
    }
    .no-wrap {
      white-space: nowrap;
    }
    .v-icon {
      &.icon-size {
        font-size: 16px;
      }
      color: $default-content-color;
    }
  }
</style>
