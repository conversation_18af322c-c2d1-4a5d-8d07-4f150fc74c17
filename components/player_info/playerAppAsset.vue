<template>
  <!-- app限定 -->
  <v-container class="coin-card-container border-line rounded-lg">
    <div class="coin-card-wrap">
      <v-menu
        open-delay="50"
        open-on-hover
        offset-y
        top
        origin="center bottom"
        transition="scale-transition"
        :nudge-top="breakpoint <= 432 ? 0 : -14"
        :attach="breakpoint <= 432 ? false : '.coin-card-title-box'"
        :max-width="breakpoint <= 432 ? 'calc(100% - 32px)' : 400"
        content-class="coin-card-title-box-tooltip"
      >
        <template v-slot:activator="{ on }">
          <div v-on="on" class="coin-card-title-box border-line rounded-pill">
            <div class="coin-card-title-wrap custom-text-noto default-content--text">
              <span class="coin-card-title-text">{{ $t('app_only') }}</span>
              <v-icon class="coin-card-title-icon default-content--text" size="18"
                >mdi-information-outline
              </v-icon>
            </div>
          </div>
        </template>
        <v-card>
          <span>
            {{ $t('start_the_game') }}
            <a @click="openRedirectDialog">{{ $t('go_download') }}</a
            >，{{ $t('explore_the_unknown_and_wonderful_world') }}
          </span>
        </v-card>
      </v-menu>
      <div class="coin-card-box">
        <v-sheet
          class="coin-card gradient-app-icon default-content--text"
          v-for="asset in localAppAssets"
          :key="asset.assetName"
        >
          <v-tooltip open-delay="50" top nudge-top="-8">
            <template v-slot:activator="{ on, attrs }">
              <v-img
                v-bind="attrs"
                v-on="on"
                :src="getImage(`${asset.imgName}@3x.png`)"
                :srcset="getSrcset(`${asset.imgName}`)"
                width="24"
                height="24"
                class="coin-card-icon"
              ></v-img>
            </template>
            <span>{{ $t(asset.assetName) }}</span>
          </v-tooltip>
          <span v-if="asset.isCoin">{{ formatNumber(asset.quality) }}</span>
          <div v-else-if="!asset.isCoin">{{ asset.quality ?? '-' }}/{{ asset.total }}</div>
          <!-- 初始顯示值 -->
          <span v-else>-</span>
        </v-sheet>
      </div>
    </div>
  </v-container>
</template>

<script>
  import images from '~/mixins/images'
  export default {
    name: 'PlayerAppAsset',
    mixins: [images],
    props: {
      appAssets: {
        type: Array,
        default: []
      }
    },
    data() {
      return {
        localAppAssets: this.appAssets,
        goDownloadAppConfirm: false
      }
    },
    watch: {
      appAssets: {
        handler() {
          this.localAppAssets = this.appAssets
        },
        immediate: true
      }
    },
    methods: {
      //千分位
      formatNumber(num) {
        if (num === undefined || num === null) return '-'
        // 10 位數以上，轉換為 m 單位
        if (num >= 1000000000) {
          return Math.floor(num / 1000000).toLocaleString() + 'm'
        }
        return num.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
      },
      openRedirectDialog() {
        this.$nuxt.$emit('root:redirectDialogStatus', { show: true, drawAnalytics: false })
      }
    },
    computed: {
      breakpoint() {
        return this.$vuetify.breakpoint.width
      }
    }
  }
</script>

<style lang="scss" scoped>
  $info: map-get($colors, info);
  $primary: map-get($colors, primary);

  .coin-card-container {
    margin-top: 38px;
    padding: 1px;
    border-width: 0;
  }
  .coin-card-wrap {
    position: relative;
    padding: 32px 16px 16px;
  }
  .coin-card-title {
    &-box {
      width: initial;
      height: initial;
      position: absolute;
      top: -14px;
      left: 50%;
      transform: translateX(-50%);
      padding: 1px;
      border-width: 0;
    }
    &-wrap {
      display: flex;
      gap: 8px;
      align-items: center;
      padding: 4px 12px;
      cursor: default;
    }
    &-text {
      line-height: 20px;
    }
  }
  .coin-card-title-box-tooltip {
    font-size: 14px;
    font-style: normal;
    letter-spacing: 0.035px;
    padding: 5px 16px;
    border-radius: 4px;
    background: rgba(97, 97, 97, 0.9);
    min-width: max-content !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    box-shadow: none;
    @media screen and (max-width: 432px) {
      width: max-content !important;
      min-width: initial !important;
    }
    //清除dark-theme的樣式
    .v-sheet.v-card {
      background: initial;
      box-shadow: none;
    }
    a {
      color: $info !important;
      text-decoration: underline;
      transition: all 0.2s ease-in-out;
      &:hover {
        color: $primary !important;
      }
    }
  }
  .coin-card-box {
    display: grid;
    gap: 12px;
    grid-template-columns: repeat(5, 1fr);
    margin: 0;
    @media screen and(max-width:959px) and (min-width: 600px) {
      grid-template-columns: repeat(3, 1fr);
    }
    @media screen and (max-width: 599px) {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  .coin-card {
    display: flex;
    padding: 8px;
    align-items: center;
    gap: 8px;
    flex: 1 0 0;
    font-size: 12px;
    background-color: initial;
    border-color: initial;
    border-radius: 10px;
  }
  .coin-card-icon {
    max-width: 24px;
  }
</style>
