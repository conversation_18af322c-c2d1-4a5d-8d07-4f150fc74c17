<template>
  <v-container grid-list-xl class="layouts-error">
    <v-card
      v-if="isMaintenanceError"
      elevation="0"
      :max-width="$vuetify.breakpoint.thresholds.xs"
      color="transparent"
      class="mx-auto"
    >
      <v-layout row wrap fill-height justify-center align-content-center>
        <v-flex xs12 class="d-flex justify-center align-center w-100">
          <v-img
            v-show="imageMap[errorInfo.code]"
            :src="imageMap[errorInfo.code]"
            :max-width="$vuetify.breakpoint.smAndDown ? '343' : '572'"
            contain
            width="100vw"
            :class="[$UIConfig.error.imgClass]"
            title="maintenance"
          />
        </v-flex>
        <v-flex xs12 text-center>
          <v-card
            elevation="0"
            :max-width="$vuetify.breakpoint.thresholds.xs"
            height="50%"
            color="transparent"
            class="mx-auto"
          >
            <div
              v-if="$UIConfig.error.showErrorInfo"
              class="custom-text-noto text-h5 font-weight-bold gradient-primary--text pb-4"
            >
              {{ $t('error.website_maintenance') }}
            </div>
            <template v-if="$UIConfig.error.showMaintenanceTime">
              <v-card-subtitle class="pa-2">
                <span :class="[$UIConfig.error.maintenanceSubTitle]">
                  {{ $t('error.website_maintenance_content') }}
                </span>
              </v-card-subtitle>
              <v-card-text>
                <p class="text-md-body-1 mb-2">
                  <span :class="[$UIConfig.error.maintenanceText]">
                    {{ $t('error.maintenance_time') }}{{ $UIConfig.error.maintenanceColon }}</span
                  >
                  <br class="py-10" />
                </p>
                <p class="text-md-body-1">
                  <span class="warning--text">
                    <span :class="[$UIConfig.error.maintenanceTimeText]">{{
                      formattedMaintenanceTimes.beginTime
                    }}</span>
                    <span :class="[$UIConfig.error.maintenanceTimeText]">~</span>
                    <span :class="[$UIConfig.error.maintenanceTimeText]">{{
                      formattedMaintenanceTimes.endTime
                    }}</span>
                  </span>
                </p>
              </v-card-text>
            </template>
          </v-card>
        </v-flex>
      </v-layout>
    </v-card>

    <v-card
      v-else-if="errorInfo.code === 200"
      :elevation="0"
      :max-width="$vuetify.breakpoint.smAndDown ? '343' : '572'"
      height="50%"
      color="transparent"
      class="mx-auto"
    >
      <v-layout row wrap fill-height justify-center align-center>
        <v-flex xs12 font-weight-bold>
          <p class="text-center custom-text-noto text-h6">
            {{ $t('ie_not_support') }}
          </p>
          <p class="text-center custom-text-noto text-h6">
            <span>{{ $t('recommended_use') }}</span>
            <a :href="`https://www.google.com/intl/${$i18n.locale}/chrome/`" target="_blank">
              <span class="primary--text">
                {{ $t('chrome') }}
              </span>
            </a>
            <span>{{ $t('browser_best_experience') }}</span>
          </p>
        </v-flex>
      </v-layout>
    </v-card>

    <v-card
      v-else
      :elevation="0"
      :max-width="$vuetify.breakpoint.thresholds.xs"
      height="50%"
      color="transparent"
      class="mx-auto"
    >
      <v-layout row wrap fill-height justify-center align-content-center class="my-0">
        <v-flex xs12 class="d-flex justify-center align-center w-100">
          <v-img
            v-show="imageMap[errorInfo.code]"
            :src="imageMap[errorInfo.code]"
            :max-width="$vuetify.breakpoint.smAndDown ? '343' : '572'"
            :class="[$UIConfig.error.imgClass]"
          />
        </v-flex>
        <v-flex v-if="errorInfo.title && $UIConfig.error.showErrorInfo" xs12 text-center>
          <h2 class="text-h5 font-weight-bold gradient-primary--text">
            {{ errorInfo.title }}
          </h2>
        </v-flex>
        <v-flex v-if="$UIConfig.error.showErrorInfo" xs12 text-center>
          <span class="default-content--text">{{ errorInfo.message }}</span>
        </v-flex>
        <v-flex xs12 text-center>
          <v-btn
            depressed
            rounded
            color="gradient-primary button-content--text"
            @click="navigateTo(isNewsPath ? 'news' : 'home')"
          >
            {{ isNewsPath ? $t('goNews') : $t('gohome') }}
          </v-btn>
        </v-flex>
      </v-layout>
    </v-card>
  </v-container>
</template>

<script>
  import convertTime from '~/utils/convertTime'

  const STATION = process.env.STATION
  export default {
    layout: 'white',
    props: {
      error: { type: Object, default: () => {} }
    },
    data() {
      const imageMap = {
        400: require(`~/assets/image/${STATION}/error/400.png`),
        401: require(`~/assets/image/${STATION}/error/401.png`),
        403: require(`~/assets/image/${STATION}/error/403.png`),
        404: require(`~/assets/image/${STATION}/error/404.png`),
        500: require(`~/assets/image/${STATION}/error/500.png`),
        502: require(`~/assets/image/${STATION}/error/502.png`),
        503: require(`~/assets/image/${STATION}/error/maintenance.png`),
        504: require(`~/assets/image/${STATION}/error/504.png`)
      }

      const textMap = {
        400: this.$t('error.bad_request'),
        401: this.$t('error.unauthorized'),
        403: this.$t('error.forbidden'),
        404: this.$t('error.not_found'),
        500: this.$t('error.internal_server_error'),
        502: this.$t('error.bad_gateway'),
        503: this.$t('error.service_unavailable'),
        504: this.$t('error.gateway_timeout')
      }

      const contentMap = {
        400: this.$t('error.bad_request_content'),
        401: this.$t('error.unauthorized_content'),
        403: this.$t('error.forbidden_content'),
        404: this.$t('error.not_found_content'),
        500: this.$t('error.internal_server_error_content'),
        502: this.$t('error.internal_server_error_content'),
        503: this.$t('error.internal_server_error_content'),
        504: this.$t('error.internal_server_error_content')
      }

      return {
        textMap,
        imageMap,
        contentMap
      }
    },
    computed: {
      maintainSystem() {
        return this.$store.getters['maintain/system']
      },
      // 是否為維護錯誤頁面 (503 且有維護參數)
      isMaintenanceError() {
        return this.error.statusCode === 503 && this.error.params?.maintainBeginAt
      },

      /**
       * 格式化維護時間 - 簡化實作，一次處理開始和結束時間
       */
      formattedMaintenanceTimes() {
        const params = this.errorInfo.params
        if (!params.maintainBeginAt || !params.maintainEndAt) {
          return { beginTime: '', endTime: '' }
        }

        const timeConfig = convertTime.getTimeConfig(this)

        return {
          beginTime: convertTime.formatMaintenanceTime(params.maintainBeginAt, timeConfig),
          endTime: convertTime.formatMaintenanceTime(params.maintainEndAt, timeConfig)
        }
      },

      errorInfo() {
        let message =
          this.error.statusCode === 404 && this.isNewsPath // 判斷是否為公告頁
            ? this.$t('error.not_found_news') // 公告頁404內容
            : this.contentMap[this.error.statusCode] || '' // 一般404內容

        if (this.$te(`error.${this.error.message}`)) {
          message = this.$t(`error.${this.error.message}`)
        }

        return {
          code: this.error.statusCode,
          title: this.textMap[this.error.statusCode] || '',
          params: this.error.params || {},
          message
        }
      },
      isNewsPath() {
        // 檢查URL是否包含news
        return this.$route.path.includes('/news')
      }
    },
    created() {
      this.$store.commit('SET_IS_IN_ERROR_PAGE', true)
      // 關閉遊戲模式
    },
    beforeDestroy() {
      this.$store.commit('SET_IS_IN_ERROR_PAGE', false)
    },
    watch: {
      'error.code': {
        handler() {
          this.$nuxt.$emit('root:game', false)
          this.$nuxt.$emit('root:game.demo', false)
          this.$nuxt.$emit('root:game.id', 0)
        }
      },
      // 監聽維護狀態變化，當維護結束時立即導向首頁
      maintainSystem: {
        handler(newSystem, oldSystem) {
          this.handleMaintenanceChange(newSystem, oldSystem)
        },
        deep: true // 需要深度監聽陣列內對象的 maintaining 屬性變化
      }
    },
    methods: {
      /**
       * 處理維護狀態變化
       * 當 server 推送新的維護狀態時觸發
       */
      handleMaintenanceChange(newSystem, oldSystem) {
        const isCurrentlyMaintaining = newSystem?.[0]?.maintaining || false
        const wasCurrentlyMaintaining = oldSystem?.[0]?.maintaining || false

        if (process.env.NODE_ENV === 'development') {
          console.log('[維護狀態變化]', {
            wasCurrentlyMaintaining,
            isCurrentlyMaintaining,
            isMaintenanceError: this.isMaintenanceError,
            errorStatusCode: this.error.statusCode
          })
        }

        // 當前在維護錯誤頁面且維護狀態變化時
        if (this.isMaintenanceError) {
          // 維護結束：立即導向首頁/重整
          if (wasCurrentlyMaintaining && !isCurrentlyMaintaining) {
            if (process.env.NODE_ENV === 'development') {
              console.log('[維護結束] Server 通知維護結束，立即導向首頁')
            }
            this.$nuxt.$loading.start()
            this.redirectToHome()
          }
        }
      },

      /**
       * 統一的導航方法 - 新增 back 選項支援回到上一頁
       * @param {string} target - 導航目標：'home', 'news', 'reload', 'back'
       * @param {Object} options - 導航選項（保留供未來擴展）
       */
      navigateTo(target = 'home', options = {}) {
        const currentPath = this.$route.fullPath
        const homePath = this.localePath('/')

        if (process.env.NODE_ENV === 'development') {
          console.log(`[導航] 目標: ${target}`, { currentPath, homePath, options })
        }

        // 啟動載入動畫（除了重新整理）
        if (target !== 'reload') {
          this.$nuxt.$loading.start()
        }

        switch (target) {
          case 'home':
            if (currentPath === homePath) {
              // 在首頁的特殊處理：先跳轉到下載頁再回首頁
              this.$router.push(this.localePath('/downloads#pc'))
              setTimeout(() => {
                this.$router.replace(homePath)
              }, 100)
            } else {
              this.$router.push(homePath)
            }
            break

          case 'news':
            this.$router.push(this.localePath('/news?type=2&page=1'))
            break

          case 'back':
            // 新增：回到上一頁的選項
            if (process.env.NODE_ENV === 'development') {
              console.log('[導航] 回到上一頁')
            }
            this.$router.go(-1)
            break

          case 'reload':
            if (process.env.NODE_ENV === 'development') {
              console.log('[導航] 重新整理頁面')
            }
            window.location.reload()
            break

          default:
            if (process.env.NODE_ENV === 'development') {
              console.warn(`[導航] 未知的導航目標: ${target}`)
            }
            this.$router.push(homePath)
        }
      },

      /**
       * 維護結束時的導向方法 - 使用原生瀏覽器導航確保可靠性
       */
      redirectToHome() {
        const currentPath = this.$route.fullPath
        const homePath = this.localePath('/')
        const isGamePath = currentPath.includes('/play') || currentPath.includes('/demo')

        if (process.env.NODE_ENV === 'development') {
          console.log('[維護結束導向]', {
            currentPath,
            homePath,
            isGamePath,
            hasHistory: window.history.length > 1
          })
        }

        // 清除錯誤狀態
        this.$store.commit('SET_IS_IN_ERROR_PAGE', false)

        // 停止 loading（維護結束不需要繼續顯示 loading）
        this.$nuxt.$loading.finish()

        // 使用原生瀏覽器導航，避免 Vue Router 狀態問題
        if (isGamePath && window.history.length > 1) {
          // 遊戲路徑且有歷史記錄：使用原生回到上一頁
          if (process.env.NODE_ENV === 'development') {
            console.log('[維護結束] 使用原生瀏覽器回到上一頁')
          }
          window.history.go(-1)
        } else {
          // 其他情況：直接導向首頁或重新整理
          if (process.env.NODE_ENV === 'development') {
            console.log('[維護結束] 使用原生瀏覽器導向首頁')
          }

          // 構建完整的首頁 URL
          const baseUrl = window.location.origin
          const fullHomePath = baseUrl + homePath

          // 直接跳轉到首頁
          window.location.href = fullHomePath
        }
      }
    }
  }
</script>
