import Vue from 'vue'

const STATION = process.env.STATION
export const state = () => ({
  gameDefaultImg: require(`~/assets/image/${STATION}/game/game_default.webp`),
  // 印度站圖片背景為透明，需使用png才能正常呈現，故無提供jpg圖片
  // 未來統一圖片格式為PNG
  gameDefaultImgJpg:
    STATION === 'india_01'
      ? require(`~/assets/image/${STATION}/game/game_default.png`)
      : require(`~/assets/image/${STATION}/game/game_default.jpg`),
  gameDefaultFeaturedImg: require(`~/assets/image/${STATION}/game/game_img_default.webp`),
  gameDefaultFeaturedImgPng: require(`~/assets/image/${STATION}/game/game_img_default.png`),
  singleGameHallInfo: {},
  gameList: [],
  hotGameList: [],
  gameLink: '',
  xinkey: '',
  openGameLock: false,
  playedGameList: [],
  allGameList: [],
  totalCount: 0,
  // RTP 相關狀態
  rtpUpdateStatus: false, // 用於觸發 watcher
  lastRtpUpdate: null, // 最後更新時間
  // 新增：獨立的 RTP Map
  rtpMap: {}
})

export const getters = {
  singleGameHallInfo(state) {
    return state.singleGameHallInfo
  },
  gameList(state) {
    return state.gameList
  },
  hotGameList(state) {
    return state.hotGameList
  },
  gameDefaultImg(state) {
    return state.gameDefaultImg
  },
  gameLink(state) {
    return state.gameLink
  },
  xinkey(state) {
    return state.xinkey
  },
  openGameLock(state) {
    return state.openGameLock
  },
  gameDefaultImgJpg(state) {
    return state.gameDefaultImgJpg
  },
  gameDefaultFeaturedImg(state) {
    return state.gameDefaultFeaturedImg
  },
  gameDefaultFeaturedImgPng(state) {
    return state.gameDefaultFeaturedImgPng
  },
  playedGameList(state) {
    return state.playedGameList
  },
  allGameList(state) {
    return state.allGameList
  },
  totalCount(state) {
    return state.totalCount
  },
  rtpUpdateStatus(state) {
    return state.rtpUpdateStatus
  },
  lastRtpUpdate(state) {
    return state.lastRtpUpdate
  },
  // 新增：獲取 RTP Map
  rtpMap(state) {
    return state.rtpMap
  },
  // 新增：根據遊戲 ID 獲取 RTP
  getGameRtp: (state) => (gameId) => {
    // 確保 gameId 是字串格式
    const gameIdStr = String(gameId)
    return state.rtpMap[gameIdStr] || null
  }
}

export const mutations = {
  SET_SINGLEGAMEHALLINFO(state, singleGameHallInfo) {
    state.singleGameHallInfo = singleGameHallInfo
  },
  SET_GAMELIST(state, list) {
    state.gameList = list
  },
  SET_ALTIMG(state, { index, ImgPath }) {
    state.gameList[index].thumbUrl = ImgPath
  },
  SET_HOTGAMELIST(state, list) {
    state.hotGameList = list
  },
  SET_GAME_LINK(state, data) {
    state.gameLink = data
  },
  SET_GAMELIST_MAINTAIN_STATUS(state, { gameId, status }) {
    state.gameList.forEach((item) => {
      if (item.id === gameId) {
        item.maintaining = status
      }
    })
  },
  SET_GAMELIST_DISABLED(state, gameId) {
    state.gameList = state.gameList.filter((item) => item.id !== gameId)
  },
  SET_XINKEY(state, data) {
    state.xinkey = data
  },
  SET_OPENGAMELOCK(state, data) {
    state.openGameLock = data
  },
  ADD_TO_PLAYED_GAME_LIST(state, gameId) {
    state.playedGameList = state.playedGameList.filter((id) => id !== gameId)
    state.playedGameList.unshift(gameId)
    if (state.playedGameList.length > 10) {
      state.playedGameList.pop()
    }
  },
  SET_PLAYED_GAME_LIST(state, data) {
    state.playedGameList = data
  },
  ADD_ALL_GAME_LIST(state, data) {
    const list = state.allGameList
    const map = new Map(list.map((item) => [item.id, item]))
    data.forEach((obj) => {
      map.set(obj.id, obj)
    })

    state.allGameList = Array.from(map.values())
  },
  SET_ALL_GAME_LIST(state, data) {
    state.allGameList = data
  },
  SET_ALL_GAME_LIST_MAINTAIN_STATUS(state, { gameId, status }) {
    state.allGameList.forEach((item) => {
      if (item.id === gameId) {
        item.maintaining = status
      }
    })
  },
  BATCH_UPDATE_GAME_MAINTAIN_STATUS(state, maintenanceMap) {
    // 更新 allGameList 中的遊戲維護狀態
    state.allGameList.forEach((item) => {
      if (maintenanceMap.has(item.id)) {
        const maintenanceInfo = maintenanceMap.get(item.id)

        Vue.set(item, 'maintaining', maintenanceInfo.maintaining)
        Vue.set(item, 'maintainBeginAt', maintenanceInfo.maintainBeginAt)
        Vue.set(item, 'maintainEndAt', maintenanceInfo.maintainEndAt)
      }
    })

    // 更新 gameList 中的遊戲維護狀態
    state.gameList.forEach((item) => {
      if (maintenanceMap.has(item.id)) {
        const maintenanceInfo = maintenanceMap.get(item.id)
        Vue.set(item, 'maintaining', maintenanceInfo.maintaining)
        Vue.set(item, 'maintainBeginAt', maintenanceInfo.maintainBeginAt)
        Vue.set(item, 'maintainEndAt', maintenanceInfo.maintainEndAt)
      }
    })
  },
  /**
   * 批量更新遊戲 RTP 資料
   * 純粹的狀態更新，不包含驗證邏輯
   */
  BATCH_UPDATE_GAME_RTP(state, rtpMapData) {
    // 直接批量更新 RTP Map
    rtpMapData.forEach(({ gameId, rtpData }) => {
      Vue.set(state.rtpMap, gameId, rtpData)
    })

    // 更新狀態標誌，觸發 watcher
    state.rtpUpdateStatus = !state.rtpUpdateStatus
    state.lastRtpUpdate = new Date().toISOString()
  },
  SET_ALL_GAME_LIST_DISABLED(state, gameId) {
    state.allGameList = state.allGameList.filter((item) => item.id !== gameId)
  },
  RESET(state) {
    Vue.set(state, 'singleGameHallInfo', {})
    Vue.set(state, 'gameLink', '')
    Vue.set(state, 'xinkey', 'demo')
    Vue.set(state, 'rtpList', [])
    Vue.set(state, 'openGameLock', false)
  },
  SET_TOTAL_COUNT(state, count) {
    state.totalCount = count
  },
  /**
   * 清理 RTP Map（可選）
   */
  CLEAR_RTP_MAP(state) {
    state.rtpMap = {}
  }
}
// gameCategoryId 變數統一成 gameCategoryCode
export const actions = {
  /**
   * 初始化遊戲相關功能
   * 包含 RTP 更新、遊戲狀態推播等
   */
  init({ dispatch }) {
    if (!process.client || !this.$gtrSocket) {
      return
    }

    const eventBus = this.$gtrSocket.getEventBus()

    // 監聽 GTR 推播的 RTP 更新
    eventBus.$on('message-game', (data) => {
      if (data.command === 'server_rtp_update' && !data.request_id) {
        dispatch('handleRtpPublish', data)
      }
      // 未來可以在這裡加入其他遊戲相關的推播處理
      // 例如：遊戲上下架、遊戲狀態變更等
    })
  },

  /**
   * 處理 GTR 推播的 RTP 資料
   */
  handleRtpPublish({ dispatch }, rtpData) {
    dispatch('processAndUpdateRtp', rtpData)
  },

  /**
   * 更新已玩遊戲列表，包含 localStorage 處理
   */
  async updatePlayedGameList({ commit, getters }, gameId) {
    // 更新狀態
    commit('ADD_TO_PLAYED_GAME_LIST', gameId)

    // 處理 localStorage 副作用
    if (process.client) {
      try {
        this.$localStorage.set('playedGameList', {
          playedGameList: JSON.stringify(getters.playedGameList)
        })
      } catch (error) {
        console.error('[gameHall] 儲存已玩遊戲列表失敗:', error)
      }
    }
  },

  /**
   * 設定已玩遊戲列表，包含 localStorage 處理
   */
  async setPlayedGameList({ commit }, gameList) {
    // 更新狀態
    commit('SET_PLAYED_GAME_LIST', gameList)

    // 處理 localStorage 副作用
    if (process.client) {
      try {
        this.$localStorage.set('playedGameList', {
          playedGameList: JSON.stringify(gameList)
        })
      } catch (error) {
        console.error('[gameHall] 儲存已玩遊戲列表失敗:', error)
      }
    }
  },

  /**
   * 處理並更新 RTP 資料
   * 包含資料驗證、轉換和日誌
   */
  async processAndUpdateRtp({ commit }, rtpDataList) {
    // 資料驗證
    if (!rtpDataList || !rtpDataList.list || !Array.isArray(rtpDataList.list)) {
      console.warn('[gameHall] RTP 資料格式不正確', rtpDataList)
      return
    }

    // 資料處理和轉換
    const processedData = []
    rtpDataList.list.forEach((rtpData) => {
      const gameId = rtpData.gameId
      if (!gameId) {
        return
      }

      // 確保 gameId 是字串格式
      const gameIdStr = String(gameId)

      processedData.push({
        gameId: gameIdStr,
        rtpData: {
          dailyRtp: rtpData.dailyRtp,
          weeklyRtp: rtpData.weeklyRtp,
          monthlyRtp: rtpData.monthlyRtp,
          lastUpdate: new Date().toISOString()
        }
      })
    })

    // 呼叫純粹的 mutation
    if (processedData.length > 0) {
      commit('BATCH_UPDATE_GAME_RTP', processedData)
    }
  },

  /**
   * 批量更新遊戲維護狀態
   * 包含資料驗證和處理
   */
  async updateGameMaintenanceStatus({ commit }, gameMaintenanceList) {
    // 資料驗證
    if (!Array.isArray(gameMaintenanceList)) {
      console.warn('[gameHall] gameMaintenanceList 不是陣列')
      return
    }

    // 建立維護狀態映射表
    const maintenanceMap = new Map()
    gameMaintenanceList.forEach((game) => {
      maintenanceMap.set(game.id, {
        maintaining: game.maintaining,
        maintainBeginAt: game.maintainBeginAt,
        maintainEndAt: game.maintainEndAt
      })
    })

    // 呼叫純粹的 mutation
    commit('BATCH_UPDATE_GAME_MAINTAIN_STATUS', maintenanceMap)
  },

  async fetchGameList(
    { commit, rootGetters },
    { gameCategoryId, sortType, lang, limit, stationName, offset, keyword, providerId, hasRtp }
  ) {
    const headerData = {
      username: rootGetters['role/userName'] ? rootGetters['role/userName'] : null,
      alias: null
    }

    const body = {
      headerData,
      gameCategoryId,
      sortType,
      lang,
      limit,
      offset,
      keyword,
      providerId: providerId || null,
      stationName,
      hasRtp
    }

    try {
      const response = await this.$clientApi.game.gameSortList(body)

      // 保存總數量
      commit('SET_TOTAL_COUNT', response.count || 0)

      let gameList = response.count > 0 ? response.list : []

      if (process.client) {
        // 第一次先show 遊戲列表
        commit('SET_GAMELIST', gameList)
      }

      // 返回處理後的 gameList 和總數量
      return {
        list: gameList,
        count: response.count || 0
      }
    } catch (error) {
      console.error('[Store] 獲取遊戲列表失敗:', error)
      commit('SET_GAMELIST', [])
      commit('SET_TOTAL_COUNT', 0)
      throw error
    }
  },
  // eslint-disable-next-line no-unused-vars
  updateListWhenDisabled({ commit, dispatch }, { gameCategoryId, gameId }) {
    commit('SET_GAMELIST_DISABLED', gameId)
    commit('SET_ALL_GAME_LIST_DISABLED', gameId)
  },
  async fetchGameDemo({ commit }, { headerData, body }) {
    const requestParams = {
      headerData,
      gameId: body.gameId,
      mobile: body.mobile,
      lang: body.lang,
      backUrl: body.backUrl
    }
    if (body.stationName) {
      requestParams.stationName = body.stationName
    }
    let link = await this.$clientApi.game.gameLinkDemo(requestParams)
    let res = ''
    if ('link' in link) {
      res = link.link
    } else {
      res = link.errorCode
    }
    commit('SET_GAME_LINK', res)
  },
  async fetchGameLink({ commit }, { headerData, body }) {
    const requestParams = {
      headerData,
      xinkey: body.xinkey,
      gameId: body.gameId,
      mobile: body.mobile,
      lang: body.lang,
      ip: body.ip,
      backUrl: body.backUrl,
      userAgent: body.userAgent,
      thumbUrl: body.thumbUrl,
      userLevel: body.userLevel ? body.userLevel : 0,
      vipLevel: body.vipLevel ? body.vipLevel : 0
    }
    if (body.stationName) {
      requestParams.stationName = body.stationName
    }
    let link = await this.$clientApi.game.gameLink(requestParams)
    let res = ''
    if ('link' in link) {
      res = link.link
    } else {
      res = link.errorCode
    }
    commit('SET_GAME_LINK', res)
  },
  /**
   * 使用 GTR WebSocket 獲取 RTP 資料
   * 取代原本的 API 呼叫方式
   */
  async fetchRTPList({ dispatch, state }, { gameIds, options = {}, stationName = null }) {
    if (!gameIds || gameIds.length === 0) {
      return
    }

    // 過濾掉真人遊戲（categoryType === 200）
    const filteredGameIds = gameIds.filter((gameId) => {
      const game = state.allGameList.find((g) => g.id === gameId)
      if (game && game.categoryType === 200) {
        return false
      }
      return true
    })

    if (filteredGameIds.length === 0) {
      return
    }

    // 檢查 GTR Socket 是否可用
    if (!this.$gtrSocket) {
      console.error('[RTP] GTR Socket 不可用，無法獲取 RTP 資料')
      // 返回空資料作為保底，避免中斷流程
      return null
    }

    try {
      // 決定 nocache 值：如果未指定，根據環境決定
      let nocache = options.nocache
      if (nocache === undefined || nocache === null) {
        nocache = process.env.NUXT_ENV !== 'production'
      }

      // 使用 GTR WebSocket 獲取 RTP
      const rtpResponse = await this.$gtrSocket.getRtpList(filteredGameIds, nocache, stationName)

      // 使用新的 action 處理資料
      await dispatch('processAndUpdateRtp', rtpResponse)

      return rtpResponse
    } catch (error) {
      console.error('[RTP] GTR 獲取失敗:', error)

      // GTR 失敗時返回空資料作為保底
      // 保留 fallbackToAPI 參數以便未來需要時可快速啟用
      if (options.fallbackToAPI) {
        console.warn('[RTP] fallbackToAPI 已設定但目前已停用 API 回退機制')
      }

      // 返回 null 而不是拋出錯誤，讓程式能繼續執行
      return null
    }
  },

  /**
   * 原本的 API 方式 (作為備用)
   */
  async fetchRTPListViaAPI({ dispatch }, { gameIds }) {
    let gameRTPList = []
    if (gameIds.length !== 0) {
      gameRTPList = await this.$clientApi.game.gameRTPList(gameIds)

      if (
        Object.prototype.hasOwnProperty.call(gameRTPList, 'list') &&
        gameRTPList.list &&
        Array.isArray(gameRTPList.list) &&
        gameRTPList.list.length !== 0
      ) {
        // 轉換 API 格式為 GTR 格式
        const rtpData = {
          list: gameRTPList.list.map((item) => ({
            gameId: item.gameId, // API 回應已經是小駝峰格式
            dailyRtp: item.dailyRtp,
            weeklyRtp: item.weeklyRtp,
            monthlyRtp: item.monthlyRtp
          }))
        }

        // 使用新的 action 處理資料
        await dispatch('processAndUpdateRtp', rtpData)

        return rtpData
      }
    }

    return null
  },

  /**
   * 統一的 RTP 更新方法
   * 可以同時更新多個遊戲列表的 RTP
   */
  async updateGameListsRtp({ dispatch, state }, { listNames = ['gameList', 'allGameList'] }) {
    // 收集所有需要更新的遊戲 ID
    const gameIdSet = new Set()

    listNames.forEach((listName) => {
      const list = state[listName]
      if (Array.isArray(list)) {
        list.forEach((game) => {
          if (game.id) {
            gameIdSet.add(game.id)
          }
        })
      }
    })

    const gameIds = Array.from(gameIdSet)

    if (gameIds.length === 0) {
      return
    }

    // 呼叫統一的 RTP 獲取方法
    return await dispatch('fetchRTPList', {
      gameIds,
      options: { fallbackToAPI: true }
    })
  },

  /**
   * 獲取所有遊戲列表並自動更新 RTP
   */
  async fetchAllGameList({ dispatch }) {
    try {
      // 這裡可以實現獲取所有遊戲的邏輯
      // 獲取後自動觸發 RTP 更新

      // 示例：獲取完成後更新 RTP
      await dispatch('updateGameListsRtp', {
        listNames: ['allGameList']
      })
    } catch (error) {
      console.error('[gameHall] 獲取所有遊戲列表失敗:', error)
    }
  },

  async fetchPlayedGameList({ dispatch, rootGetters }) {
    const userName = rootGetters['role/userName']
    if (userName !== '' && userName !== undefined && userName !== null) {
      const serverGamesId = await this.$clientApi.game.gamePlayed(userName)
      const gameIds = serverGamesId.list.map(Number)
      await dispatch('setPlayedGameList', gameIds)
    } else {
      let storageJsonData
      if (process.client) storageJsonData = this.$localStorage.get('playedGameList').playedGameList
      const storageGameList = storageJsonData ? JSON.parse(storageJsonData) : []
      await dispatch('setPlayedGameList', storageGameList)
    }
  }
}
