const NUXT_ENV = process.env.NUXT_ENV
const STATION = process.env.STATION
const stationConfig = require(`./station/${STATION}/${NUXT_ENV}`).default
const vuetifyConfig = require(`./station/${STATION}/vuetify`).default
import WatcherConfig from './nuxtconfig/watchers'
const HeaderConfig = require(`./station/${STATION}/head`).default
import PluginsConfig from './nuxtconfig/plugins'
const pwaConfig = require(`./station/${STATION}/pwa`).default
import buildConfig from './nuxtconfig/build'
import imageConfig from './nuxtconfig/image'
import renderConfig from './nuxtconfig/render'
const router = require(`./station/${STATION}/router`).default
process.env.DEBUG = 'nuxt:*'

export default {
  env: stationConfig.env,
  ...WatcherConfig,
  ...HeaderConfig,
  ...PluginsConfig,
  ...pwaConfig,
  ...buildConfig,
  ...vuetifyConfig,
  ...imageConfig,
  ...renderConfig,
  ...router,
  i18n: stationConfig.i18n,

  loading: '@/components/loading.vue',
  // Auto import components (https://go.nuxtjs.dev/config-components)
  components: false,
  // Modules for dev and build (recommended) (https://go.nuxtjs.dev/config-modules)
  buildModules: [
    // https://go.nuxtjs.dev/eslint
    '@nuxtjs/eslint-module',
    // https://go.nuxtjs.dev/vuetify
    '@nuxtjs/vuetify',
    '@nuxtjs/pwa',
    '@nuxtjs/style-resources'
  ],

  // Modules (https://go.nuxtjs.dev/config-modules)
  modules: [
    // https://go.nuxtjs.dev/axios
    // '@nuxtjs/proxy',
    '@nuxtjs/axios',
    '@nuxtjs/device',
    'cookie-universal-nuxt',
    'nuxt-i18n',
    [
      '@nuxt/content',
      {
        liveEdit: !process.env.NUXT_ENV === 'development',
        watch: !process.env.NUXT_ENV === 'development',
        websocket: !process.env.NUXT_ENV === 'development',
        dir: 'content', // 明確指定內容目錄，避免掃描 docs
        ignore: ['docs/**'] // 明確忽略 docs 資料夾
      }
    ],
    '@nuxtjs/sitemap',
    '@nuxtjs/svg',
    '@nuxtjs/robots',
    'nuxt-user-agent',
    [
      'nuxt-facebook-pixel-module',
      {
        pixelId: '351779695487574',
        track: 'PageView',
        autoPageView: true,
        disabled: true,
        debug: false
      }
    ],
    [
      'nuxt-compress',
      {
        gzip: {
          threshold: 8192
        },
        brotli: {
          threshold: 8192
        }
      }
    ],
    '@nuxt/image',
    ['@nuxtjs/dotenv', { systemvars: true }],
    '@nuxtjs/gtm'
  ],
  sitemap: {
    path: '/sitemap.xml',
    hostname: process.env.BASE_URL,
    i18n: true,
    defaults: {
      lastmod: new Date(),
      lastmodrealtime: true
    },
    filter({ routes }) {
      return routes.filter(
        (route) => !route.path.includes('/player') && !route.path.includes('/guild')
      )
    }
  },
  //官網 https://nuxt.com/modules/robots
  robots: [
    {
      UserAgent: '*', // 適用於所有搜索引擎機器人
      Disallow: [
        // Facebook
        '/*?fbclid=',
        '/*?fb_action_ids=',
        '/*?fb_action_types=',
        '/*?fb_source=',
        '/*?fbclientids=',
        '/*?fbref=',
        // Google
        '/*?gclid=',
        '/*?gclsrc=',
        '/*?dclid=',
        // Twitter, LinkedIn and other UTM sources
        '/*?utm_source=',
        // Instagram
        '/*?igshid=',
        // Advertising and Promotion
        '/*?utm_medium=',
        '/*?utm_campaign=',
        '/*?utm_content=',
        '/*?utm_term=',
        '/*?promote=',
        // Other parameters
        '/*?ref=',
        '/*?source=',
        '/*?mc_cid=',
        '/*?mc_eid='
      ]
    },
    {
      Sitemap: (req) =>
        `${req.headers['x-forwarded-proto'] || 'http'}://${
          req.headers['x-forwarded-host'] || req.headers.host
        }/sitemap.xml`
    }
  ],
  gtm: {
    enabled: true /* see below */,
    debug: false
  },
  serverMiddleware: [
    {
      path: '/healthy',
      handler: (req, res) => {
        res.setHeader('Content-Type', 'application/json')
        res.end('OK!')
      }
    }
  ],

  layouts: {
    // 設定默認佈局的路徑
    //為什麼不直接放在layouts資料夾下就好呢?
    //因為在開站台或是build時，layouts資料夾底下的所有檔案會直接編譯過
    //檔案若有缺失就會直接報錯
    default: `~/layouts_station/${STATION}_default.vue`
  }
}
