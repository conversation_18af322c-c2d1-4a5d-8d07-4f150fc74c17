<template>
  <v-container fluid :class="['pa-0', { 'notch-left': hasLeftNotch }]">
    <!-- Top Banner -->
    <v-row
      no-gutters
      justify="center"
      id="bannerRow"
      :class="['pa-4 pa-md-6 px-lg-0 px-xl-0', { 'notch-right': hasRightNotch }]"
    >
      <v-col cols="12" lg="9" class="mw-75-v">
        <banner :banner-adapt="bannerAdapt" />
      </v-col>
    </v-row>
    <!-- marquee -->
    <v-row no-gutters justify="center" class="mb-6 px-4 px-md-6 pa-lg-0">
      <v-col cols="12" lg="9" class="mw-75-v" :class="{ 'notch-right': hasRightNotch }">
        <marquee />
      </v-col>
    </v-row>
    <!-- Grand Prize -->
    <v-row
      justify="center"
      no-gutters
      :class="['text-center mb-4', { 'notch-right': hasRightNotch }]"
    >
      <v-col cols="12" lg="9" class="px-lg-0 px-md-6 px-4 mw-75-v">
        <v-lazy
          :options="{
            threshold: 0.3
          }"
          transition="fade-transition"
        >
          <grandPrizeNoty />
        </v-lazy>
      </v-col>
    </v-row>
    <!-- Games Content -->
    <v-row class="text-center mb-md-10 mb-6" justify="center" no-gutters>
      <v-col cols="12" lg="9" class="px-lg-0 px-md-6 px-4 mw-75-v">
        <!-- inCompleteGame Content -->
        <inCompleteGame :prop-game-list="inCompleteGameList" />
        <!-- recentGames Content -->
        <recentGames :prop-game-list="recentGameList" />
        <!-- liveGames Content -->
        <v-lazy
          :options="{
            threshold: 0.3
          }"
          transition="fade-transition"
        >
          <liveGameList
            :prop-game-list="liveGameList"
            :prop-game-sort-type="liveGameSet.sortType"
            :game-category-id="liveGameSet.category"
            :block-title-capital="false"
          />
        </v-lazy>
        <!-- hotGames Content -->
        <v-lazy
          :options="{
            threshold: 0.3
          }"
          transition="fade-transition"
        >
          <popularGameList
            :prop-game-list="hotGameList"
            :prop-game-sort-type="popularGameSet.hotGame.sortType"
            :game-category-id="popularGameSet.hotGame.category"
            block-title="slot_short_hot"
            :block-title-capital="false"
          />
        </v-lazy>
        <!-- fishingGames Content -->
        <v-lazy
          :options="{
            threshold: 0.3
          }"
          transition="fade-transition"
        >
          <popularGameList
            :prop-game-list="fishingGameList"
            :prop-game-sort-type="popularGameSet.fishingGame.sortType"
            :game-category-id="popularGameSet.fishingGame.category"
            block-title="fishing_game_hot"
            :block-title-capital="false"
          />
        </v-lazy>
        <!-- featuredGames Content -->
        <v-lazy
          :options="{
            threshold: 0.3
          }"
          transition="fade-transition"
        >
          <featuredGameList :prop-game-list="featuredGameList" :block-title-capital="false" />
        </v-lazy>
        <!-- chessAndCard games Content -->
        <v-lazy
          :options="{
            threshold: 0.3
          }"
          transition="fade-transition"
        >
          <popularGameList
            :prop-game-list="chessAndCardGameList"
            :prop-game-sort-type="popularGameSet.chessAndCardGame.sortType"
            :game-category-id="popularGameSet.chessAndCardGame.category"
            block-title="chess_and_card_game_hot"
            :block-title-capital="false"
          />
        </v-lazy>
        <!-- newestGames Content -->
        <v-lazy
          :options="{
            threshold: 0.3
          }"
          transition="fade-transition"
        >
          <newestGameList
            :prop-game-list="newestGameList"
            :prop-game-sort-type="newestGameSet.sortType"
            :block-title-capital="false"
          />
        </v-lazy>
      </v-col>
    </v-row>

    <!-- PWA -->
    <v-lazy
      :options="{
        threshold: 0.3
      }"
      transition="fade-transition"
      :class="{ 'notch-right': hasRightNotch }"
    >
      <v-row class="text-center justify-center" no-gutters>
        <v-col cols="12" lg="9" class="pt-4 pt-sm-6 pb-0 pb-sm-2 px-lg-0 px-md-6 px-4 mw-75-v">
          <pwa-install-block />
        </v-col>
      </v-row>
    </v-lazy>

    <!-- App Download -->
    <v-lazy
      v-if="appDownload"
      :options="{
        threshold: 0.3
      }"
      transition="fade-transition"
      :class="{ 'notch-right': hasRightNotch }"
    >
      <v-row class="text-center justify-center" no-gutters>
        <v-col cols="12" lg="9" class="pt-6 px-lg-0 px-md-6 px-4 mw-75-v">
          <app-download />
        </v-col>
      </v-row>
    </v-lazy>

    <!-- Games Content End -->
  </v-container>
</template>

<script>
  const STATION = process.env.STATION
  import orientation from '@/mixins/orientation.js'
  export default {
    name: 'Index',
    mixins: [require(`~/mixins_station/${STATION}/homeGameList`).default, orientation],
    components: {
      banner: () => import('~/components/banner'),
      grandPrizeNoty: () => import(`~/components/grandPrize/grandPrizeNoty`),
      recentGames: () => import(`~/components/game/recentGames`),
      liveGameList: () => import(`~/components/game/liveGameList`),
      featuredGameList: () => import(`~/components/game/featuredGameList`),
      popularGameList: () => import(`~/components/game/popularGameList`),
      pwaInstallBlock: () => import('~/components/pwa/pwaInstallBlock'),
      appDownload: () => import('~/components/appDownload'),
      marquee: () => import('~/components/marquee'),
      newestGameList: () => import('~/components/game/newestGameList'),
      inCompleteGame: () => import('~/components/game/inCompleteGame')
    },
    data() {
      return {
        banner: []
      }
    },
    computed: {
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      },
      bannerSource({ banner }) {
        const source = {}
        const self = this

        // 防護性檢查：確保 banner 是陣列且有內容
        if (Array.isArray(banner) && banner.length > 0) {
          banner.forEach((item) => {
            // 確保必要的屬性存在
            if (!item || !item.source || !item.thumbs) {
              console.warn('[Banner] ⚠️ 跳過無效的橫幅項目:', item)
              return
            }

            source[item.source] = {
              event: () => {
                switch (item.displayMethod) {
                  case 'new_window':
                    if (item.link !== null) {
                      this.$lineOpenWindow.open(item.link, '_blank')
                    }
                    break
                  case 'redirect':
                    if (item.link !== null) {
                      //若是網址內沒有http則直接跳轉內部網頁
                      if (!/http/.test(item.link)) {
                        self.$router.push({ path: this.localePath(item.link) })
                      } else if (item.link.includes(location.origin)) {
                        //若是網址內有http且網址內有location.origin則直接跳轉內部網頁
                        self.$router.push({
                          path: this.localePath(item.link.split(location.origin)[1])
                        })
                      } else {
                        window.location.href = item.link
                      }
                    }
                    break
                }
              }
            }
            source[item.source][this.$i18n.locale] = {
              lg: '',
              xs: ''
            }

            // 防護性檢查：確保 thumbs 是陣列
            if (Array.isArray(item.thumbs)) {
              item.thumbs.forEach((img) => {
                if (img && img.lang && img.sizeCode && img.thumbUrl) {
                  source[item.source][img.lang][img.sizeCode] = img.thumbUrl
                }
              })
            }
          })
        } else {
          console.warn('[Banner] 📝 橫幅資料為空或無效，顯示空狀態')
        }

        return source
      },
      bannerAdapt({ bannerSource, $i18n: { locale }, $vuetify: { breakpoint } }) {
        const src = {}
        const point = breakpoint.mdAndUp ? 'lg' : 'xs'
        for (const key in bannerSource) {
          if (!src[key]) {
            src[key] = {}
          }
          src[key].src = bannerSource[key][locale][point]
          src[key].event = bannerSource[key].event
        }
        return src
      },
      appDownload() {
        return this.$UIConfig.lock.appDownload
      }
    },
    async created() {
      await this.getBanners()
      await this.$store.dispatch('gameProvider/fetch')
      if (this.$route.params.inviteCode !== undefined) {
        this.$store.commit('event/SET_INVITE_CODE', this.$route.params.inviteCode)
        this.$store.commit('social/SET_PROMOTE', this.$route.params.inviteCode)
      }
      if (process.client) {
        await this.init()
      }
    },
    async mounted() {
      // line webview 好貼心，直接加參數就好
      if (this.$ua['_ua'].includes('Line')) {
        await this.lineWebPageJump()
      }
    },
    methods: {
      async getBanners() {
        try {
          const bannerData = await this.$clientApi.banner.lists({ lang: this.$i18n.locale })

          if (!bannerData.errorCode) {
            this.banner = bannerData.data.list
          } else {
            this.$notify.backendError(bannerData.errorCode)
          }
        } catch (error) {
          // API 層級已處理 Mock 資料，這裡只需要記錄日誌
          console.error('[Banner] 獲取失敗:', error)
          this.banner = []
        }
      }
    },
    watch: {
      '$route.query.promote': {
        immediate: true,
        async handler(promote) {
          if (promote) {
            // 公會長推薦碼，登入時送給總部SERVER
            this.$store.commit('social/SET_PROMOTE', promote)
          }
        }
      },
      'maintainSystem.0.maintaining': {
        handler(val) {
          // 平台維護檢查
          if (val === false) {
            this.getBanners()
          }
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .border-line {
    &::after {
      width: 99.7%;
      height: 99.4%;
    }
  }

  @media screen and (max-width: 600px) {
    .border-line {
      &::after {
        width: 99.5%;
        height: 99.7%;
      }
    }
  }
  @media (orientation: landscape) {
    .notch-left {
      padding-left: env(safe-area-inset-left) !important;
    }
    .notch-right {
      padding-right: env(safe-area-inset-right) !important;
    }
    #bannerRow {
      &.notch-right {
        padding-right: calc(16px + env(safe-area-inset-right)) !important;
      }
    }
    /* md*/
    @media (min-width: 960px) and (max-width: 1263px) {
      #bannerRow {
        &.notch-right {
          padding-right: calc(24px + env(safe-area-inset-right)) !important;
        }
      }
    }
    /* lg */
    @media (min-width: 1264px) {
      #bannerRow {
        &.notch-right {
          padding-right: calc(0px + env(safe-area-inset-right)) !important;
        }
      }
    }
  }
</style>
