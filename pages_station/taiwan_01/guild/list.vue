<template>
  <v-container fluid class="pt-0 pt-md-16 px-0 pb-0 pb-md-15">
    <v-row no-gutters justify="center" :class="$vuetify.breakpoint.smAndDown ? '' : 'pt-6'">
      <v-col xl="8" lg="10" cols="12">
        <v-row no-gutters justify="start" class="pl-0 pl-md-1">
          <guildListContent v-if="isLogin" style="width: 100%" />
        </v-row>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
  import menu from '~/mixins/menu.js'

  export default {
    name: 'GuildListPage',
    mixins: [menu],
    components: {
      guildListContent: () => import('@/components/guild/guildListContent')
    },
    data() {
      return {}
    },
    computed: {
      isLogin() {
        return this.$store.getters['role/isLogin']
      }
    },
    watch: {
      isLogin: {
        handler(val) {
          if (!val) this.$router.push({ path: '/' })
        },
        immediate: true
      }
    },
    mounted() {
      if (!this.isLogin) {
        this.$nextTick(() => {
          this.$notify.info(this.$t('plz_login'))
          this.$router.push({ path: '/' })
        })
      }
    },
    methods: {}
  }
</script>

<style></style>
