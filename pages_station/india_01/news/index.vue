<template>
  <v-container class="pb-15" :class="{ 'notch-left': hasLeftNotch, 'notch-right': hasRightNotch }">
    <!-- title -->
    <v-row justify="center" no-gutters>
      <div class="pt-8">
        <linearGradientTitle :title="$t('announcement').toUpperCase()" />
      </div>
    </v-row>
    <v-row justify="center" class="mt-10" no-gutters>
      <v-col xl="8" lg="10" cols="12" class="px-md-0 px-4">
        <div class="border-line rounded-xl">
          <v-card
            color="transparent"
            class="rounded-xl pa-2 card-bg"
            elevation="6"
            style="z-index: 4"
          >
            <!-- tabs -->
            <v-tabs
              v-model="tab"
              background-color="transparent"
              center-active
              color="primary"
              class="mt-2"
              :class="$vuetify.breakpoint.width <= 420 ? 'px-0' : 'px-6'"
              show-arrows
              :centered="$vuetify.breakpoint.width <= 420"
            >
              <v-tab
                v-for="item in newsCategory"
                :key="item.type"
                :href="`#${item.type}`"
                @click="() => handleTabAndPageChange({ type: item.type })"
                >{{ item.title }}</v-tab
              >
            </v-tabs>
            <v-card-text class="px-0">
              <!-- content -->
              <client-only>
                <v-tabs-items v-model="tab" class="transparent" continuous>
                  <v-tab-item v-for="item in newsData" :key="item.type" :value="item.type">
                    <v-row class="news-content" justify="center" no-gutters>
                      <!-- 打API途中，顯示loading -->
                      <v-col v-if="isInnerLoading" class="d-flex justify-center align-center">
                        <v-progress-circular
                          :width="3"
                          indeterminate
                          class="text--secondary"
                          style="width: 23px; height: 23px"
                        ></v-progress-circular>
                      </v-col>
                      <!-- API打完確認沒有資料且不在loading中，顯示無資料訊息 -->
                      <v-col
                        v-else-if="!isInnerLoading && item.data.length === 0"
                        class="grey-3--text d-flex justify-center align-center"
                      >
                        <span style="font-size: 20px">
                          {{ noNewsMessage(item) }}
                        </span>
                      </v-col>
                      <newsList
                        v-show="!isInnerLoading && item.data.length > 0"
                        ref="expandComponent"
                        :show-news-arr="item.data"
                        :mode="1"
                        class="w-100"
                      />
                      <!-- pagination -->
                      <div v-show="!isInnerLoading && item.count > 0" class="pt-6 w-100">
                        <v-pagination
                          :total-visible="7"
                          v-model="pagination.page"
                          :length="item.pages"
                          circle
                          @input="(page) => handleTabAndPageChange({ page })"
                        ></v-pagination>
                      </div>
                    </v-row>
                  </v-tab-item>
                </v-tabs-items>
              </client-only>
            </v-card-text>
          </v-card>
        </div>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
  import newsUrlRelate from '@/mixins/newsUrlRelate'
  import orientation from '@/mixins/orientation.js'
  import newsList from '@/components/news'

  const STATION = process.env.STATION
  export default {
    name: 'NewsIndex',
    mixins: [newsUrlRelate, orientation],
    components: {
      newsList,
      linearGradientTitle: () => import(`~/components_station/${STATION}/linearGradientTitle`)
    },
    created() {
      this.checkMaintain()
    },
    computed: {
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      }
    },
    methods: {
      checkMaintain() {
        // 平台維護檢查已由 layout 處理
        }
      }
    }
  }
</script>
<style lang="scss" scoped>
  $primary: map-get($colors, 'primary');
  .v-tab {
    color: rgba(255, 255, 255, 0.6) !important;
    &--active {
      color: $primary !important;
    }
  }
  .card-bg {
    background-image: linear-gradient(180deg, #09315e 0%, #091842 100%);
  }
  .oversea-wordBreak::v-deep .news-word-break {
    word-break: keep-all;
  }
  .news-content {
    min-height: 180px;
  }
  @media (orientation: landscape) {
    .notch-left {
      padding-left: env(safe-area-inset-left) !important;
    }
    .notch-right {
      padding-right: env(safe-area-inset-right) !important;
    }
  }
</style>
