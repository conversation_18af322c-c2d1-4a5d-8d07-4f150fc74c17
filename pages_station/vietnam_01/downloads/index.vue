<template>
  <v-container class="pb-14">
    <v-row>
      <v-col class="d-flex justify-center mt-4">
        <div class="pa-2">
          <linearGradientTitle class="policy-title" :title="$t('download_xincity')" />
        </div>
      </v-col>
    </v-row>
    <!-- 星城Online APP -->
    <v-row class="justify-center">
      <v-col cols="12" sm="9" md="6" lg="5" xl="4">
        <v-img width="570px" height="100%" :src="appCover" />
      </v-col>
      <v-col
        cols="12"
        md="6"
        lg="5"
        xl="4"
        :class="{ 'notch-left': hasLeftNotch, 'notch-right': hasRightNotch }"
      >
        <v-card id="app" color="transparent" elevation="0" class="mt-0 mt-sm-6">
          <!-- title -->
          <div
            class="d-flex gradient-title--text font-weight-bold custom-text-noto"
            :class="breakpoint.smAndDown ? 'text-h5 flex-row' : 'text-h4 flex-column'"
          >
            <!-- 星城Online -->
            <div
              class="gradient-title--text font-weight-bold custom-text-noto pl-4 pr-1"
              :class="breakpoint.smAndDown ? 'text-h5' : 'text-h4'"
            >
              {{ $t('xincity') }}
            </div>
            <!-- APP -->
            <v-card-subtitle :class="breakpoint.xs || breakpoint.sm ? 'pa-0' : 'py-2'">
              <span
                class="gradient-title--text font-weight-bold custom-text-noto"
                :class="breakpoint.smAndDown ? 'text-h5' : 'text-h4'"
              >
                APP
              </span>
            </v-card-subtitle>
          </div>

          <v-card-text class="pb-0">
            <p class="custom-text-noto text-subtitle-1 default-content--text">
              {{ $t('downloads_description1') }}
            </p>
          </v-card-text>
        </v-card>
        <!-- 2download btn  -->
        <v-card class="d-flex mt-2 px-4" color="transparent" elevation="0">
          <div>
            <v-img
              position="left"
              contain
              width="145px"
              height="50px"
              class="cursor-pointer"
              :src="getImage('downloads/' + this.$i18n.locale + '/' + appDownloadList[0].img)"
              @click="goto('IOS', appDownloadList[0].url)"
            />
          </div>
          <div>
            <v-img
              position="left"
              contain
              width="175px"
              height="50px"
              class="cursor-pointer"
              :src="getImage('downloads/' + this.$i18n.locale + '/' + appDownloadList[1].img)"
              @click="goto('android', appDownloadList[1].url)"
            />
          </div>
        </v-card>
      </v-col>
    </v-row>
    <!-- 星城Online 電腦版 -->
    <v-row
      class="flex-wrap-reverse justify-center"
      :class="{
        'flex-column-reverse': breakpoint.xsOnly
      }"
    >
      <v-col
        cols="12"
        md="5"
        lg="5"
        xl="4"
        :class="{ 'notch-left': hasLeftNotch, 'notch-right': hasRightNotch }"
      >
        <v-card id="pc" color="transparent" elevation="0" class="mt-0 mt-md-6">
          <!-- title -->
          <div
            class="d-flex flex-nowrap gradient-title--text font-weight-bold custom-text-noto"
            :class="breakpoint.smAndDown ? 'text-h5 flex-row' : 'text-h4 flex-column'"
          >
            <!-- 星城Online -->
            <div
              class="gradient-title--text font-weight-bold custom-text-noto pl-4 pr-1"
              :class="breakpoint.smAndDown ? 'text-h5' : 'text-h4'"
            >
              {{ $t('xincity') }}
              <span
                v-if="breakpoint.xsOnly"
                class="gradient-title--text font-weight-bold custom-text-nototext-h5"
              >
                {{ $t('pc_version') }}
              </span>
            </div>
            <!-- 電腦版 -->
            <v-card-subtitle
              v-if="!breakpoint.xsOnly"
              :class="breakpoint.xs || breakpoint.sm ? 'pa-0' : 'py-2'"
            >
              <span
                class="gradient-title--text font-weight-bold custom-text-noto"
                :class="breakpoint.smAndDown ? 'text-h5' : 'text-h4'"
              >
                {{ $t('pc_version') }}
              </span>
            </v-card-subtitle>
          </div>

          <v-card-text class="pb-0">
            <p class="custom-text-noto text-subtitle-1 default-content--text">
              {{ $t('downloads_description2') }}
            </p>
          </v-card-text>
        </v-card>
        <v-card class="mt-2 px-4" color="transparent" elevation="0">
          <v-img
            position="left"
            contain
            width="145px"
            height="50px"
            class="cursor-pointer"
            :src="getImage('downloads/' + this.$i18n.locale + '/' + appDownloadList[2].img)"
            @click="goto('android', appDownloadList[1].url)"
          />
        </v-card>
      </v-col>
      <v-col cols="12" sm="9" md="7" lg="5" xl="4">
        <v-img width="570px" height="100%" :src="pcCover" />
      </v-col>
    </v-row>

    <!-- 硬體需求title -->
    <v-row>
      <v-col>
        <v-row class="justify-center">
          <div class="pa-2">
            <linearGradientTitle class="policy-title" :title="$t('hardware_requirements')" />
          </div>
        </v-row>
      </v-col>
    </v-row>
    <!-- 硬體需求內容 -->
    <v-row class="justify-center">
      <v-col
        cols="12"
        lg="10"
        xl="8"
        :class="{ 'notch-left': hasLeftNotch, 'notch-right': hasRightNotch }"
      >
        <div class="border-line rounded-xl">
          <v-card color="transparent" class="pa-6 rounded-xl">
            <v-tabs show-arrows background-color="transparent" color="primary" class="pb-2">
              <v-tab class="custom-text-noto" @click="hrType = 'ios'"> iOs </v-tab>
              <v-tab class="custom-text-noto" @click="hrType = 'android'"> Android </v-tab>
              <v-tab class="custom-text-noto" @click="hrType = 'windows'">
                {{ $t('pc_version') }}
              </v-tab>
            </v-tabs>

            <nuxt-content v-if="hrType === 'ios'" :document="ios" />
            <nuxt-content v-else-if="hrType === 'android'" :document="android" />
            <nuxt-content v-else-if="hrType === 'windows'" :document="windows" />
          </v-card>
        </div>
      </v-col>
    </v-row>
    <!-- 電腦版下載dialog -->
    <v-dialog
      v-model="showWindowsDowanloadDialog"
      persistent
      max-width="600px"
      content-class="rounded-lg"
    >
      <!-- title -->
      <customDialogTitle
        :title="$t('pc_version').toUpperCase() + $t('download').toUpperCase()"
        @closeDialog="showWindowsDowanloadDialog = false"
      />
      <v-card id="download-card" class="rounded-0 dialog-fill scrollable pa-4 pa-sm-6">
        <v-container fluid>
          <!-- win10 版本以上 -->
          <v-row no-gutters>
            <v-col>
              <span class="default-content--text"> win10 {{ $t('version') + $t('over_up') }} </span>
            </v-col>
          </v-row>
          <v-row class="ma-0">
            <v-col
              cols="6"
              md="3"
              sm="6"
              :class="[
                'px-2 py-2 py-md-4',
                (idx + 1) % (breakpoint.smAndDown ? 2 : 4) === 1 ? 'pl-0' : '',
                (idx + 1) % (breakpoint.smAndDown ? 2 : 4) === 0 ? 'pr-0' : ''
              ]"
              v-for="(download, idx) in winDownloadList"
              :key="idx"
              class="py-4"
            >
              <v-card class="rounded-0" color="transparent" elevation="0">
                <v-btn
                  :loading="download.status"
                  :disabled="download.status"
                  outlined
                  color="primary"
                  style="width: 100%"
                  @click="goDownload('7', idx)"
                >
                  <v-icon right dark v-if="idx == 3"> mdi-tray-arrow-down</v-icon>
                  <v-icon right dark v-else> mdi-google-drive </v-icon>
                  <span class="pl-2 primary--text">
                    {{ $t('download_address') + (idx + 1) }}
                  </span>
                </v-btn>
              </v-card>
            </v-col>
          </v-row>
          <v-divider class="mt-4 mt-md-2" />
          <!-- XP版本 -->
          <v-row no-gutters>
            <v-col class="pt-4">
              <span class="default-content--text"> XP {{ $t('version') }} </span>
            </v-col>
          </v-row>
          <v-row class="ma-0">
            <v-col
              cols="6"
              md="3"
              sm="6"
              :class="[
                'px-2 pb-0 pt-2 pt-md-4',
                (idx + 1) % (breakpoint.smAndDown ? 2 : 4) === 1 ? 'pl-0' : '',
                (idx + 1) % (breakpoint.smAndDown ? 2 : 4) === 0 ? 'pr-0' : ''
              ]"
              v-for="(download, idx) in winXPDownloadList"
              :key="idx"
              class="pt-4 pb-0"
            >
              <v-card class="rounded-0" color="transparent" elevation="0">
                <v-btn
                  :loading="download.status"
                  :disabled="download.status"
                  outlined
                  color="primary"
                  style="width: 100%"
                  @click="goDownload('xp', idx)"
                >
                  <v-icon right dark> mdi-tray-arrow-down </v-icon>
                  <span class="pl-2 primary--text">
                    {{ $t('download') }}
                  </span>
                </v-btn>
              </v-card>
            </v-col>
          </v-row>
        </v-container>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
  import analytics from '@/mixins/analytics.js'
  import appDownload from '@/mixins/appDownload.js'
  import images from '~/mixins/images'
  import orientation from '@/mixins/orientation.js'
  const STATION = process.env.STATION
  export default {
    name: 'IndexAppDownload',
    mixins: [analytics, appDownload, images, orientation],
    components: {
      customDialogTitle: () => import('~/components/customDialogTitle'),
      linearGradientTitle: () => import(`~/components_station/${STATION}/linearGradientTitle`)
    },
    async asyncData({ app, $content }) {
      let ios = {}
      let android = {}
      let windows = {}

      try {
        ios = await $content(`articles/${app.i18n.locale}/hr_ios`).fetch()
        android = await $content(`articles/${app.i18n.locale}/hr_android`).fetch()
        windows = await $content(`articles/${app.i18n.locale}/hr_windows`).fetch()
      } catch (loadErr) {
        console.log(loadErr)
      }

      return {
        ios,
        android,
        windows
      }
    },
    data() {
      const appDownloadList = [
        {
          img: 'app_store_download.svg',
          name: 'IOS',
          url: 'https://apps.apple.com/vn/app/vua-tr%C3%B2-ch%C6%A1i-online-vui-b%E1%BA%AFn-c%C3%A1/id1542157640'
        },
        {
          img: 'google_play_download.svg',
          name: 'android',
          url: 'https://play.google.com/store/apps/details?id=vn.esgame.luckystar'
        },
        {
          img: 'windows_download.svg',
          name: 'windows',
          url: 'https://vtco.esgame.vn/?news=https://vtco.esgame.vn/a/huong-dan-tai-game-ban-pc'
        }
      ]
      const winDownloadList = [
        {
          url: 'https://vtco.esgame.vn/?news=https://vtco.esgame.vn/a/huong-dan-tai-game-ban-pc',
          status: false
        }
      ]

      const winXPDownloadList = [
        {
          url: 'https://drive.google.com/open?id=1kHWMZs_fC2ZZOe-gIEodsOcy7E0kHuwE',
          status: false
        }
      ]

      return {
        appDownloadList,
        hrType: 'ios',
        pcCover: process.env.IMAGE_URL + '/app_download/xinstar_download_pc.webp',
        appCover: process.env.IMAGE_URL + '/app_download/xinstar_download_app.webp',
        showWindowsDowanloadDialog: false,
        winDownloadList,
        winXPDownloadList
      }
    },

    created() {
      //在data inital的時候是無法取得this.$device.isDesktop的值，所以要在這邊重新賦值，且在data的特性為一次性賦值
      this.appDownloadList.forEach((item) => {
        if (item.name === 'android') item.url = this.androidUrl
        if (item.name === 'IOS') item.url = this.iosUrl
      })
      this.checkMaintain()
    },
    computed: {
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    methods: {
      checkMaintain() {
        // 平台維護檢查
        // 平台維護檢查已由 layout 處理
      },
      goto(name, url) {
        this.$lineOpenWindow.open(url, 'redirect')
        let label = ''
        switch (name) {
          case 'IOS':
            label = 'dlhdios'
            break
          case 'android':
            label = 'dlhdapk'
            break

          default:
            break
        }
        this.downloadClickAnalytics(label)
      },
      goDownload(version, idx) {
        let url = ''
        if (version === '7') {
          this.winDownloadList[idx].status = true
          url = this.winDownloadList[idx].url
        } else {
          this.winXPDownloadList[idx].status = true
          url = this.winDownloadList[idx].url
        }

        setTimeout(() => {
          if (version === '7') {
            this.winDownloadList[idx].status = false
          } else {
            this.winXPDownloadList[idx].status = false
          }
        }, 3000)

        this.$lineOpenWindow.open(url, '_blank')

        this.downloadClickAnalytics('dlpc')
      }
    }
  }
</script>
<style lang="scss" scoped>
  .border-line {
    &::after {
      width: 99.5%;
      height: 99.8%;
    }
  }

  @media screen and (max-width: 600px) {
    .border-line {
      &::after {
        width: 99.5%;
        height: 99.8%;
      }
    }
    .policy-title {
      white-space: break-spaces;
      text-align: center;
    }
  }
  #download-card {
    &.scrollable {
      max-height: calc(90vh - 52px);
      overflow-y: auto;
    }
    @supports (height: 90svh) {
      &.scrollable {
        max-height: calc(90svh - 52px);
      }
    }
  }
  @media (orientation: landscape) {
    .notch-left {
      padding-left: calc(12px + env(safe-area-inset-left)) !important;
    }
    .notch-right {
      padding-right: calc(12px + env(safe-area-inset-right)) !important;
    }
  }
</style>
