<template>
  <v-container class="pwaContainer px-0">
    <!-- title -->
    <v-row justify="center" no-gutters>
      <div class="pt-8">
        <linearGradientTitle :title="$t('created_shortcut_tutorial')" />
      </div>
    </v-row>

    <!-- 標籤 -->
    <v-tabs
      ref="tabs"
      background-color="transparent"
      color="primary"
      class="mt-6"
      :class="$vuetify.breakpoint.lgAndUp ? 'mb-11' : 'mb-6'"
      v-model="selectType"
      show-arrows
      centered
    >
      <v-tab class="a-link" href="#ios" key="ios"> IOS </v-tab>
      <v-tab class="a-link" href="#android" key="android"> ANDROID </v-tab>
      <v-tab class="a-link" href="#pc" key="pc"> PC </v-tab>
    </v-tabs>

    <!-- 內容 -->
    <v-tabs-items v-model="selectType">
      <!-- ios -->
      <v-tab-item key="ios" value="ios">
        <v-row
          justify="center"
          no-gutters
          :class="{ 'notch-left': hasLeftNotch, 'notch-right': hasRightNotch }"
        >
          <v-col
            xl="8"
            lg="10"
            cols="12"
            class="item-content"
            :class="$vuetify.breakpoint.lgAndUp ? 'bg-color' : ''"
          >
            <v-row
              class="d-flex pa-8"
              :class="[
                $vuetify.breakpoint.lgAndUp || $vuetify.breakpoint.xsOnly
                  ? 'flex-row'
                  : 'flex-column'
              ]"
              no-gutters
              align="center"
            >
              <v-col
                class="d-flex"
                :class="[
                  $vuetify.breakpoint.lgAndUp || $vuetify.breakpoint.xsOnly
                    ? 'flex-column'
                    : 'flex-row'
                ]"
                :style="$vuetify.breakpoint.lgAndUp ? 'max-width:560px' : 'max-width:780px'"
              >
                <!-- text -->
                <v-row no-gutters class="flex-column">
                  <v-col no-gutters>{{ $t('pwa_ios_text_1') }}</v-col>
                  <v-col no-gutters class="mt-4 d-flex flex-nowrap align-center flex-row">
                    <div
                      class="li-title custom-text-noto secondary-variant-1--text text-body-1 mr-2"
                    >
                      1
                    </div>
                    <div class="li-text custom-text-noto text-body-1">
                      {{ $device.isMobile ? $t('pwa_ios_text_2_in_mobile') : $t('pwa_ios_text_2') }}
                    </div>
                  </v-col>
                  <v-col no-gutters class="mt-4 d-flex flex-nowrap align-center flex-row">
                    <div
                      class="li-title custom-text-noto secondary-variant-1--text text-body-1 mr-2"
                    >
                      2
                    </div>
                    <div class="li-text custom-text-noto text-body-1">
                      {{ $device.isMobile ? $t('pwa_ios_text_3_in_mobile') : $t('pwa_ios_text_3') }}
                    </div>
                  </v-col>
                  <v-col no-gutters class="mt-4 d-flex flex-nowrap align-center flex-row">
                    <div
                      class="li-title custom-text-noto secondary-variant-1--text text-body-1 mr-2"
                    >
                      3
                    </div>
                    <div class="li-text custom-text-noto text-body-1">
                      {{ $t('pwa_ios_text_4') }}
                    </div>
                  </v-col>
                </v-row>
                <!-- qr code -->
                <v-row
                  v-if="!$device.isMobile || $vuetify.breakpoint.mdAndUp"
                  no-gutters
                  :class="$vuetify.breakpoint.xsOnly ? 'mt-6 justify-center' : 'mt-6 ml-11'"
                >
                  <div class="qr-code-img">
                    <figure class="qrcode">
                      <qrcode tag="img" :value="QRCodeUrl" :options="{ width: 180 }" />
                    </figure>
                  </div>
                </v-row>
              </v-col>
              <v-spacer v-if="$vuetify.breakpoint.lgAndUp"></v-spacer>
            </v-row>
            <!-- img -->
            <div
              class="right-img-content"
              v-if="selectType === 'ios' && $vuetify.breakpoint.lgAndUp"
            >
              <v-img
                width="570px"
                height="100%"
                :src="getImage(isAppleDevice ? 'pwa/pwa_ios.gif' : 'pwa/pwa_ios.webp')"
              />
            </div>
          </v-col>
        </v-row>
        <div
          class="in-mobile-img-content"
          v-if="selectType === 'ios' && $vuetify.breakpoint.mdAndDown"
        >
          <div class="in-mobile-img-bg bg-color"></div>
          <v-img
            class="in-mobile-img"
            :src="getImage(isAppleDevice ? 'pwa/pwa_ios.gif' : 'pwa/pwa_ios.webp')"
            contain
          />
        </div>
      </v-tab-item>
      <!-- android -->
      <v-tab-item key="android" value="android">
        <v-row
          justify="center"
          no-gutters
          :class="{ 'notch-left': hasLeftNotch, 'notch-right': hasRightNotch }"
        >
          <v-col
            xl="8"
            lg="10"
            cols="12"
            class="item-content"
            :class="$vuetify.breakpoint.lgAndUp ? 'bg-color' : ''"
          >
            <v-row
              class="d-flex pa-8"
              :class="[
                $vuetify.breakpoint.lgAndUp || $vuetify.breakpoint.xsOnly
                  ? 'flex-row'
                  : 'flex-column'
              ]"
              no-gutters
              align="center"
            >
              <v-col
                class="d-flex"
                :class="[
                  $vuetify.breakpoint.lgAndUp || $vuetify.breakpoint.xsOnly
                    ? 'flex-column'
                    : 'flex-row'
                ]"
                :style="$vuetify.breakpoint.lgAndUp ? 'max-width:560px' : 'max-width:780px'"
              >
                <!-- text -->
                <v-row no-gutters class="flex-column">
                  <v-col no-gutters class="mt-4 d-flex flex-nowrap align-center flex-row">
                    <div
                      class="li-title custom-text-noto secondary-variant-1--text text-body-1 mr-2"
                    >
                      1
                    </div>
                    <div class="li-text custom-text-noto text-body-1">
                      {{
                        $device.isMobile
                          ? $t('pwa_android_text_1_in_mobile')
                          : $t('pwa_android_text_1')
                      }}
                    </div>
                  </v-col>
                  <v-col no-gutters class="mt-4 d-flex flex-nowrap align-center flex-row">
                    <div
                      class="li-title custom-text-noto secondary-variant-1--text text-body-1 mr-2"
                    >
                      2
                    </div>
                    <div class="li-text custom-text-noto text-body-1">
                      {{ $t('pwa_android_text_2') }}
                    </div>
                  </v-col>
                  <v-col no-gutters class="mt-4 d-flex flex-nowrap align-center flex-row">
                    <div
                      class="li-title custom-text-noto secondary-variant-1--text text-body-1 mr-2"
                    >
                      3
                    </div>
                    <div class="li-text custom-text-noto text-body-1">
                      {{ $t('pwa_android_text_3') }}
                    </div>
                  </v-col>
                  <v-col no-gutters class="mt-4 d-flex flex-nowrap align-center flex-row">
                    <div class="custom-text-noto text-caption">
                      {{ $t('samsung_dont_use_pwa1') }}
                    </div>
                  </v-col>
                </v-row>
                <!-- qr code -->
                <v-row
                  v-if="!$device.isMobile || $vuetify.breakpoint.mdAndUp"
                  no-gutters
                  :class="$vuetify.breakpoint.xsOnly ? 'mt-6 justify-center' : 'mt-6 ml-11'"
                >
                  <div class="qr-code-img">
                    <figure class="qrcode">
                      <qrcode tag="img" :value="QRCodeUrl" :options="{ width: 180 }" />
                    </figure>
                  </div>
                </v-row>
              </v-col>
              <v-spacer v-if="$vuetify.breakpoint.lgAndUp"></v-spacer>
            </v-row>
            <!-- img -->
            <div
              class="right-img-content"
              v-if="selectType === 'android' && $vuetify.breakpoint.lgAndUp"
            >
              <v-img
                width="570px"
                height="100%"
                :src="getImage(isAppleDevice ? 'pwa/pwa_android.gif' : 'pwa/pwa_android.webp')"
              />
            </div>
          </v-col>
        </v-row>
        <div
          class="in-mobile-img-content"
          v-if="selectType === 'android' && $vuetify.breakpoint.mdAndDown"
        >
          <div class="in-mobile-img-bg bg-color"></div>
          <v-img
            class="in-mobile-img"
            :src="getImage(isAppleDevice ? 'pwa/pwa_android.gif' : 'pwa/pwa_android.webp')"
            contain
          />
        </div>
      </v-tab-item>
      <!-- pc -->
      <v-tab-item key="pc" value="pc">
        <v-row no-gutters justify="center" class="mt-2">
          <v-btn text @click="goto('chrome')">CHROME</v-btn>
          <v-btn text @click="goto('edge')">EDGE</v-btn>
        </v-row>
        <!-- CHROME教學 -->
        <v-row
          justify="center"
          no-gutters
          :class="{ 'notch-left': hasLeftNotch, 'notch-right': hasRightNotch }"
        >
          <v-col
            id="chrome"
            xl="8"
            lg="10"
            cols="12"
            class="item-content"
            :class="$vuetify.breakpoint.lgAndUp ? 'bg-color mt-16' : ''"
          >
            <v-row
              class="d-flex pa-8"
              :class="[
                $vuetify.breakpoint.lgAndUp || $vuetify.breakpoint.xsOnly
                  ? 'flex-row'
                  : 'flex-column'
              ]"
              no-gutters
              align="center"
            >
              <v-col
                class="d-flex"
                :class="[
                  $vuetify.breakpoint.lgAndUp || $vuetify.breakpoint.xsOnly
                    ? 'flex-column'
                    : 'flex-row'
                ]"
                :style="$vuetify.breakpoint.lgAndUp ? 'max-width:560px' : 'max-width:780px'"
              >
                <!-- text -->
                <v-row no-gutters class="flex-column">
                  <v-col
                    no-gutters
                    class="custom-text-noto text-h6 primary--text font-weight-medium"
                    >CHROME {{ $t('teach') }}</v-col
                  >
                  <v-col no-gutters class="mt-4 d-flex flex-nowrap align-center flex-row">
                    <div
                      class="li-title custom-text-noto secondary-variant-1--text text-body-1 mr-2"
                    >
                      1
                    </div>
                    <div class="li-text custom-text-noto text-body-1">
                      {{ $t('pwa_pc_chrome_text_1') }}
                    </div>
                  </v-col>
                  <v-col no-gutters class="mt-4 d-flex flex-nowrap align-center flex-row">
                    <div
                      class="li-title custom-text-noto secondary-variant-1--text text-body-1 mr-2"
                    >
                      2
                    </div>
                    <div class="li-text custom-text-noto text-body-1">
                      {{ $t('pwa_pc_chrome_text_2') }}
                    </div>
                  </v-col>
                  <v-col no-gutters class="mt-4 d-flex flex-nowrap align-center flex-row">
                    <div
                      class="li-title custom-text-noto secondary-variant-1--text text-body-1 mr-2"
                    >
                      3
                    </div>
                    <div class="li-text custom-text-noto text-body-1">
                      {{ $t('pwa_pc_chrome_text_3') }}
                    </div>
                  </v-col>
                </v-row>
              </v-col>
              <v-spacer v-if="$vuetify.breakpoint.lgAndUp"></v-spacer>
            </v-row>
            <!-- img -->
            <div
              class="right-img-content"
              v-if="selectType === 'pc' && $vuetify.breakpoint.lgAndUp"
            >
              <v-img
                width="470px"
                :src="getImage(isAppleDevice ? 'pwa/pwa_chrome.gif' : 'pwa/pwa_chrome.webp')"
              />
            </div>
          </v-col>
        </v-row>
        <div
          class="in-mobile-img-content my-10"
          v-if="selectType === 'pc' && $vuetify.breakpoint.mdAndDown"
        >
          <div class="in-mobile-img-bg-pc bg-color"></div>
          <v-img
            class="in-mobile-img"
            max-height="331"
            :src="getImage(isAppleDevice ? 'pwa/pwa_chrome.gif' : 'pwa/pwa_chrome.webp')"
            contain
          />
        </div>
        <!-- EDGE教學 -->
        <v-row justify="center" no-gutters>
          <v-col
            id="edge"
            xl="8"
            lg="10"
            cols="12"
            class="item-content-edge"
            :class="$vuetify.breakpoint.lgAndUp ? 'bg-color' : ''"
          >
            <v-row
              class="pa-8 flex-row-reverse"
              :class="[
                $vuetify.breakpoint.lgAndUp || $vuetify.breakpoint.xsOnly
                  ? 'flex-row'
                  : 'flex-column'
              ]"
              no-gutters
              align="center"
            >
              <v-col
                class="d-flex"
                :class="[
                  $vuetify.breakpoint.lgAndUp || $vuetify.breakpoint.xsOnly
                    ? 'flex-column'
                    : 'flex-row'
                ]"
                :style="$vuetify.breakpoint.lgAndUp ? 'max-width:560px' : 'max-width:780px'"
              >
                <!-- text -->
                <v-row no-gutters class="flex-column">
                  <v-col
                    no-gutters
                    class="custom-text-noto text-h6 primary--text font-weight-medium"
                    >EDGE {{ $t('teach') }}</v-col
                  >
                  <v-col>
                    <div class="li-text custom-text-noto text-body-2">
                      {{ $t('pwa_pc_edge_text_1') }}
                    </div>
                  </v-col>
                  <v-col no-gutters class="mt-4 d-flex flex-nowrap align-center flex-row">
                    <div
                      class="li-title custom-text-noto secondary-variant-1--text text-body-1 mr-2"
                    >
                      1
                    </div>
                    <div class="li-text custom-text-noto text-body-1">
                      {{ $t('pwa_pc_edge_text_2') }}
                    </div>
                  </v-col>
                  <v-col no-gutters class="mt-4 d-flex flex-nowrap align-center flex-row">
                    <div
                      class="li-title custom-text-noto secondary-variant-1--text text-body-1 mr-2"
                    >
                      2
                    </div>
                    <div class="li-text custom-text-noto text-body-1">
                      {{ $t('pwa_pc_edge_text_3') }}
                    </div>
                  </v-col>
                  <v-col no-gutters class="mt-4 d-flex flex-nowrap align-center flex-row">
                    <div
                      class="li-title custom-text-noto secondary-variant-1--text text-body-1 mr-2"
                    >
                      3
                    </div>
                    <div class="li-text custom-text-noto text-body-1">
                      {{ $t('pwa_pc_edge_text_4') }}
                    </div>
                  </v-col>
                  <v-col no-gutters class="mt-4 d-flex flex-nowrap align-center flex-row">
                    <div
                      class="li-title custom-text-noto secondary-variant-1--text text-body-1 mr-2"
                    >
                      4
                    </div>
                    <div class="li-text custom-text-noto text-body-1">
                      {{ $t('pwa_pc_edge_text_5') }}
                    </div>
                  </v-col>
                </v-row>
              </v-col>
              <v-spacer v-if="$vuetify.breakpoint.lgAndUp"></v-spacer>
            </v-row>
            <!-- img -->
            <div class="right-img-content-edge" v-if="$vuetify.breakpoint.lgAndUp">
              <v-img
                width="470px"
                :src="getImage(isAppleDevice ? 'pwa/pwa_edge.gif' : 'pwa/pwa_edge.webp')"
              />
            </div>
          </v-col>
        </v-row>
        <div class="in-mobile-img-content my-12" v-if="$vuetify.breakpoint.mdAndDown">
          <div class="in-mobile-img-bg-pc bg-color"></div>
          <v-img
            class="in-mobile-img"
            max-height="331"
            :src="getImage(isAppleDevice ? 'pwa/pwa_edge.gif' : 'pwa/pwa_edge.webp')"
            contain
          />
        </div>
      </v-tab-item>
    </v-tabs-items>
  </v-container>
</template>

<script>
  import images from '~/mixins/images'
  import scssLoader from '~/mixins/scssLoader'
  import orientation from '@/mixins/orientation.js'
  const STATION = process.env.STATION
  export default {
    name: 'PwaTeaching',
    mixins: [images, scssLoader, orientation],
    components: {
      linearGradientTitle: () => import(`~/components_station/${STATION}/linearGradientTitle`)
    },
    data() {
      return {
        selectType: '',
        QRCodeUrl: process.env.PLATFORM_URL + 'teach/pwa'
      }
    },
    async created() {
      this.checkMaintain()
    },
    computed: {
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      },
      isAppleDevice() {
        return this.$device.isIos || this.$device.isMacOS
      }
    },
    watch: {},
    mounted() {
      this.setDevice()
    },
    methods: {
      checkMaintain() {
        // 平台維護檢查
        // 平台維護檢查已由 layout 處理
          this.$nuxt.error({
            statusCode: 503,
            params: {
              maintainBeginAt: this.maintainSystem[0].maintainBeginAt,
              maintainEndAt: this.maintainSystem[0].maintainEndAt
            }
          })
        }
      },
      goto(id) {
        const targetElement = document.getElementById(id) // 替換為你的目標元素的 ID
        targetElement.scrollIntoView({
          block: 'start', // 滾動到目標元素的位置，可以是 'start'、'center' 或 'end'
          inline: 'nearest' // 滾動到目標元素的內聯軸位置
        })
        window.scrollBy(0, -60)
      },
      //依照裝置不同，而跳至不同的tab
      setDevice() {
        let device = this.$device
        let userAgent = this.$device.userAgent
        let isSafari = userAgent.indexOf('Safari') !== -1 && userAgent.indexOf('Version') !== -1
        let isIphone = userAgent.indexOf('iPhone') !== -1 && userAgent.indexOf('Version') !== -1
        let isIpadPro = isSafari && !isIphone && 'ontouchend' in document // 判斷是否為ipadPro
        //ipad pro 特立獨行
        if (isIpadPro) {
          this.selectType = 'ios'
        } else if (device.isDesktop) {
          this.selectType = 'pc'
        } else {
          if (device.isIos) {
            this.selectType = 'ios'
          } else {
            this.selectType = 'android'
          }
        }
      }
    },
    beforeDestroy() {}
  }
</script>

<style lang="scss" scoped>
  $primary-variant-3: map-get($colors, 'primary-variant-3');
  $primary: map-get($colors, 'primary');
  $card-fill: map-get($colors, 'card-fill');
  .pwaContainer {
    padding-bottom: 72px;
  }
  .border-style-title {
    border-left: solid 1px $primary-variant-3 !important;
    border-right: solid 1px $primary-variant-3 !important;
    border-top: solid 1px $primary-variant-3 !important;
  }
  .border-style-bottom {
    border-left: solid 1px $primary-variant-3 !important;
    border-right: solid 1px $primary-variant-3 !important;
    border-bottom: solid 1px $primary-variant-3 !important;
  }

  .v-tabs-items {
    background-color: transparent !important;
  }
  .a-link {
    color: rgba(255, 255, 255, 0.6) !important;
  }
  .v-tab--active {
    color: $primary !important;
  }
  .bg-color {
    background-color: $card-fill !important;
  }
  .item-content {
    position: relative;
    margin: auto;
    border-radius: 8px;
  }
  .item-content-edge {
    position: relative;
    margin: auto;
    margin-top: 100px;
    border-radius: 8px;
    @media (max-width: 768px) {
      margin-top: 60px;
    }
  }
  .li-title {
    min-width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    background-color: $primary-variant-3 !important;
  }
  .li-text {
    display: flex;
    align-items: center;
  }
  .right-img-content {
    position: absolute;
    top: -20px;
    right: -40px;
    width: 510px;
    height: 535px;
  }
  .right-img-content-edge {
    position: absolute;
    top: 50px;
    left: -16px;
    width: 510px;
    height: 500px;
  }
  .v-item-group {
    overflow: unset !important;
  }
  .in-mobile-img-content {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding-top: 40%;
    position: relative;
    margin: 160px 0;
    .in-mobile-img-bg {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 100%;
      padding-top: 42.66%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .in-mobile-img-bg-pc {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 100%;
      padding-top: 21%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .in-mobile-img {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
  @media (orientation: landscape) {
    .notch-left {
      padding-left: env(safe-area-inset-left) !important;
    }
    .notch-right {
      padding-right: env(safe-area-inset-right) !important;
    }
  }
</style>
