<template>
  <v-container class="no-service-container pa-0" fluid>
    <div class="safe-area-wrapper">
      <v-row no-gutters justify="center"
        ><v-col
          ><v-card class="text-center" color="transparent" elevation="0">
            <v-img
              :src="noServicePageImage"
              class="no-service-image"
              width="100%"
              :max-width="$vuetify.breakpoint.mdAndUp ? '570' : '343'"
              alt="noServiceImage"
            ></v-img>
            <v-card-title
              :class="[
                $vuetify.breakpoint.mdAndUp ? 'pt-6 pb-4' : 'pt-4 pb-2',
                'text-md-h5 text-sm-h6 justify-center font-weight-bold custom-text-noto gradient-primary--text'
              ]"
              >{{ $t('region_not_supported') }}</v-card-title
            >
            <v-card-text
              :class="[
                $vuetify.breakpoint.mdAndUp ? 'pb-6' : 'pb-4',
                'default-content--text custom-text-noto text-body-1 font-weight-regular '
              ]"
            >
              <span v-for="text in contactTexts" :key="text"> {{ text }}<br /></span>
            </v-card-text>
            <v-img
              class="no-service-image"
              max-width="130"
              :src="`/${STATION}/logo.webp`"
              alt="logo"
            /> </v-card></v-col
      ></v-row>
    </div>
  </v-container>
</template>

<script>
  const STATION = process.env.STATION

  export default {
    name: 'NoService',
    layout: 'empty',
    data() {
      return {
        STATION,
        noServicePageImage: require(`~/assets/image/${STATION}/error/error_not_available.png`)
      }
    },
    computed: {
      contactTexts() {
        const email = '<EMAIL>'
        const translationKey = this.$vuetify.breakpoint.xs
          ? 'customer_service_contact_xs'
          : 'customer_service_contact'
        return this.$t(translationKey, { email }).split('\r\n')
      }
    }
  }
</script>

<style lang="scss" scoped>
  .v-card__subtitle,
  .v-card__text,
  .v-card__title {
    padding: 0;
  }
  .no-service {
    &-container {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100vh;

      @supports (height: 100svh) {
        height: 100svh;
      }

      @media (orientation: landscape) {
        height: auto;
        min-height: 100vh;
        padding: 16px 0;
        overflow-y: auto;

        @supports (min-height: 100svh) {
          min-height: 100svh;
        }
      }
    }

    &-image {
      margin: 0 auto;
    }
  }

  .safe-area-wrapper {
    $safe-areas: (
      'left': safe-area-inset-left,
      'right': safe-area-inset-right,
      'top': safe-area-inset-top,
      'bottom': safe-area-inset-bottom
    );

    @each $direction, $value in $safe-areas {
      padding-#{$direction}: env(#{$value});
      padding-#{$direction}: constant(#{$value});
    }
  }
</style>
