function md5(d) {
  var r = M(V(Y(X(d), 8 * d.length)))
  return r.toLowerCase()
}
function M(d) {
  for (var _, m = '0123456789ABCDEF', f = '', r = 0; r < d.length; r++)
    (_ = d.charCodeAt(r)), (f += m.charAt((_ >>> 4) & 15) + m.charAt(15 & _))
  return f
}
function X(d) {
  for (var _ = Array(d.length >> 2), m = 0; m < _.length; m++) _[m] = 0
  for (m = 0; m < 8 * d.length; m += 8) _[m >> 5] |= (255 & d.charCodeAt(m / 8)) << m % 32
  return _
}
function V(d) {
  for (var _ = '', m = 0; m < 32 * d.length; m += 8)
    _ += String.fromCharCode((d[m >> 5] >>> m % 32) & 255)
  return _
}
function Y(d, _) {
  ;(d[_ >> 5] |= 128 << _ % 32), (d[14 + (((_ + 64) >>> 9) << 4)] = _)
  for (
    var m = 1732584193, f = -271733879, r = -1732584194, i = 271733878, n = 0;
    n < d.length;
    n += 16
  ) {
    var h = m,
      t = f,
      g = r,
      e = i
    ;(f = md5_ii(
      (f = md5_ii(
        (f = md5_ii(
          (f = md5_ii(
            (f = md5_hh(
              (f = md5_hh(
                (f = md5_hh(
                  (f = md5_hh(
                    (f = md5_gg(
                      (f = md5_gg(
                        (f = md5_gg(
                          (f = md5_gg(
                            (f = md5_ff(
                              (f = md5_ff(
                                (f = md5_ff(
                                  (f = md5_ff(
                                    f,
                                    (r = md5_ff(
                                      r,
                                      (i = md5_ff(
                                        i,
                                        (m = md5_ff(m, f, r, i, d[n + 0], 7, -680876936)),
                                        f,
                                        r,
                                        d[n + 1],
                                        12,
                                        -389564586
                                      )),
                                      m,
                                      f,
                                      d[n + 2],
                                      17,
                                      606105819
                                    )),
                                    i,
                                    m,
                                    d[n + 3],
                                    22,
                                    -1044525330
                                  )),
                                  (r = md5_ff(
                                    r,
                                    (i = md5_ff(
                                      i,
                                      (m = md5_ff(m, f, r, i, d[n + 4], 7, -176418897)),
                                      f,
                                      r,
                                      d[n + 5],
                                      12,
                                      1200080426
                                    )),
                                    m,
                                    f,
                                    d[n + 6],
                                    17,
                                    -1473231341
                                  )),
                                  i,
                                  m,
                                  d[n + 7],
                                  22,
                                  -45705983
                                )),
                                (r = md5_ff(
                                  r,
                                  (i = md5_ff(
                                    i,
                                    (m = md5_ff(m, f, r, i, d[n + 8], 7, 1770035416)),
                                    f,
                                    r,
                                    d[n + 9],
                                    12,
                                    -1958414417
                                  )),
                                  m,
                                  f,
                                  d[n + 10],
                                  17,
                                  -42063
                                )),
                                i,
                                m,
                                d[n + 11],
                                22,
                                -1990404162
                              )),
                              (r = md5_ff(
                                r,
                                (i = md5_ff(
                                  i,
                                  (m = md5_ff(m, f, r, i, d[n + 12], 7, 1804603682)),
                                  f,
                                  r,
                                  d[n + 13],
                                  12,
                                  -40341101
                                )),
                                m,
                                f,
                                d[n + 14],
                                17,
                                -1502002290
                              )),
                              i,
                              m,
                              d[n + 15],
                              22,
                              1236535329
                            )),
                            (r = md5_gg(
                              r,
                              (i = md5_gg(
                                i,
                                (m = md5_gg(m, f, r, i, d[n + 1], 5, -165796510)),
                                f,
                                r,
                                d[n + 6],
                                9,
                                -1069501632
                              )),
                              m,
                              f,
                              d[n + 11],
                              14,
                              643717713
                            )),
                            i,
                            m,
                            d[n + 0],
                            20,
                            -373897302
                          )),
                          (r = md5_gg(
                            r,
                            (i = md5_gg(
                              i,
                              (m = md5_gg(m, f, r, i, d[n + 5], 5, -701558691)),
                              f,
                              r,
                              d[n + 10],
                              9,
                              38016083
                            )),
                            m,
                            f,
                            d[n + 15],
                            14,
                            -660478335
                          )),
                          i,
                          m,
                          d[n + 4],
                          20,
                          -405537848
                        )),
                        (r = md5_gg(
                          r,
                          (i = md5_gg(
                            i,
                            (m = md5_gg(m, f, r, i, d[n + 9], 5, 568446438)),
                            f,
                            r,
                            d[n + 14],
                            9,
                            -1019803690
                          )),
                          m,
                          f,
                          d[n + 3],
                          14,
                          -187363961
                        )),
                        i,
                        m,
                        d[n + 8],
                        20,
                        1163531501
                      )),
                      (r = md5_gg(
                        r,
                        (i = md5_gg(
                          i,
                          (m = md5_gg(m, f, r, i, d[n + 13], 5, -1444681467)),
                          f,
                          r,
                          d[n + 2],
                          9,
                          -51403784
                        )),
                        m,
                        f,
                        d[n + 7],
                        14,
                        1735328473
                      )),
                      i,
                      m,
                      d[n + 12],
                      20,
                      -1926607734
                    )),
                    (r = md5_hh(
                      r,
                      (i = md5_hh(
                        i,
                        (m = md5_hh(m, f, r, i, d[n + 5], 4, -378558)),
                        f,
                        r,
                        d[n + 8],
                        11,
                        -2022574463
                      )),
                      m,
                      f,
                      d[n + 11],
                      16,
                      1839030562
                    )),
                    i,
                    m,
                    d[n + 14],
                    23,
                    -35309556
                  )),
                  (r = md5_hh(
                    r,
                    (i = md5_hh(
                      i,
                      (m = md5_hh(m, f, r, i, d[n + 1], 4, -1530992060)),
                      f,
                      r,
                      d[n + 4],
                      11,
                      1272893353
                    )),
                    m,
                    f,
                    d[n + 7],
                    16,
                    -155497632
                  )),
                  i,
                  m,
                  d[n + 10],
                  23,
                  -1094730640
                )),
                (r = md5_hh(
                  r,
                  (i = md5_hh(
                    i,
                    (m = md5_hh(m, f, r, i, d[n + 13], 4, 681279174)),
                    f,
                    r,
                    d[n + 0],
                    11,
                    -358537222
                  )),
                  m,
                  f,
                  d[n + 3],
                  16,
                  -722521979
                )),
                i,
                m,
                d[n + 6],
                23,
                76029189
              )),
              (r = md5_hh(
                r,
                (i = md5_hh(
                  i,
                  (m = md5_hh(m, f, r, i, d[n + 9], 4, -640364487)),
                  f,
                  r,
                  d[n + 12],
                  11,
                  -421815835
                )),
                m,
                f,
                d[n + 15],
                16,
                530742520
              )),
              i,
              m,
              d[n + 2],
              23,
              -995338651
            )),
            (r = md5_ii(
              r,
              (i = md5_ii(
                i,
                (m = md5_ii(m, f, r, i, d[n + 0], 6, -198630844)),
                f,
                r,
                d[n + 7],
                10,
                1126891415
              )),
              m,
              f,
              d[n + 14],
              15,
              -1416354905
            )),
            i,
            m,
            d[n + 5],
            21,
            -57434055
          )),
          (r = md5_ii(
            r,
            (i = md5_ii(
              i,
              (m = md5_ii(m, f, r, i, d[n + 12], 6, 1700485571)),
              f,
              r,
              d[n + 3],
              10,
              -1894986606
            )),
            m,
            f,
            d[n + 10],
            15,
            -1051523
          )),
          i,
          m,
          d[n + 1],
          21,
          -2054922799
        )),
        (r = md5_ii(
          r,
          (i = md5_ii(
            i,
            (m = md5_ii(m, f, r, i, d[n + 8], 6, 1873313359)),
            f,
            r,
            d[n + 15],
            10,
            -30611744
          )),
          m,
          f,
          d[n + 6],
          15,
          -1560198380
        )),
        i,
        m,
        d[n + 13],
        21,
        1309151649
      )),
      (r = md5_ii(
        r,
        (i = md5_ii(
          i,
          (m = md5_ii(m, f, r, i, d[n + 4], 6, -145523070)),
          f,
          r,
          d[n + 11],
          10,
          -1120210379
        )),
        m,
        f,
        d[n + 2],
        15,
        718787259
      )),
      i,
      m,
      d[n + 9],
      21,
      -343485551
    )),
      (m = safe_add(m, h)),
      (f = safe_add(f, t)),
      (r = safe_add(r, g)),
      (i = safe_add(i, e))
  }
  return Array(m, f, r, i)
}
function md5_cmn(d, _, m, f, r, i) {
  return safe_add(bit_rol(safe_add(safe_add(_, d), safe_add(f, i)), r), m)
}
function md5_ff(d, _, m, f, r, i, n) {
  return md5_cmn((_ & m) | (~_ & f), d, _, r, i, n)
}
function md5_gg(d, _, m, f, r, i, n) {
  return md5_cmn((_ & f) | (m & ~f), d, _, r, i, n)
}
function md5_hh(d, _, m, f, r, i, n) {
  return md5_cmn(_ ^ m ^ f, d, _, r, i, n)
}
function md5_ii(d, _, m, f, r, i, n) {
  return md5_cmn(m ^ (_ | ~f), d, _, r, i, n)
}
function safe_add(d, _) {
  var m = (65535 & d) + (65535 & _)
  return (((d >> 16) + (_ >> 16) + (m >> 16)) << 16) | (65535 & m)
}
function bit_rol(d, _) {
  return (d << _) | (d >>> (32 - _))
}
const STATION = 'vietnam_01'
const outpostList = require(`~/station/outpostList.js`).default
export default {
  data() {
    return {
      secretKey: 'd6e586674ee386288d2f5d1ca3db07b8', // secretKey login
      alertMessage: '',
      paymentUrl: ''
    }
  },
  computed: {
    facebookLoginUrl() {
      const browserDomainName = window.location.hostname
      const outpost = outpostList().includes(browserDomainName)
      const production = process.env.NUXT_ENV === 'production'
      const stationType = production ? (outpost ? 'h5-vtco-outpost' : 'h5-vtco') : 'h5-vtco-test'
      return `https://id.esgame.vn/auth/facebook?redirect_url=https://games.esgame.vn/play/${stationType}`
    },
    googleLoginUrl() {
      const browserDomainName = window.location.hostname
      const outpost = outpostList().includes(browserDomainName)
      const production = process.env.NUXT_ENV === 'production'
      const stationType = production ? (outpost ? 'h5-vtco-outpost' : 'h5-vtco') : 'h5-vtco-test'
      return `https://id.esgame.vn/auth/google?redirect_url=https://games.esgame.vn/play/${stationType}`
    },
    esgLoginUrl() {
      const browserDomainName = window.location.hostname
      const outpost = outpostList().includes(browserDomainName)
      const production = process.env.NUXT_ENV === 'production'
      const stationType = production ? (outpost ? 'h5-vtco-outpost' : 'h5-vtco') : 'h5-vtco-test'
      return `https://id.esgame.vn/login?redirect_url=https://games.esgame.vn/play/${stationType}`
    },
    paymentImageUrl() {
      return 'https://id.esgame.vn/frontend/images/payment.png'
    },
    facebookImageUrl() {
      return 'https://id.esgame.vn/frontend/images/facebook.png'
    },
    googleImageUrl() {
      return 'https://id.esgame.vn/frontend/images/google.png'
    },
    googleLoginQCUrl() {
      return 'https://id.esgame.vn/auth/google?redirect_url=https://games.esgame.vn/play/h5-vtco-qc'
    },
    stationName() {
      return this.$store.getters[`${process.env.STATION}/station/stationName`]
    },
    loginRecord({ $store }) {
      return $store.getters['role/loginRecord']
    },
    isESGLogin() {
      const openId = this.$route.query.open_id !== undefined
      const timestamp = this.$route.query.timestamp !== undefined
      const ip = this.$route.query.ip !== undefined
      const sign = this.$route.query.sign !== undefined
      return openId && timestamp && ip && sign
    }
  },
  methods: {
    async checkLogin(openId, timestamp, ip, sign) {
      if (openId && timestamp && ip && sign) {
        const partnerSign = md5(openId + timestamp + ip + this.secretKey)
        if (sign === partnerSign) {
          this.alertMessage = 'Login Success then do something, ESGID = ' + openId
          await this.wsInit()
          const esgSetting = JSON.parse(this.$localStorage.get('esgSetting').setting)
          const loginTypeMapping = {
            3: 4,
            4: 5,
            6: 16
          }
          const loginType =
            esgSetting.loginType === 4
              ? this.$xinConfig.ACCOUNT_TYPE.GAMECENTER
              : this.$xinConfig.ACCOUNT_TYPE.GAMECENTER
          const xAgent = this.$cookies.get('xAgent')
          const packet = this.$wsPacketFactory.loginWithVietnam({
            idToken: openId,
            accountType: loginType,
            device: xAgent,
            clientVersion: this.$xinServerConfig.clientVersion,
            serverVersion: this.$xinServerConfig.serverVersion,
            esgJson: '',
            tiemStamp: timestamp,
            ipAddr: ip,
            sign: sign
            // promote: this.$store.getters['social/promote']
          })
          this.$store.commit('role/SET_LOGIN_TYPE', loginTypeMapping[esgSetting.loginType])
          this.$wsClient.send(packet)
          await this.esgLoginHandler()
          this.handLoginEnd()
        } else {
          this.alertMessage = 'Login Fail ! Please check sign!'
        }
      }
    },

    async wsInit() {
      if (!this.$wsClient.isConnected) {
        do {
          let endPoint = this.$wsClient.endPoint.current
          try {
            // 1000 正常關閉
            await this.$wsClient.disconnect(1000)
            if (process.env.STATION === 'qc_overseas') {
              this.$store.dispatch('xinProtocol/resetConfig', STATION)
              this.$store.dispatch(`${process.env.STATION}/station/setStation`, STATION)
              this.$wsClient.updateStationSetting(STATION, true)
              this.$wsPacketFactory.updateStationSetting(STATION, false)
              await this.$wsClient.connect(endPoint, STATION)
            } else {
              await this.$wsClient.connect(endPoint)
            }
          } catch (err) {
            console.log('CATCH ERR', err)
            this.$wsClient.endPoint.next()
          }
          await this.$xinUtility.delay(1000)
        } while (!this.$wsClient.isConnected)

        this.$wsClient.send(this.$wsPacketFactory.connectionPacket())
        await this.$xinUtility
          .waitEvent(
            this.$wsClient.receivedListeners,
            (data) => data.protocolId === this.$xinConfig.PROTOCOL_ID.CONNECT
          )
          .then((data) => {
            this.$store.dispatch('role/saveToken', data)
            // 初始化動作-進入登入服務器
            this.$wsClient.send(
              this.$wsPacketFactory.getServiceJoin(this.$xinConfig.LOGIN_SERVICE.ID)
            )
            this.$store.commit('xinProtocol/SET_SERVICE_IDS', {
              serviceId: this.$xinConfig.LOGIN_SERVICE.ID,
              enable: true,
              connected: true
            })
          })
      }
    },
    async esgLoginHandler() {
      try {
        const title = this.$t('reminder')
        let message = ''
        const label = 'WebESG'
        this.$store.commit('xinProtocol/SET_SERVICE_IDS', {
          serviceId: this.$xinConfig.LOGIN_SERVICE.ID,
          enable: false,
          connected: false
        })

        // 避免剛登入時連續跳出 noty info 的設計，先透過該值關閉通知
        this.$store.commit('mail/SET_FIRST_RECIVE', true)
        const res = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
          return data.isFeature(this.$xinConfig.FEATURE.LOGIN.ID)
        })
        if (
          res.commandId === this.$xinConfig.LOGIN_SERVICE.TYPE.NEW_LOGIN.COMMAND.SHOW_CREATE_CHAR
        ) {
          this.$nuxt.$emit('root:showCreateRoleDialogStatus', true)
        }
        if (res.type === 0 && res.commandId === this.$xinConfig.LOGIN_SERVICE.TYPE.PLAYER_INFO.ID) {
          // 先發送前三個服務的加入請求
          const initialServices = [
            this.$xinConfig.LOTTERY_SERVICE.ID, // 進入摸彩服務
            this.$xinConfig.GAME_SERVICE.ID, // 進入遊戲服務
            this.$xinConfig.SOCIAL_SERVICE.ID // 進入社群服務
          ]

          initialServices.forEach((serviceId) => {
            this.$wsClient.send(this.$wsPacketFactory.getServiceJoin(serviceId))
            this.$store.commit('xinProtocol/SET_SERVICE_IDS', {
              serviceId,
              enable: true,
              connected: true
            })
          })

          // 等待 SOCIAL_SERVICE 確認
          await this.$xinUtility
            .waitEvent(
              this.$wsClient.receivedListeners,
              (data) =>
                data.protocolId === this.$xinConfig.PROTOCOL_ID.SERVICE &&
                data.serviceId === this.$xinConfig.SOCIAL_SERVICE.ID
            )
            .then(() => {
              this.$wsClient.send(this.$wsPacketFactory.initMail())
              // 延遲五秒後開啟新信件通知
              setTimeout(() => {
                this.$store.commit('mail/SET_FIRST_RECIVE', false)
              }, 5000)
            })
            .catch((err) => {
              console.log('SOCIAL_SERVICE ERROR:', err)
            })
          // 初始化角色資料
          await this.$store.dispatch('role/profileInit', res)
          // 再加入公會服務
          // this.$wsClient.send(
          //   this.$wsPacketFactory.getServiceJoin(this.$xinConfig.GUILD_SERVICE.ID)
          // )
          // this.$store.commit('xinProtocol/SET_SERVICE_IDS', {
          //   serviceId: this.$xinConfig.GUILD_SERVICE.ID,
          //   enable: true,
          //   connected: true
          // })

          //取得與伺服器時間差
          await this.$store.dispatch('xinProtocol/getServerLocalTimeDiff')
          if (this.isBindPhone) {
            setTimeout(() => {
              // 成功登入，執行使用者登入前動作
              this.callPreLoginAction()
            }, 500)
          } else {
            //未綁定手機號碼 第一次登入 或 階級>=黃金,顯示綁定手機號碼提示框
            if (
              (this.loginRecord.length <= 1 || this.vipLevel >= 3) &&
              process.env.STATION !== 'qc_overseas'
            ) {
              // 用連結直接開啟遊戲時，若為正式遊玩，登入後暫不顯示綁定手機號碼提示框
              this.$nuxt.$emit(
                'root:showNotyNotBindPhoneDialogStatus',
                !(this.showGameModeStatus && this.$route.params.mode === 'play')
              )
              this.$nuxt.$emit('root:showLoginDialogStatus', {
                show: false,
                onCancelNotify: () => {}
              })
            } else {
              setTimeout(() => {
                // 成功登入，執行使用者登入前動作
                this.callPreLoginAction()
              }, 500)
            }
          }
          this.$nuxt.$emit('root:showWelcomeStatus', true)

          //第三方登入 登入成功後 送出登入事件至google analytics 與 facebook analytics
          if (process.env.STATION !== 'qc_overseas') {
            this.loginAnalytics(label)
          }
          if (
            Object.prototype.hasOwnProperty.call(res, 'lastLogoutTime') &&
            res.lastLogoutTime === '1900/01/01 00:00:00'
          ) {
            this.createRoleAnalytics()
          }
        } else if (res.commandId === 136) {
          this.$notify.info(res.message)
          this.$nuxt.$emit('root:showDeviceWhiteDialogStatus', true)
        } else if (res.commandId === 135) {
          message = res.message
          this.showNotyDialog(title, message)
        }
      } catch (error) {
        console.log('esg login', error)
      }
    },
    showNotyDialog(title, message) {
      this.$store.dispatch('easyDialog/setDialog', {
        title: title,
        message: message
      })
      this.$nuxt.$emit('root:showNotyDialogStatus', true)
    },
    handLoginEnd() {
      const esgSetting = JSON.parse(this.$localStorage.get('esgSetting').setting)
      if (esgSetting.gamePage) {
        const page = esgSetting.page
        const gameCategory = esgSetting.gameCategory
        const gameSortType = esgSetting.gameSortType
        const searchWord = esgSetting.searchWord
        this.$router.push({
          path: this.localePath(
            process.env.STATION === 'qc_overseas' ? '/vietnam_01/game' : '/game'
          ),
          query: { page, gameCategory, gameSortType, searchWord }
        })
      } else {
        if (process.env.STATION === 'qc_overseas') {
          this.setStation()
        } else {
          this.$router.push({
            path: this.localePath('/')
          })
        }
      }
      this.$wsClient.send(this.$wsPacketFactory.getServiceJoin(this.$xinConfig.REDEEM_SERVICE.ID))
    },
    setStation() {
      this.$store.commit(`qc_overseas/station/SET_LOADING`, true)
      this.$router.push({
        path: `/vietnam_01/game`,
        query: { ...this.$route.query }
      })
      setTimeout(() => {
        this.$store.commit(`qc_overseas/station/SET_LOADING`, false)
      }, 5000)
    },
    async getUrlPayment(itemID) {
      // itemID of payment item name
      const urlPayment = 'https://pay.esgame.vn/purchase/in-game/vua-tro-choi-online' // payment url
      const playerID = this.$store.getters['role/userName'] // ID Player in game
      const serverID = process.env.NUXT_ENV === 'production' ? 'formal' : 'test' // formal or test
      const timestamp = this.timeStamp() // timestamp
      const ip = await this.getPublicIP() // current ip of user
      const payment_key = 'e706b12d-b111-4a7c-8c1c-bcf3f3ef7972' // secretKey payment
      const sign = md5(playerID + serverID + timestamp + ip + itemID + payment_key)

      return `${urlPayment}?player_id=${playerID}&server_id=${serverID}&timestamp=${timestamp}&ip=${ip}&item_id=${itemID}&sign=${sign}`
    },
    getQueryParam(name) {
      const match = new RegExp('[?&]' + encodeURIComponent(name) + '=([^&]*)').exec(location.search)
      return match ? decodeURIComponent(match[1]) : undefined
    },
    async getPublicIP() {
      try {
        const response = await this.$axios.get('https://api.ipify.org?format=json')
        return response.data.ip
      } catch (error) {
        console.error("can't get public IP:", error)
        return null
      }
    },
    timeStamp() {
      var timeStampInMs =
        window.performance &&
        window.performance.now &&
        window.performance.timing &&
        window.performance.timing.navigationStart
          ? window.performance.now() + window.performance.timing.navigationStart
          : Date.now()
      return timeStampInMs
    }
  }
}
