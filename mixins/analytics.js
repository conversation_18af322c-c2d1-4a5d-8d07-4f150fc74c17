let thirdParty = null
if (process.client) {
  thirdParty = require(`~/station/${process.env.STATION}/sdk.js`).default
}

export default {
  methods: {
    loginAnalytics(loginType) {
      this.basicAnalytics({
        trackType: 'trackCustom',
        category: 'Weblogin',
        label: loginType,
        value: 1
      })
    },
    createRoleAnalytics() {
      this.basicAnalytics({
        trackType: 'trackCustom',
        category: 'Webcreate',
        label: 'Webcreate',
        value: 1
      })
    },
    playAnalytics() {
      this.basicAnalytics({
        trackType: 'trackCustom',
        category: 'Webplay',
        label: 'Webplay',
        value: 1
      })
    },
    gameClickAnalytics({ headerData, gameId, isDemo, body, xinkey, stationName = null }) {
      if (process.server) {
        return
      }

      try {
        this.$clientApi.game.gameClick({
          headerData,
          gameId,
          isDemo,
          lang: body.lang,
          username: body.username,
          xinkey,
          ...(stationName && { stationName })
        })
      } catch (error) {
        console.log('gameClickAnalytics', error)
      }
    },
    gameSearchAnalytics({ content, username, gameIds, at }) {
      if (process.server) {
        return
      }
      // GDPR 歐盟法規
      if (
        !this.$localStorage.get('accept_cookie_policy').expired &&
        !this.$moment(this.$localStorage.get('accept_cookie_policy').expired).isAfter(
          this.$moment()
        )
      ) {
        return
      }

      try {
        this.$clientApi.game.gameSearchAnalytics({
          content,
          username,
          gameIds,
          at
        })
      } catch (error) {
        console.log('gameClickAnalytics', error)
      }
    },
    freePlayAnalytics() {
      this.basicAnalytics({
        trackType: 'trackCustom',
        category: 'Webfreeplay',
        label: 'webfreeplay',
        value: 1
      })
    },
    paymentClickAnalytics(paymentType) {
      this.basicAnalytics({
        trackType: 'trackCustom',
        category: 'Webvalue',
        label: paymentType,
        value: 1
      })
    },
    downloadClickAnalytics(downloadType) {
      this.basicAnalytics({
        trackType: 'trackCustom',
        category: 'Webdownload',
        label: downloadType,
        value: 1
      })
    },
    basicAnalytics({ trackType, category, label, value }) {
      if (process.server) {
        return
      }
      // GDPR 歐盟法規
      if (
        !this.$localStorage.get('accept_cookie_policy').expired &&
        !this.$moment(this.$localStorage.get('accept_cookie_policy').expired).isAfter(
          this.$moment()
        )
      ) {
        return
      }
      if (thirdParty.analysis.google.analyticsPluginName === 'ga') {
        this.$gtag.event(category, {
          event: category,
          event_category: category,
          event_label: label,
          value: value
        })
      } else {
        this.$gtm.push({
          event: category,
          event_category: category,
          event_label: label,
          value: value
        })
      }

      if (this.$fb.fbq !== null) {
        this.$fb.fbq(trackType, category, {
          eventAction: category,
          eventLabel: label,
          eventValue: value
        })
      }
    },
    loginAnalyticsToPlatform({
      username,
      is_new_member,
      level,
      vip_level,
      phone_number,
      type,
      use_pwa
    }) {
      if (process.server) {
        return
      }
      // 1 => 帳號密碼登入
      // 2 => 簡訊驗證碼登入
      // 3 => 寶物密碼登入
      // 4 => Facebook 登入
      // 5 => GOOGLE 登入
      // 6 => APPLE ID 登入
      // 7 => QPP 掃碼
      // 8 => 內接館登入
      // 9 => LINE 登入
      // 10 twitter 登入
      // 11 whatsapp 登入
      // 12 TikTok 登入
      // 13 WeChat 登入
      // 14 Telegram 登入
      // 15 FinShell Pay 登入
      // 16 ESG 登入
      const expiredDate = this.$localStorage.get('accept_cookie_policy').expired
      if (!expiredDate && !this.$moment(expiredDate).isAfter(this.$moment())) {
        return
      }
      this.$clientApi.login.sendAnalytics({
        username,
        is_new_member,
        level,
        vip_level,
        phone_number,
        type,
        use_pwa
      })
    },
    paymentAnalyticsToPlatform({ username, type, point }) {
      if (process.server) {
        return
      }
      // 1 => 遊E卡
      // 2 => 遊E數位卡
      // 3 => GASH儲值
      // 4 => 線上支付(台灣)
      // 5 => 線上支付(海外)
      // 6 => VIP 儲值
      // 7 => 商城消費
      // 8 => 商城VIP消費
      const expiredDate = this.$localStorage.get('accept_cookie_policy').expired
      if (!expiredDate && !this.$moment(expiredDate).isAfter(this.$moment())) {
        return
      }
      this.$clientApi.payment.sendAnalytics({ username, type, point })
    },
    grandPrizeAnalytics(deviceType) {
      this.basicAnalytics({
        trackType: 'trackCustom',
        category: 'Webflow',
        label: deviceType,
        value: 1
      })
    },
    grandPrizeCompletedAnalytics(deviceType) {
      this.basicAnalytics({
        trackType: 'trackCustom',
        category: 'phonexinapp',
        label: deviceType,
        value: 1
      })
    },
    drawAnalytics(deviceType) {
      this.basicAnalytics({
        trackType: 'trackCustom',
        category: 'drawxinapp',
        label: deviceType,
        value: 1
      })
    },
    sendMailAnalytics(sender, receiver, item_count, mail_type, item_id) {
      if (process.server) {
        return
      }
      // GDPR 歐盟法規
      if (
        !this.$localStorage.get('accept_cookie_policy').expired &&
        !this.$moment(this.$localStorage.get('accept_cookie_policy').expired).isAfter(
          this.$moment()
        )
      ) {
        return
      }
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      const hours = String(now.getHours()).padStart(2, '0')
      const minutes = String(now.getMinutes()).padStart(2, '0')
      const seconds = String(now.getSeconds()).padStart(2, '0')
      const tzOffset = now.getTimezoneOffset()
      const tzHours = String(Math.abs(Math.floor(tzOffset / 60))).padStart(2, '0')
      const tzMinutes = String(Math.abs(tzOffset % 60)).padStart(2, '0')
      const tzSign = tzOffset <= 0 ? '+' : '-'
      const at = `${year}-${month}-${day}T${hours}:${minutes}:${seconds}${tzSign}${tzHours}:${tzMinutes}`
      this.$clientApi.guild.sendAnalytics({
        item_id,
        item_count,
        mail_type,
        at,
        sender,
        receiver
      })
    }
  }
}
