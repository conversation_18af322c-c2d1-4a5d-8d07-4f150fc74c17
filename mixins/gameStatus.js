/**
 * 遊戲狀態判斷 Mixin
 * 統一處理遊戲組件的各種狀態判斷邏輯，包括維護狀態、可玩性、Beta狀態、RTP等
 *
 * 使用方式：
 * 1. 組件需要有 game prop (Object)
 * 2. 直接使用 computed: isGameMaintaining, isGamePlayable, isGameBeta, hasRtp, hasDemo, hasRobot, hasExp, defaultRtp
 *
 * 維護優先順序：
 * 1. 動態維護（最高優先級）- 運行時檢測到錯誤時設置
 * 2. 系統維護（第二優先級）- 整個系統停機
 * 3. 平台維護（遊戲廠商維護）- 整個廠商的遊戲都無法使用
 * 4. 個別遊戲維護 - 只有特定遊戲無法使用
 * 5. 遊戲本身維護狀態 - 遊戲資料中的維護標記
 */
export default {
  data() {
    return {
      /**
       * 動態維護狀態
       * 當遊戲啟動失敗或收到特定錯誤碼時設置為 true
       */
      dynamicMaintaining: false
    }
  },

  watch: {
    /**
     * 監聽遊戲資料變化，重置動態維護狀態
     */
    game: {
      handler() {
        this.dynamicMaintaining = false
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    /**
     * 獲取最新的遊戲資料（從 store 中獲取，而非 prop）
     * 確保所有維護狀態判斷都基於最新資料
     */
    latestGameData() {
      if (!this.game || !this.game.id) {
        return this.game
      }

      // 優先從 allGameList 中獲取最新資料
      const allGameList = this.$store.getters['gameHall/allGameList']
      const latestGame = allGameList.find((g) => g.id === this.game.id)

      if (latestGame) {
        return latestGame
      }

      // 如果 allGameList 中沒有，則從 gameList 中獲取
      const gameList = this.$store.getters['gameHall/gameList']
      const gameInList = gameList.find((g) => g.id === this.game.id)

      return gameInList || this.game
    },

    /**
     * 系統維護狀態
     */
    maintainSystem() {
      return this.$store.getters['maintain/system']
    },

    /**
     * 平台維護狀態
     */
    platformMaintain() {
      return this.$store.getters['maintain/platform']
    },

    /**
     * 遊戲維護狀態
     */
    gameMaintenance() {
      return this.$store.getters['maintain/game']
    },

    /**
     * 判斷遊戲是否維護中
     * 使用最新的 store 資料而非 prop
     */
    isGameMaintaining() {
      const gameData = this.latestGameData

      // 1. 動態維護（最高優先級）- 運行時檢測到錯誤時設置
      if (this.dynamicMaintaining) {
        return true
      }

      if (!gameData || typeof gameData !== 'object') {
        return false
      }

      // 2. 系統維護（第二優先級）- 整個系統停機
      if (this.maintainSystem?.[0]?.maintaining) {
        return true
      }

      // 3. 平台維護（遊戲廠商維護）- 整個廠商的遊戲都無法使用
      const platform = this.platformMaintain.find(
        (p) => p.id === gameData.platformId && p.maintaining === true
      )
      if (platform) {
        return true
      }

      // 4. 個別遊戲維護 - 從維護列表中檢查
      const gameMaintenanceItem = this.gameMaintenance.find(
        (game) => game.id === gameData.id && game.maintaining === true
      )
      if (gameMaintenanceItem) {
        return true
      }

      // 5. 遊戲本身維護狀態 - 遊戲資料中的維護標記
      if (gameData.maintaining) {
        return true
      }

      // 所有維護檢查都通過
      return false
    },

    /**
     * 判斷遊戲是否可玩
     * 使用最新的 store 資料而非 prop
     */
    isGamePlayable() {
      const gameData = this.latestGameData

      if (!gameData || typeof gameData !== 'object') {
        return false
      }

      return !this.isGameMaintaining && (gameData.enable === 1 || gameData.enable === 2)
    },

    /**
     * 判斷遊戲是否為 Beta 狀態
     * @returns {boolean} true - Beta, false - 正式版
     */
    isGameBeta() {
      if (!this.game || typeof this.game !== 'object') {
        return false
      }

      return this.game.enable === 2
    },

    /**
     * 判斷遊戲是否有 RTP
     * @returns {boolean} true - 有 RTP, false - 無 RTP
     */
    hasRtp() {
      if (!this.game || typeof this.game !== 'object') {
        return false
      }

      return this.game.rtp !== null
    },

    /**
     * 判斷遊戲是否有試玩功能
     * @returns {boolean} true - 有試玩, false - 無試玩
     */
    hasDemo() {
      if (!this.game || typeof this.game !== 'object') {
        return false
      }

      return this.game.hasDemo !== 0
    },

    /**
     * 判斷遊戲是否有機器人
     * @returns {boolean} true - 有機器人, false - 無機器人
     */
    hasRobot() {
      if (!this.game || typeof this.game !== 'object') {
        return false
      }

      return this.game.hasRobot !== 0
    },

    /**
     * 判斷遊戲是否有經驗值
     * @returns {boolean} true - 有經驗值, false - 無經驗值
     */
    hasExp() {
      if (!this.game || typeof this.game !== 'object') {
        return false
      }

      return this.game.hasExp !== 0
    },

    /**
     * 遊戲預設 RTP
     * @returns {number|null} RTP 值
     */
    defaultRtp() {
      if (!this.game || typeof this.game !== 'object') {
        return null
      }

      return this.game.rtp
    },

    /**
     * 判斷是否為真人遊戲
     * @returns {boolean} true - 真人遊戲, false - 其他遊戲
     */
    isLive() {
      if (!this.game || typeof this.game !== 'object') {
        return false
      }

      return this.game.categoryType === 200
    },

    /**
     * 判斷是否顯示 RTP 資訊
     * 只有非真人遊戲且有 RTP 資料的遊戲才顯示
     * @returns {boolean} true - 顯示 RTP, false - 不顯示 RTP
     */
    showRtp() {
      if (!this.game || typeof this.game !== 'object') {
        return false
      }

      return !this.isLive && this.hasRtp
    },

    /**
     * RTP 顯示樣式計算
     * 根據遊戲的各種 RTP 數據計算顯示樣式
     * @returns {Object} RTP 樣式物件
     */
    showRTPStyle() {
      // 預設樣式
      const defaultStyle = {
        showRTPTooltipStatus: false,
        dailyShowStatus: false,
        dailyBackgroundColor: 'success',
        dailyIcon: 'trending_down',
        dailyIconColor: 'success--text',
        weeklyShowStatus: false,
        weeklyIcon: 'trending_down',
        weeklyIconColor: 'success--text',
        monthlyShowStatus: false,
        monthlyIcon: 'trending_down',
        monthlyIconColor: 'success--text'
      }

      if (!this.game || typeof this.game !== 'object') {
        return defaultStyle
      }

      // 從 RTP Map 中獲取資料（新架構）
      const rtpData = this.$store.getters['gameHall/getGameRtp'](this.game.id)

      if (!rtpData) {
        return defaultStyle
      }

      const { dailyRtp, weeklyRtp, monthlyRtp } = rtpData
      const defaultRtp = this.defaultRtp

      // 檢查數據有效性
      const isValidNumber = (value) => typeof value === 'number'
      const hasDaily = isValidNumber(dailyRtp)
      const hasWeekly = isValidNumber(weeklyRtp)
      const hasMonthly = isValidNumber(monthlyRtp)

      // 通用樣式計算函數
      const getRtpStyle = (rtpValue) => ({
        isHigher: rtpValue > defaultRtp,
        icon: rtpValue > defaultRtp ? 'trending_up' : 'trending_down',
        iconColor: rtpValue > defaultRtp ? 'error--text' : 'success--text'
      })

      const dailyStyle = hasDaily ? getRtpStyle(dailyRtp) : null
      const weeklyStyle = hasWeekly ? getRtpStyle(weeklyRtp) : null
      const monthlyStyle = hasMonthly ? getRtpStyle(monthlyRtp) : null

      return {
        // 只有三種 RTP 都存在才顯示工具提示
        showRTPTooltipStatus: hasDaily && hasWeekly && hasMonthly,

        // 日 RTP
        dailyShowStatus: hasDaily,
        dailyBackgroundColor: dailyStyle?.isHigher ? 'error' : 'success',
        dailyIcon: dailyStyle?.icon || defaultStyle.dailyIcon,
        dailyIconColor: dailyStyle?.iconColor || defaultStyle.dailyIconColor,

        // 週 RTP
        weeklyShowStatus: hasWeekly,
        weeklyIcon: weeklyStyle?.icon || defaultStyle.weeklyIcon,
        weeklyIconColor: weeklyStyle?.iconColor || defaultStyle.weeklyIconColor,

        // 月 RTP
        monthlyShowStatus: hasMonthly,
        monthlyIcon: monthlyStyle?.icon || defaultStyle.monthlyIcon,
        monthlyIconColor: monthlyStyle?.iconColor || defaultStyle.monthlyIconColor
      }
    }
  },

  methods: {
    /**
     * 處理遊戲錯誤
     * 當收到特定錯誤碼時，設置遊戲為維護狀態
     * @param {number} errorCode - 錯誤碼
     */
    handleGameError(errorCode) {
      console.log(`[gameStatus] 處理遊戲錯誤代碼: ${errorCode}`)

      // 需要設置為維護狀態的錯誤碼
      const maintenanceErrorCodes = [50003, 60001, 60032, 60037, 80001]

      if (maintenanceErrorCodes.includes(errorCode)) {
        this.dynamicMaintaining = true

        // 同步更新 store 中的遊戲列表維護狀態
        this.$store.commit('gameHall/SET_GAMELIST_MAINTAIN_STATUS', {
          gameId: this.game.id,
          status: true
        })
        this.$store.commit('gameHall/SET_ALL_GAME_LIST_MAINTAIN_STATUS', {
          gameId: this.game.id,
          status: true
        })

        console.log(`[gameStatus] 遊戲 ${this.game.id} 已設定為維護狀態`)
      }
    },

    /**
     * 處理遊戲啟動失敗
     * 檢查遊戲是否已在 store 中標記為維護狀態，如果是則設置動態維護
     */
    handleGameFailure() {
      console.log(`[gameStatus] 遊戲 ${this.game.id} 啟動失敗，檢查是否需要設定維護狀態`)

      const gameList = this.$store.getters['gameHall/gameList']
      const allGameList = this.$store.getters['gameHall/allGameList']

      const gameInList = gameList.find((g) => g.id === this.game.id)
      const gameInAllList = allGameList.find((g) => g.id === this.game.id)

      // 如果遊戲已在任一列表中標記為維護狀態，則設置動態維護
      if ((gameInList && gameInList.maintaining) || (gameInAllList && gameInAllList.maintaining)) {
        this.dynamicMaintaining = true
        console.log(`[gameStatus] 遊戲 ${this.game.id} 已設定為動態維護狀態`)
      }
    }
  }
}
