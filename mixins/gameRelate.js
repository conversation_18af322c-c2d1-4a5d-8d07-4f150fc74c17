export default {
  methods: {
    goToLobbyWithGameSortType(propGameSortType = null, gameCategory = null) {
      this.$router.push({
        path: this.localePath('/game'),
        query: {
          gameSortType: propGameSortType,
          gameCategory: gameCategory
        }
      })
    },
    async getSortGameListData(gameCategoryId, sortType) {
      const headerData = {
        username: this.$store.getters['role/userName']
          ? this.$store.getters['role/userName']
          : null,
        alias: null
      }
      const body = {
        headerData,
        gameCategoryId: gameCategoryId,
        sortType: sortType
      }

      const gameList = await this.$clientApi.game.gameSortList(body)
      return gameList
    },

    // 取得不分類遊戲列表
    async getUnCategorizedSortGameListData(sortType, isOnlyId) {
      const headerData = {
        username: this.$store.getters['role/userName']
          ? this.$store.getters['role/userName']
          : null,
        alias: null
      }
      const body = {
        headerData: headerData,
        sortType: sortType,
        lang: this.$i18n.locale,
        isOnlyId: isOnlyId ? 1 : 0,
        limit: 30,
        offset: 0
      }
      let gameList = await this.$clientApi.game.gameUnCategorizedSortList(body)
      return gameList
    }
  }
}
