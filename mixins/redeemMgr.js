export default {
  data() {
    return {}
  },
  methods: {
    redeemOut() {
      this.$wsClient.send(this.$wsPacketFactory.exitService(this.$xinConfig.SOCIAL_SERVICE.ID))
      this.$store.commit('xinProtocol/SET_SERVICE_IDS', {
        serviceId: this.$xinConfig.SOCIAL_SERVICE.ID,
        enable: false,
        connected: false,
        sendId: 0,
        receiveId: 0
      })
    },
    redeemInit() {
      this.redeemOut()
      this.$wsClient.send(this.$wsPacketFactory.getServiceJoin(this.$xinConfig.SOCIAL_SERVICE.ID))
      this.$store.commit('xinProtocol/SET_SERVICE_IDS', {
        serviceId: this.$xinConfig.SOCIAL_SERVICE.ID,
        enable: true
      })
    },

    async redeemCodeRequest(redeemCode) {
      this.$wsClient.send(this.$wsPacketFactory.initRedeemCode())
      this.$nuxt.$loading.start()
      try {
        this.$wsClient.send(this.$wsPacketFactory.changeRedeemCode(redeemCode))
        const isValidFeature = (data) => {
          const features = [
            this.$xinConfig.FEATURE.REDEEM.REDEEM_CODE,
            this.$xinConfig.FEATURE.ACTIVITY.ABILITY
          ]
          if (this.$xinConfig.FEATURE.REDEEM.REDEEM_GIFT !== undefined)
            features.push(this.$xinConfig.FEATURE.REDEEM.REDEEM_GIFT)
          const isFeatures = features.some((feature) => {
            const result = data.isFeature(feature)
            return result
          })
          return isFeatures
        }
        let res = await this.$xinUtility.waitEvent(
          this.$wsClient.receivedListeners,
          isValidFeature,
          6000
        )
        // if (res.type === 0 && res.message.includes('lang_152')) { // 未確定是否需要刪除
        //   res = await this.$xinUtility.waitEvent(
        //     this.$wsClient.receivedListeners,
        //     (data) => data.isFeature(this.$xinConfig.FEATURE.REDEEM.REDEEM_GIFT),
        //     6000
        //   )
        // }
        return res
      } catch (error) {
        console.log('redeem: ', error)
        return undefined
      } finally {
        this.$wsClient.send(this.$wsPacketFactory.initRedeemCode())
        this.$nuxt.$loading.finish()
      }
    }
  }
}
