export default {
  methods: {
    // 取得玩家資訊
    async getPlayerData(name, guildname = '') {
      const stringIsEmpty = (string) => {
        return string === '' || string === null || string === undefined
      }
      const info = await this.$store.dispatch('social/getUserDetail', name)
      const userData = { userName: name }
      const thumbUrl = await this.$store.dispatch('role/getThumbUrl', userData)
      if (this.$UIConfig.lock.guild && stringIsEmpty(guildname)) {
        const guild = await this.getPlayerGuild(name)
        guildname = guild.hasGuild ? guild.guildName : ''
      }
      const avatarFrameIcon = 'rankframe_' + info.levelVip + '.png'
      const role = {
        username: name,
        userName: name,
        rank: info.rank,
        thumbUrl: thumbUrl,
        balance: info.money,
        honor: info.honor,
        level: info.level,
        activeValue: info.activeValue,
        phoneNumber: info.phoneNumber,
        accountType: info.accountType,
        isBind: info.isBind,
        platformId: info.platformId,
        gameId: info.gameId,
        isLogin: info.online,
        vipLevel: parseInt(info.levelVip),
        vipLevelTitle: this.vipLevelTitle,
        vipLevelImgFileName: this.vipLevelImgFileName,
        online: info.online,
        money: info.money,
        guildName: guildname,
        avatarFrameIcon: avatarFrameIcon
      }
      return role
    },
    // 將選取的玩家資訊存入vuex
    async setSelectPlayerInfo(role) {
      this.$store.commit('social/SET_SELECT_FRIEND', role)
    },
    //取得玩家公會
    async getPlayerGuild(name) {
      const reqData = this.$wsPacketFactory.getUserGuild(name)
      this.$wsClient.send(reqData)
      const res = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
        return data.isFeature(this.$xinConfig.FEATURE.GUILD.TYPE.GET_USER_GUILD)
      })
      return res
    },
    //加好友前檢查
    async checkAddFriend(name) {
      const unlockLevel = this.$UIConfig.lock.unlockLevel
      if (this.vipLevel >= unlockLevel) {
        this.$notify.warning(this.$t('official_member_only'))
      } else {
        //黑名單加好友要提醒
        if (this.checkIsBlock(name)) {
          this.showConfirmAddFriendDialog(name)
        } else {
          await this.addFriend(name)
        }
      }
    },
    // 加好友
    async addFriend(name) {
      const reqData = this.$wsPacketFactory.addFriend(name)
      this.$wsClient.send(reqData)
      const resData = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
        return data.isFeature(this.$xinConfig.FEATURE.FRIEND.TYPE.FRIEND_RELATION)
      })
      if (resData.success) {
        const resName = resData.username
        this.$notify.success(this.$t('add_player_success', { player: resName }))
        await this.$store.dispatch('social/addFriend', resData)
        //加好友成功時需要刪除黑名單
        this.$store.commit('social/DELETE_BLOCK', resName)
        this.$emit('update:confirmAddFriendDialogStatus', { show: false, name: '' })
        this.$store.dispatch('chat/addFriendEvent', resName)
      } else {
        const officialName = ['新增失敗:無此玩家', '@lang_42']
        const isNotHavePlayr = officialName.some((text) => {
          return resData.errorMessage.includes(text)
        })
        if (isNotHavePlayr) {
          this.$notify.warning(this.$t('player_not_found'))
        } else {
          this.$notify.error(resData.errorMessage)
        }
      }
    },
    // 加好友dialog (若此玩家為黑名單)
    showConfirmAddFriendDialog(name) {
      this.$nuxt.$emit('root:confirmAddFriendDialogStatus', { show: true, name: name })
    },
    // 刪好友dialog
    showConfirmDeleteFriendDialog(name) {
      this.$nuxt.$emit('root:confirmDeleteFriendDialogStatus', { show: true, name: name })
    },
    // 刪好友
    async deleteFriend(name, needAlert = false) {
      const reqData = this.$wsPacketFactory.removeFriend(name)
      this.$wsClient.send(reqData)
      const resData = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
        return data.isFeature(this.$xinConfig.FEATURE.FRIEND.TYPE.FRIEND_RELATION)
      })
      if (resData.success) {
        this.$store.commit('social/DELETE_FRIEND', name)
        if (needAlert) {
          this.$notify.success(resData.username + ' ' + this.$t('remove_from_friend_list_success1'))
        }
        this.$emit('update:confirmDeleteFriendDialogStatus', { show: false, name: '' })
        this.$store.dispatch('chat/deleteFriendEvent', name)
      } else {
        this.$notify.error(resData.errorMessage)
      }
    },
    //加黑名單前檢查
    async checkAddBlock(name) {
      const unlockLevel = this.$UIConfig.lock.unlockLevel
      if (this.vipLevel >= unlockLevel) {
        this.$notify.warning(this.$t('official_member_only'))
      } else {
        //好友加黑名單要提醒
        if (this.checkIsFriend(name)) {
          this.showConfirmAddBlockDialog(name)
        } else {
          await this.addBlock(name)
        }
      }
    },
    // 加黑名單
    async addBlock(name) {
      const reqData = this.$wsPacketFactory.addBlock(name)
      const selectedName =
        this.$store.getters['chat/chats'].find((chat) => chat.id === this.selectedChat)?.title ||
        null

      this.$wsClient.send(reqData)
      const resData = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
        return data.isFeature(this.$xinConfig.FEATURE.FRIEND.TYPE.FRIEND_RELATION)
      })
      if (resData.success) {
        this.$notify.success(
          this.$t('add_player_to_blacklist_success', { player: resData.username })
        )
        await this.$store.dispatch('social/addBlock', resData)
        // 加黑名單成功時需要刪除好友
        this.$store.commit('social/DELETE_FRIEND', name)
        this.$emit('update:confirmAddBlockDialogStatus', { show: false, name: '' })
        this.$store.dispatch('chat/deleteFriendEvent', name)
        this.$store.dispatch('chat/addBlockEvent', name)
        this.$store.dispatch('chat/countAllChatNoty')
        this.$store.dispatch('chat/countWhisperNoty')
        if (name === selectedName) this.$store.commit('chat/SET_SELECTED_CHAT', 0)
      } else {
        const officialName = ['新增失敗:無此玩家', '@lang_42']
        const isNotHavePlayr = officialName.some((text) => {
          return resData.errorMessage.includes(text)
        })
        if (isNotHavePlayr) {
          this.$notify.warning(this.$t('player_not_found'))
        } else {
          this.$notify.error(resData.errorMessage)
        }
      }
    },
    // 加黑名單dialog (若此玩家為好友)
    showConfirmAddBlockDialog(name) {
      this.$nuxt.$emit('root:confirmAddBlockDialogStatus', { show: true, name: name })
    },
    // 刪黑名單dialog
    showConfirmDeleteBlockDialog(name) {
      this.$nuxt.$emit('root:confirmDeleteBlockDialogStatus', { show: true, name: name })
    },
    // 刪黑名單
    async deleteBlock(name, needAlert = false) {
      const reqData = this.$wsPacketFactory.removeBlock(name)
      this.$wsClient.send(reqData)
      const resData = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
        return data.isFeature(this.$xinConfig.FEATURE.FRIEND.TYPE.FRIEND_RELATION)
      })
      if (resData.success) {
        this.$store.commit('social/DELETE_BLOCK', name)
        this.$store.dispatch('chat/deleteBlockEvent', name)
        this.$store.dispatch('chat/countAllChatNoty')
        this.$store.dispatch('chat/countWhisperNoty')
        if (needAlert) {
          this.$notify.success(
            resData.username +
              ' ' +
              this.$t('remove_from_blacklist_success', {
                player: ''
              })
          )
        }
        this.$emit('update:confirmDeleteBlockDialogStatus', false)
      } else {
        this.$notify.error(resData.errorMessage)
      }
    },
    //檢查是否為好友
    checkIsFriend(name) {
      return (
        !!name && this.friendList.some((item) => item.username.toLowerCase() === name.toLowerCase())
      )
    },
    //檢查是否為黑名單
    checkIsBlock(name) {
      return (
        !!name && this.blockList.some((item) => item.username.toLowerCase() === name.toLowerCase())
      )
    },
    //檢查是否為同公會
    checkIsSameGuild(name) {
      if (!name) return false

      const guildMemberList = this.$store.getters['guild/guildMemberInfoList']
      return guildMemberList.some((item) => item.name.toLowerCase() === name.toLowerCase())
    },
    //檢舉玩家
    async showReportDialog(name) {
      const customerLocalStorage = this.$localStorage.get('customerTimeNoty').userArray
      const customerArray = customerLocalStorage ? JSON.parse(customerLocalStorage) : []
      const usercustomerData = customerArray.find((x) => x.userName === this.userName)
      const customerStorageExpired = usercustomerData
        ? this.$moment(usercustomerData.customerExpired)
        : this.$moment()

      const userLocalStorage = this.$localStorage.get('reportTimeNoty').userArray
      const userReportArray = userLocalStorage ? JSON.parse(userLocalStorage) : []
      const userReportData = userReportArray.find((x) => x.userName === this.userName)
      const reportExpired = userReportData
        ? this.$moment(userReportData.reportExpired)
        : this.$moment()
      if (this.$moment().isBefore(reportExpired)) {
        const diff = reportExpired.diff(this.$moment(), 'seconds')
        const showTimeString =
          diff > 60
            ? this.$t('report_alert_text1', {
                min: reportExpired.diff(this.$moment(), 'minutes')
              })
            : this.$t('report_alert_text2', {
                second: diff
              })
        this.$notify.warning(showTimeString)
      } else if (this.$moment().isBefore(customerStorageExpired)) {
        this.$notify.warning(
          this.$t('report_alert_text3', {
            second: customerStorageExpired.diff(this.$moment(), 'seconds')
          })
        )
      } else {
        if (this.vipLevel >= 1)
          this.$nuxt.$emit('root:reportDialogStatus', { show: true, name: name })
        else this.$notify.warning(this.$t('unofficial_members_report'))
      }
    }
  },
  computed: {
    friendList({ $store }) {
      return $store.getters['social/friendList']
    },
    vipLevelImgFileName({ $store }) {
      return $store.getters['role/vipLevelImgFileName']
    },
    vipLevelTitle({ $store }) {
      return $store.getters['role/vipLevelTitle']
    },
    blockList({ $store }) {
      return $store.getters['social/blockList']
    },
    selectPlayer({ $store }) {
      return $store.getters['social/selectPlayer']
    },
    vipLevel({ $store }) {
      return $store.getters['role/vipLevel']
    },
    level({ $store }) {
      return $store.getters['role/level']
    },
    guildMemberList({ $store }) {
      return $store.getters['guild/guildMemberList']
    },
    userName({ $store }) {
      return $store.getters['role/userName']
    },
    selectedChat({ $store }) {
      return $store.getters['chat/selectedChat']
    },
    maintainSystem({ $store }) {
      return $store.getters['maintain/system']
    }
  }
}
