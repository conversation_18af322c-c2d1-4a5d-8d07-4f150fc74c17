import { cloneDeep } from 'lodash'
import gameRelate from '@/mixins/gameRelate'
import gameStatus from '@/mixins/gameStatus'
import utilWhiteList from '@/utils/whiteList.js'

export default {
  mixins: [gameRelate, gameStatus],
  data() {
    return {
      list: {
        inCompleteGame: [],
        recentGame: [],
        liveGame: [],
        hotGame: [],
        fishingGame: [],
        featuredGame: [],
        chessAndCardGame: []
      },
      // 各站台可覆寫這些配置
      liveGameSet: {
        max: 15,
        sortType: 2,
        category: 200
      },
      popularGameSet: {
        hotGame: {
          max: 15,
          sortType: 2,
          category: 100
        },
        fishingGame: {
          max: 15,
          sortType: 2,
          category: 600
        },
        chessAndCardGame: {
          max: 15,
          sortType: 2,
          category: 300
        }
      },
      recentGameSet: {
        max: 10
      }
    }
  },
  computed: {
    providers({ $store }) {
      return $store.getters['gameProvider/providers']
    },
    allGameList({ $store }) {
      return $store.getters['gameHall/allGameList']
    },
    playedGameList({ $store }) {
      return $store.getters['gameHall/playedGameList']
    },
    isLogin({ $store }) {
      return $store.getters['role/isLogin']
    },
    userName({ $store }) {
      return $store.getters['role/userName']
    },
    gameShowMax() {
      return this.recentGameSet?.max || 10
    },
    inCompleteGameList({ list }) {
      return list.inCompleteGame.length > 0
        ? this.filterList(this.getGameListId(list.inCompleteGame), list.inCompleteGame)
        : []
    },
    recentGameList({ list }) {
      return list.recentGame.length > 0
        ? this.filterList(this.getGameListId(list.recentGame), list.recentGame)
        : []
    },
    liveGameList({ list }) {
      return list.liveGame.length > 0
        ? this.filterList(this.getGameListId(list.liveGame), list.liveGame)
        : []
    },
    hotGameList({ list }) {
      return list.hotGame.length > 0
        ? this.filterList(this.getGameListId(list.hotGame), list.hotGame)
        : []
    },
    fishingGameList({ list }) {
      return list.fishingGame.length > 0
        ? this.filterList(this.getGameListId(list.fishingGame), list.fishingGame)
        : []
    },
    chessAndCardGameList({ list }) {
      return list.chessAndCardGame.length > 0
        ? this.filterList(this.getGameListId(list.chessAndCardGame), list.chessAndCardGame)
        : []
    },
    featuredGameList({ list }) {
      if (list.featuredGame.length > 0) {
        const featuredGames = this.filterFeaturedList(
          this.getGameListId(list.featuredGame),
          list.featuredGame
        )
        const uniqueGroups = new Set(list.featuredGame.map((game) => game.group))
        const uniqueGroupsArray = Array.from(uniqueGroups).filter(
          (group) => typeof group === 'string' && group.length > 0
        )
        return {
          group: cloneDeep(uniqueGroupsArray),
          data: cloneDeep(featuredGames)
        }
      } else {
        return {
          group: [],
          data: []
        }
      }
    }
  },
  methods: {
    // 初始化方法 - 各站台可覆寫
    async init() {
      if (!this.isLogin) {
        this.fetchLocalPlayedGameList()
      } else {
        this.fetchServerPlayedGames()
      }
      this.getPopularGameList()
      this.getLiveGameList()
      this.getFeaturedGamesList()
      // 如果站台有 newestGame，需要在覆寫的 init 中加入
    },

    // 獲取熱門遊戲列表
    async getPopularGameList() {
      const getData = async (gameSet) => {
        try {
          const rawGameData = await this.getPopularGamesEvent(
            this.providers,
            gameSet.max,
            gameSet.category,
            gameSet.sortType
          )
          return rawGameData
        } catch (error) {
          return []
        }
      }

      const gamePromises = []
      const gameTypes = []

      // 動態處理各種遊戲類型
      if (this.popularGameSet.hotGame) {
        gamePromises.push(getData(this.popularGameSet.hotGame))
        gameTypes.push('hotGame')
      }
      if (this.popularGameSet.fishingGame) {
        gamePromises.push(getData(this.popularGameSet.fishingGame))
        gameTypes.push('fishingGame')
      }
      if (this.popularGameSet.chessAndCardGame) {
        gamePromises.push(getData(this.popularGameSet.chessAndCardGame))
        gameTypes.push('chessAndCardGame')
      }
      if (this.popularGameSet.recommendGame) {
        gamePromises.push(getData(this.popularGameSet.recommendGame))
        gameTypes.push('recommendGame')
      }

      const results = await Promise.all(gamePromises)

      // 動態賦值結果
      results.forEach((result, index) => {
        this.list[gameTypes[index]] = cloneDeep(result)
      })
    },

    // 獲取真人遊戲列表
    async getLiveGameList() {
      try {
        let liveGames = await this.getSortGameListData(
          this.liveGameSet.category,
          this.liveGameSet.sortType
        )
        liveGames = liveGames.count > 0 ? liveGames.list : []

        if (liveGames.length !== 0) {
          liveGames.forEach((gameItem) => {
            gameItem.thumbUrl = gameItem.thumbUrl || this.$store.getters['gameHall/gameDefaultImg']
            gameItem.categoryType = this.liveGameSet.category
          })
        }
        const limitedLiveGames = liveGames.slice(0, this.liveGameSet.max)
        this.$store.commit('gameHall/ADD_ALL_GAME_LIST', limitedLiveGames)
        this.list.liveGame = cloneDeep(limitedLiveGames)
      } catch (error) {
        this.list.liveGame = []
      }
    },

    // 獲取精選遊戲列表
    async getFeaturedGamesList() {
      try {
        const rawGameData = await this.getFeaturedGamesEvent(this.providers)
        this.list.featuredGame = cloneDeep(rawGameData)
      } catch (error) {
        this.list.featuredGame = []
      }
    },

    // 獲取最近遊戲列表
    async getRecentGameList() {
      try {
        const rawGameData = await this.getRecentGamesEvent(this.playedGameList)
        this.list.recentGame = cloneDeep(rawGameData)
      } catch (error) {
        this.list.recentGame = []
      }
    },

    // 獲取未完成遊戲列表
    async getInCompleteGameList() {
      try {
        const rawGameData = await this.getInCompleteGameData()
        this.list.inCompleteGame = cloneDeep(rawGameData)
      } catch (error) {
        this.list.inCompleteGame = []
      }
    },

    // 以下是所有共用的核心方法
    async getPopularGamesData(gameCategoryId, type) {
      let games = await this.getSortGameListData(gameCategoryId, type)
      games = games.count > 0 ? games.list : []
      return games
    },

    async getPopularGamesEvent(providers, max, gameCategory, sortType) {
      if (providers.length > 0) {
        const hasRtp = (item, provider) =>
          item.platformId == provider.id && provider.hasRtp === true
        const getGames = (showGamesCount, games) => {
          games = games.filter((item) => providers.some((provider) => hasRtp(item, provider)))
          return games.slice(0, showGamesCount)
        }
        let list = []

        await this.getPopularGamesData(gameCategory, sortType).then((games) => {
          games.forEach((item) => {
            item.categoryType = gameCategory
            item.thumbUrl = item.thumbUrl || this.$store.getters['gameHall/gameDefaultImg']
          })
          list = getGames(max, games)
        })

        // 使用統一的 RTP 更新方法
        this.$store.commit('gameHall/ADD_ALL_GAME_LIST', list)

        // 獲取 RTP 資料（只更新 RTP Map，不合併到遊戲資料）
        // 過濾掉真人遊戲（categoryType === 200）
        const nonLiveGameIds = list
          .filter((game) => game.categoryType !== 200)
          .map((game) => game.id)

        if (nonLiveGameIds.length > 0) {
          await this.$store.dispatch('gameHall/fetchRTPList', {
            gameIds: nonLiveGameIds,
            options: { fallbackToAPI: true }
          })
        }

        return list
      }
    },

    async getFeaturedGamesDataHandler() {
      let tmp = await this.$axios.get(
        process.env.IMAGE_URL +
          `/index/gameProvider/${window.loadConfig.client_id}/gameProvider.json?` +
          Math.random()
      )
      const headerData = {
        username: this.$store.getters['role/userName'] || null,
        alias: null
      }
      const gameIds = utilWhiteList.filterRecentGames(
        tmp.data.map((item) => item.id),
        window.location.origin
      )
      const tmpGameList = await this.$clientApi.game.gameList(
        headerData,
        gameIds,
        this.$i18n.locale
      )
      const gameList = tmpGameList.list

      tmp.data.forEach((element) => {
        element.gameImg = process.env.IMAGE_URL + '/index/gameImg/' + element.gameImg

        gameList.forEach((item) => {
          if (element.id === item.id) {
            element.gameCover = `${process.env.IMAGE_URL}${item.thumbPath}`
            element.categoryType = item.categoryType
            element.title = item.name
            element.rtp = item.rtp
            element.enable = item.enable
            element.vipLevel = item.vipLevel
            element.maintainBeginAt = item.maintainBeginAt
            element.maintainEndAt = item.maintainEndAt
            element.maintaining = item.maintaining
            element.platformId = item.platformId
            element.hasExp = item.hasExp
            element.hasRobot = item.hasRobot
            element.hasScrollbar = item.hasScrollbar
            element.dailyRtp = undefined
            element.weeklyRtp = undefined
            element.monthlyRtp = undefined
            element.hasDemo = item.hasDemo
          }
        })
      })
      // 如果 this.$clientApi.game.gameList 沒有配對到遊戲，則不顯示
      const result = tmp.data.filter((item) => item.title !== undefined)
      return result
    },

    async getFeaturedGamesEvent(providers) {
      if (providers.length > 0) {
        // 取得精選遊戲清單
        let getFeaturedGamesData = await this.getFeaturedGamesDataHandler()
        // 先呈現 RTP最後更新
        let featuredGameListTmp = getFeaturedGamesData

        // 先加入 store
        this.$store.commit('gameHall/ADD_ALL_GAME_LIST', featuredGameListTmp)

        // 使用統一的 RTP 更新方法（只更新 RTP Map）
        // 過濾掉真人遊戲（categoryType === 200）
        const nonLiveGameIds = featuredGameListTmp
          .filter((game) => game.categoryType !== 200)
          .map((game) => game.id)

        if (nonLiveGameIds.length > 0) {
          await this.$store.dispatch('gameHall/fetchRTPList', {
            gameIds: nonLiveGameIds,
            options: { fallbackToAPI: true }
          })
        }

        return featuredGameListTmp
      }
    },

    async getRecentGamesEvent(playedGameList) {
      if (playedGameList.length > 0) {
        const gameIds = utilWhiteList.filterRecentGames(
          playedGameList.map(Number),
          window.location.origin
        )
        const headerData = {
          username: this.$store.getters['role/userName'] || null,
          alias: null
        }
        const tmpGameList = await this.$clientApi.game.gameList(
          headerData,
          gameIds,
          this.$i18n.locale
        )
        const gameList = tmpGameList.list
        // 對 gameList 進行排序
        const sortedGameList = gameList.sort((a, b) => {
          const indexA = gameIds.indexOf(a.id)
          const indexB = gameIds.indexOf(b.id)
          // 如果元素在第一個陣列中不存在，則將其放在最後
          if (indexA === -1) return 1
          if (indexB === -1) return -1
          return indexA - indexB
        })

        let setupGames = this.setupRecentGames(sortedGameList)
        let processedGames = setupGames.slice(0, this.gameShowMax)

        // 先加入 store
        this.$store.commit('gameHall/ADD_ALL_GAME_LIST', processedGames)

        // 使用統一的 RTP 更新方法（只更新 RTP Map）
        // 過濾掉真人遊戲（categoryType === 200）
        const nonLiveGameIds = processedGames
          .filter((game) => game.categoryType !== 200)
          .map((game) => game.id)

        if (nonLiveGameIds.length > 0) {
          await this.$store.dispatch('gameHall/fetchRTPList', {
            gameIds: nonLiveGameIds,
            options: { fallbackToAPI: true }
          })
        }

        return processedGames
      } else {
        return []
      }
    },

    setupRecentGames(gameList) {
      const gameListValue = gameList
        .filter((game) => game)
        .map((game) => ({
          ...game,
          thumbUrl:
            game.thumbPath === null
              ? this.$store.getters['gameHall/gameDefaultImg']
              : `${process.env.IMAGE_URL}${game.thumbPath}`
        }))
      return gameListValue
    },

    async getInCompleteGameData() {
      const userName = this.$store.getters['role/userName']
      const gameIds = await this.$clientApi.game.getInCompleteGameList(userName)
      const headerData = {
        username: userName || null,
        alias: null
      }
      if (!gameIds.list?.length) {
        return []
      }

      const setupGameIds = gameIds.list.filter(
        (id) => !this.allGameList.some((game) => game.id === id)
      )
      const games = cloneDeep(
        gameIds.list
          .filter((id) => this.allGameList.some((game) => game.id === id))
          .map((id) => this.allGameList.find((game) => game.id === id))
      )
      const { list } =
        setupGameIds.length > 0
          ? await this.$clientApi.game.gameList(headerData, setupGameIds, this.$i18n.locale)
          : { list: [] }

      if (games.length > 0) {
        list.push(...games)
      }
      if (list.length > 0) {
        list.forEach((item) => {
          item.thumbUrl =
            item.thumbPath === null
              ? this.$store.getters['gameHall/gameDefaultImg']
              : `${process.env.IMAGE_URL}${item.thumbPath}`
        })
        const sortedGameList = list.sort((a, b) => {
          const indexA = gameIds.list.indexOf(a.id)
          const indexB = gameIds.list.indexOf(b.id)
          // 如果元素在第一個陣列中不存在，則將其放在最後
          if (indexA === -1) return 1
          if (indexB === -1) return -1
          return indexA - indexB
        })

        // 先加入 store
        this.$store.commit('gameHall/ADD_ALL_GAME_LIST', sortedGameList)

        // 使用統一的 RTP 更新方法（只更新 RTP Map）
        // 過濾掉真人遊戲（categoryType === 200）
        const nonLiveGameIds = sortedGameList
          .filter((game) => game.categoryType !== 200)
          .map((game) => game.id)

        if (nonLiveGameIds.length > 0) {
          await this.$store.dispatch('gameHall/fetchRTPList', {
            gameIds: nonLiveGameIds,
            options: { fallbackToAPI: true }
          })
        }

        return sortedGameList
      } else {
        return []
      }
    },

    async getMaintainList() {},

    getGameListId(list) {
      return list.map((obj) => obj.id)
    },

    filterList(ids, list) {
      const filterList = ids
        .map((id) => this.allGameList.find((item) => item.id === id))
        .filter((item) => item !== undefined)
      const finalList = filterList.map((obj) => {
        const match = list.find((a) => a.id === obj.id)
        if (match) {
          return {
            ...obj,
            enable: match.enable,
            name: match.name,
            hasDemo: match.hasDemo,
            hasExp: match.hasExp,
            hasRobot: match.hasRobot,
            thumbUrl: match.thumbUrl,
            maintaining: match.maintaining || false,
            maintainBeginAt: match.maintainBeginAt || null,
            maintainEndAt: match.maintainEndAt || null
          }
        }
        return obj
      })
      return finalList
    },

    filterFeaturedList(ids, list) {
      const filterList = ids
        .map((id) => this.allGameList.find((item) => item.id === id))
        .filter((item) => item !== undefined)
      const finalList = filterList.map((obj) => {
        const match = list.find((a) => a.id === obj.id)
        if (match) {
          return {
            ...obj,
            bgColor: match.bgColor,
            gameCover: match.gameCover,
            gameImg: match.gameImg,
            title: match.title,
            category: match.category,
            group: match.group
          }
        }
        return obj
      })
      return finalList
    },

    fetchLocalPlayedGameList() {
      let storageJsonData
      // 因為更改陣列結構，改為一維陣列，所以需要判斷是否有舊資料，登入後會刷新舊資料
      storageJsonData = this.$localStorage.get('playedGameList').playedGameList
        ? this.$localStorage.get('playedGameList').playedGameList
        : this.$localStorage.get('playedGameList')
      const storageGameList = storageJsonData ? JSON.parse(storageJsonData) : []
      if (storageGameList.length !== 0) {
        this.$store.dispatch('gameHall/setPlayedGameList', storageGameList)
      }
    },

    async fetchServerPlayedGames() {
      const serverGamesId = await this.$clientApi.game.gamePlayed(this.userName)
      const gameIds = serverGamesId.list.map(Number)
      this.$store.dispatch('gameHall/setPlayedGameList', gameIds)
    }
  },
  watch: {
    playedGameList: {
      async handler(val) {
        this.getRecentGameList(val)
      }
    },
    isLogin: {
      async handler(val) {
        if (val) {
          this.fetchServerPlayedGames()
          // 暫停中遊戲
          // 6/17 通知先關閉該功能
          //this.getInCompleteGameList()
        }
      }
    }
  }
}
