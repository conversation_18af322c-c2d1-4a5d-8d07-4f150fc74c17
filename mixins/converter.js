//若是國際版回傳的訊息 開頭就會固定有@
//轉換器 當後端傳來的資文字訊息都需要經過轉換器處理 以相容多國語系
//例如 : @lang_06$皓翔$10
//@後為訊息代碼  $後為參數
//轉換後 會回傳 this.$t('lang_06', {0: '皓翔', 1: '10'})
// 建立 Trie 樹
import convertTimeMgr from '~/utils/convertTime'
function buildTrie(words) {
  const root = {}
  for (const word of words) {
    let node = root
    for (const char of word) {
      node[char] = node[char] || {}
      node = node[char]
    }
    node.isEnd = true
  }
  return root
}
// 在字串中查找所有敏感詞的位置
function findKeywords(str, trie) {
  const results = []
  const len = str.length

  for (let i = 0; i < len; i++) {
    let node = trie
    let word = ''
    let j = i

    while (j < len && node[str[j]]) {
      node = node[str[j]]
      word += str[j]
      if (node.isEnd) {
        // 檢查是否為完整單詞 (前後為空格或標點符號)
        const prevChar = i > 0 ? str[i - 1] : ' '
        const nextChar = j < len - 1 ? str[j + 1] : ' '
        if (isBoundary(prevChar) && isBoundary(nextChar)) {
          results.push({
            start: i,
            end: j + 1,
            word
          })
        }
      }
      j++
    }
  }
  return results
}
// 判斷字符是否為單詞邊界
function isBoundary(char) {
  return /[\s.,!?;:'"()[\]{}<>]/.test(char) || !char
}
function isPhoneNumber(number) {
  // 移除所有空格和特殊字符，只保留數字、加號和星號
  const cleanNumber = number.replace(/[^0-9+*]/g, '')

  const patterns = {
    // 馬來西亞: 處理遮罩的版本
    malaysia: /^(01[0-46-9][\d*]{7,8}|0[2-9][\d*]{7,8})$/,

    // 台灣: 處理遮罩的版本 (+886 或 0 開頭)
    taiwan: /^(\+886[\d*]{9}|0[\d*]{9})$/,

    // 越南: 處理遮罩的版本
    vietnam: /^0[1-9][\d*]{8}$/,

    // 印度: 處理遮罩的版本
    india: /^(\+91[6-9][\d*]{9}|0[6-9][\d*]{9})$/
  }
  // 檢查是否符合任一國家的格式
  return Object.values(patterns).some((pattern) => pattern.test(cleanNumber))
}
function isValidIP(ip) {
  // IPv4 地址的正則表達式
  const ipPattern =
    /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/

  return ipPattern.test(ip)
}
export default {
  methods: {
    convertMessage(message) {
      const pattern = /^@lang_\d/
      if (message && this.isInternational) {
        const cleanMessage = message.replace(/^[^@]/, '')
        if (pattern.test(cleanMessage)) {
          //參數列表
          let parametersList = cleanMessage.split('\uFF04')
          //訊息代碼
          let resMsgCode = parametersList[0].replace('@', '')
          //移除resMsgCode
          parametersList.shift()
          if (resMsgCode.includes('lang_427')) {
            return this.convertGuildLetter(resMsgCode, parametersList)
          }
          const parameters = {}
          for (let i = 0; i < parametersList.length; i++) {
            const param = parametersList[i]

            // 如果是電話或IP，直接使用原值
            if (isPhoneNumber(param) || isValidIP(param)) {
              parameters[i] = param
              continue
            }
            // 如果是數字且不是電話
            const isNumber = (str) => /^\d{1,3}(,\d{3})*$|^\d+$/.test(str)
            if (isNumber(param)) {
              parameters[i] = this.$numberFormatter.station(parseFloat(param))
              continue
            }
            // 如果是翻譯key
            if (pattern.test(param)) {
              parameters[i] = this.$t(param.replace('@', '').toLocaleString(), {})
              continue
            }
            // 如果是時間戳
            if (param.includes('timestamp_')) {
              parameters[i] = convertTimeMgr
                .convertUnixTime(
                  param.split('_')[1],
                  this.$UIConfig.timeStamp.format,
                  this.$UIConfig.timeStamp.timezone
                )
                .format(`(${convertTimeMgr.getGMTOffset(this.$UIConfig.timeStamp.timezone)}),`)
              continue
            }
            // 預設使用原值
            parameters[i] = param
          }

          return this.replaceKeywords(this.$t(resMsgCode, parameters))
        }
      }
      //國內版就直接回傳訊息
      return this.replaceKeywords(message)
    },
    convertGuildLetter(resCode, dataList) {
      const processWithReduce = (array) => {
        return array.reduce((result, value, index) => {
          if (index % 2 === 0 && array[index + 1]) {
            result.push(`${value}:${array[index + 1]}`)
          }
          return result
        }, [])
      }
      const parameters = {
        0: dataList.shift(),
        1: convertTimeMgr.convertUnixTime(
          dataList.shift().split('_')[1],
          'DD/MM/YYYY',
          this.$UIConfig.timeStamp.timezone
        ),
        2: dataList.shift()
      }
      const letterNoty = this.$t(resCode, parameters) + processWithReduce(dataList).join('\r\n')
      return letterNoty
    },
    replaceKeywords(str, compiledData = this.compiledData) {
      if (!this.literalPrison || !str) return str

      // 懶加載建立 Trie，只在第一次或 compiledData 變化時重建
      if (!this.trieCache || this.trieCache.version !== compiledData.length) {
        this.trieCache = {
          trie: buildTrie(compiledData),
          version: compiledData.length
        }
      }

      // 找出所有需要替換的位置
      const matches = findKeywords(str, this.trieCache.trie)
      if (matches.length === 0) return str

      // 一次性替換所有敏感詞
      const parts = []
      let lastIndex = 0

      for (const match of matches) {
        parts.push(str.substring(lastIndex, match.start))
        parts.push('*'.repeat(match.word.length))
        lastIndex = match.end
      }
      parts.push(str.substring(lastIndex))

      return parts.join('')
    },
    convertTime(expiryDate) {
      if (this.isInternational)
        return convertTimeMgr
          .convertUnixTime(
            expiryDate.split('_')[1],
            this.$UIConfig.timeStamp.format,
            this.$UIConfig.timeStamp.timezone
          )
          .format(`(${convertTimeMgr.getGMTOffset(this.$UIConfig.timeStamp.timezone)}),`)

      return expiryDate
    }
  },
  computed: {
    literalPrison() {
      return this.$UIConfig.lock.literalPrison
    },
    compiledData({ $store }) {
      return $store.getters['literalKeywords/compiledData']
    },
    // 是否為國際版
    isInternational() {
      return this.$UIConfig.lock.stationConvert
    }
  },
  data() {
    return {
      trieCache: null
    }
  }
}
