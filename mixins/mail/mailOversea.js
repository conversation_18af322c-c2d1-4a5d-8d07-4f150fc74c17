import dateTimeFormatter from '@/mixins/dateTimeFormatter.js'
import translator from '~/mixins/translator'
import converter from '~/mixins/converter'
import convertTime from '~/utils/convertTime'
import analytics from '~/mixins/analytics'
const NUXT_ENV = process.env.NUXT_ENV === 'development' ? 'staging' : process.env.NUXT_ENV
const loadConfig = require(`~/station/${process.env.STATION}/${NUXT_ENV}.js`).default

export default {
  mixins: [dateTimeFormatter, translator, converter, analytics],
  data() {
    return {
      selection: null,
      mailDialogStatusTmp: this.mailDialogStatus,
      selected_mail: [1],
      getSingleMail: {
        content: this.$t('please_select_letter')
      },
      allReceiveBtnStatus: false,
      delReadBtnStatus: false,
      confirmDeletionStatus: false,
      showRemindBindPhoneDialogStatus: false,
      showRemindPhoneOrientationDialogStatus: false,
      confirmDeletionAllStatus: false,
      confirmReadAllStatus: false,
      showMailSendDialogStatus: false,
      receiveAttachmentStatus: false,
      mobile: {
        showMailContentstatus: false,
        showFABstatus: false,
        mailListMobileScrollTop: 0
      },
      //'new' or 'reply'
      sendMode: 'new',
      showConfirmGuildInviteDialogStatus: false,
      isAccept: null,
      mailCategoryType: 0,
      noMailMessages: {
        0: 'no_mail_available',
        1: 'no_mail_official',
        2: 'no_customer_messages',
        3: 'no_mail_player'
      },
      mailItemRefs: {},
      isReceiveAllLoading: false,
      isDeleteReadAllLoading: false,
      itemInfo: [],
      displayOrder: [],
      defaultImgWebp: require('~/assets/image/inventory_card_default.webp'),
      defaultImgPng: require('~/assets/image/inventory_card_default.png'),
      receiveableItems: []
    }
  },
  computed: {
    maintainSystem() {
      return this.$store.getters['maintain/system']
    },
    mailList({ $store }) {
      return $store.getters['mail/list']
    },
    isLogin({ $store }) {
      return $store.getters['role/isLogin']
    },
    isBind({ $store }) {
      return $store.getters['role/isBind']
    },
    level({ $store }) {
      return $store.getters['role/level']
    },
    vipLevelUpThanBronze({ $store }) {
      return $store.getters['role/vipLevel'] >= 1
    },
    hasLink() {
      return this.getSingleMail.link !== ''
    },
    isFromOffical() {
      const stringIsNullOrEmpty = (str) => {
        return str === null || str === undefined || str === ''
      }
      if (stringIsNullOrEmpty(this.getSingleMail.title)) return false
      return this.convertMessage(this.getSingleMail.title) !== this.$t('player_mail')
    },
    defaultRecipient() {
      let returnData = ''
      if (!this.isFromOffical && this.getSingleMail?.from && this.sendMode == 'reply') {
        returnData = this.getSingleMail.from
      } else if (this.openSendMailDirectly) {
        returnData = this.mailDialogStatusTmp.name
      } else {
        returnData = ''
      }

      return returnData
    },
    showNotyStatus({ $store }) {
      return $store.getters['mail/getShowNotyStatus']
    },
    goMailListTopStatus({ $store }) {
      return $store.getters['mail/getScrollTopStatus']
    },
    orientation({ $store }) {
      return $store.getters['deviceManagement/getOrientation']
    },
    serverTimestamp({ $store }) {
      return $store.getters['xinProtocol/serverTimestamp']
    },
    openSendMailDirectly({ $store }) {
      return $store.getters['mail/openSendMailDirectly']
    },
    hasGuild() {
      return this.$store.getters['guild/hasGuild'] && this.$UIConfig.lock.guild
    },
    totalCount() {
      return this.getSingleMail.count + (this.getSingleMail.items?.[0]?.count || 0)
    },
    categorizedList() {
      return this.mailList.map((item) => {
        const categoryType = this.categoryMap(item.from)
        return { ...item, categoryType }
      })
    },
    showMailDataArrTmp() {
      let filteredList = this.categorizedList
      if (this.mailCategoryType !== 0) {
        filteredList = this.categorizedList.filter(
          (mail) => mail.categoryType === this.mailCategoryType
        )
      }
      if (filteredList.length === 0) {
        const key = this.noMailMessages[this.mailCategoryType]
        if (key) {
          this.getSingleMail.content = this.$t(key)
        }
      } else if (this.selection) {
        const mailObj = filteredList.find((el) => el.mailId === this.selection)
        this.$nextTick(() => {
          this.getSingleMail = { ...mailObj }
        })
      }
      this.checkTopBtn(filteredList)
      return filteredList
    },
    mailCategoryList() {
      const mailCategoryList = [
        {
          categoryType: 0,
          name: this.$t('mail_category_all')
        },
        {
          categoryType: 1,
          name: this.$t('mail_category_official')
        },
        {
          categoryType: 2,
          name: this.$t('mail_customer_service')
        },
        {
          categoryType: 3,
          name: this.$t('mail_category_player')
        }
      ]
      mailCategoryList.forEach((category) => {
        if (category.categoryType === 0) {
          category.count = this.categorizedList.filter((item) => !item.isRead).length
        } else {
          category.count = this.categorizedList.filter(
            (item) => item.categoryType === category.categoryType && !item.isRead
          ).length
        }
      })
      return mailCategoryList
    },
    isHiddenSendMailTime() {
      return (
        this.getSingleMail.creatDate === null ||
        this.$moment(this.getSingleMail.creatDate).isBefore('2008-01-01 00:00:00')
      )
    }
  },
  watch: {
    mailDialogStatus: {
      handler(status) {
        this.mailDialogStatusTmp = status
        // 信箱開啟後，置頂
        if (status && this.goMailListTopStatus) this.$nextTick(() => this.goMailListTop())
        this.$store.commit('mail/SET_SORT_ITEMS')

        if (this.showMailDataArrTmp.length === 0) {
          //顯示各分類無信件時的文字
          const key = this.noMailMessages[this.mailCategoryType]
          if (key) {
            this.getSingleMail.content = this.$t(key)
          }
          this.allReceiveBtnStatus = false
          this.delReadBtnStatus = false
          this.confirmDeletionAllStatus = false
        } else {
          this.checkTopBtn(this.showMailDataArrTmp)
        }
        //等待所按鈕狀態確認完畢後，再確認是否顯示通知
        if (this.mailDialogStatusTmp && this.receiveAttachmentStatus && this.showNotyStatus) {
          this.$nextTick(() => {
            //確保dialog已經完全開啟
            setTimeout(() => {
              this.$store.commit('easyDialog/SET_TITLE', this.$t('reminder'))
              this.$store.commit('easyDialog/SET_MESSAGE', this.$t('mail_receive_attachment_noty'))
              this.$nuxt.$emit('root:showNotyDialogStatus', true)
            }, 100)
          })
        }
      },
      immediate: true
    },
    orientation: {
      handler(orientation) {
        if (orientation === 0) {
          this.showRemindPhoneOrientationDialogStatus = false
        } else if (this.$device.isMobile) {
          this.showRemindPhoneOrientationDialogStatus = true
        }
      },
      immediate: true
    },
    openSendMailDirectly: {
      handler(status) {
        if (status) {
          this.$nextTick(() => {
            this.showMailSendDialogStatus = true
          })
        }
      },
      immediate: true
    },
    getSingleMail: {
      async handler() {
        if (this.itemInfo.length === 0) await this.getItemInfo()
        this.getReceiveableItems()
      },
      immediate: true
    }
  },
  async created() {
    this.$store.dispatch('role/updateUserDetail')
  },
  mounted() {
    this.setOrientation()
    //偵測手機轉向是否改變
    window.addEventListener('orientationchange', this.setOrientation)
  },
  beforeDestroy() {
    window.removeEventListener('orientationchange', this.setOrientation)
    this.$store.commit('mail/SET_OPEN_SEND_MAIL_DIRECTLY', false)
  },
  methods: {
    formatPrice(value) {
      return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    },
    backToMailList() {
      this.mobile.showMailContentstatus = false
      this.$nextTick(() => {
        document.getElementById('mail-list-mobile').scrollTop = this.mailListMobileScrollTop
      })
    },
    //btn狀態按鈕檢查
    checkTopBtn(mailData) {
      const now = this.$moment().subtract(this.serverTimestamp, 'milliseconds')
      // 符合以下條件，則會跳通知:9753
      // 1.未讀
      // 2.有附件
      // 3.24小時內且未逾時
      let receiveAttachmentStatusTmp = false
      let allReceiveBtnStatusTmp = false
      let delReadBtnStatusTmp = false
      mailData.forEach((item) => {
        if (!item.isRead) {
          const expireTime = this.$moment(item.expire)
          const remainHours = expireTime.diff(now, 'hours')
          const remainSeconds = expireTime.diff(now, 'seconds')
          if (item.itemType > 0 && 0 < remainSeconds && remainHours <= 24) {
            receiveAttachmentStatusTmp = true
          }
          // 符合以下任一條件，全部領取按鈕狀態改為true
          // 1.未逾期，信件 itemType > 0，且可領取
          // 2.未逾期，信件 itemType = 0，且可已讀的信件
          if (
            0 < remainSeconds &&
            ((0 < item.itemType && item.canReceive) ||
              (item.itemType === 0 && this.checkMailCanOpen(item)))
          ) {
            allReceiveBtnStatusTmp = true
          }
        } else if (0 <= item.itemType && item.isRead && !item.guildInvitationLetter) {
          delReadBtnStatusTmp = true
        }
      })
      //無信件後，預設刪除已讀按鈕為不可按
      if (mailData.length == 0) {
        delReadBtnStatusTmp = false
      }
      this.delReadBtnStatus = delReadBtnStatusTmp
      this.receiveAttachmentStatus = receiveAttachmentStatusTmp
      this.allReceiveBtnStatus = allReceiveBtnStatusTmp
    },
    closeDialog() {
      this.selection = null
      this.getSingleMail.content = this.$t('please_select_letter')
      this.mobile.showMailContentstatus = false
      this.$emit('update:mailDialogStatus', { show: false, name: '' })
    },
    async doShowMailContent(mail) {
      // show mail content page for mobile
      if (this.$vuetify.breakpoint.xsOnly) {
        this.mailListMobileScrollTop = document.getElementById('mail-list-mobile').scrollTop
        this.mobile.showMailContentstatus = true
      }
      const index = this.findIndexByMailId(mail.mailId)
      // 若是公會邀請信，檢查公會名稱是否有更換，若有更換則更新 store 的資料
      if (mail.guildInvitationLetter) {
        const guildInfo = await this.getGuildDetail(mail.itemType)
        if (mail.from !== guildInfo.guildname) {
          const newMail = {
            ...mail,
            from: guildInfo.guildname
          }
          this.$store.commit('mail/UPDATE_SINGLE_MAIL', { idx: index, data: newMail })
        }
      }
      if (index !== -1) {
        const element = this.showMailDataArrTmp[index]
        // 有附件且非星幣類型的信件，不做已讀
        if (this.checkMailCanOpen(element) && (element.canReceive || element.itemType > 1)) {
          const isExpire = this.$moment()
            .subtract(this.serverTimestamp, 'milliseconds')
            .isAfter(element.expire)
          if (!element.isRead && (element.itemType === 0 || isExpire)) {
            const res = await this.readMail(element.mailId)
            // 過期的信件與伺服器回傳的資訊一併做已讀
            if ((!element.isRead && isExpire) || (res.isRead && !isExpire)) {
              this.delReadBtnStatus = true
              const mailListIndex = this.findIndexByMailIdAll(this.getSingleMail.mailId)
              this.$store.commit('mail/READ_SINGLE_MAIL', mailListIndex)
            }
            // 經討論後，已逾時或無附件的信件不再顯示伺服器所傳的錯誤訊息 2023/03/22 by cash
          }
        }
        //後續動作會去更改到getSingleMail的內容，所以要先deep copy一份 以免使用非常規操作去改到vuex的內容
        this.getSingleMail = { ...element }
      }
      // 重新計算未讀信件數量
      this.updateNotyCount()
      // 重置信件滾動位置
      this.$nextTick(() => {
        if (this.$refs.mailContent) {
          this.$refs.mailContent.scrollTop = 0
        }
      })

      // 延遲檢查容器和滑塊的尺寸，如滑塊尺寸大於容器尺寸，則代表顯示滾動條，改變hasScrollbar
      setTimeout(() => {
        const swiperContainer = document.querySelector('.swiper-area')
        if (swiperContainer) {
          let slidesWidth = 0
          const slides = swiperContainer.querySelectorAll('.swiper-slide')
          slides.forEach((slide) => {
            slidesWidth += slide.offsetWidth + 8 // 8 是 spaceBetween
          })

          const containerWidth = swiperContainer.offsetWidth
          this.hasScrollbar = slidesWidth > containerWidth
        }
      }, 100) // 給足夠的時間讓內容渲染完成
    },
    async doDeleteRead() {
      this.confirmDeletionStatus = false
      const newMailData = this.categorizedList.filter(
        (val) => val.mailId !== this.getSingleMail.mailId
      )
      this.$wsClient.send(this.$wsPacketFactory.deleteMail(this.getSingleMail.mailId))
      const res = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
        return data.isFeature(this.$xinConfig.FEATURE.MAIL.TYPE.DELETE)
      })
      if (res.mailId) {
        this.selection = null
        this.getSingleMail.content = this.$t('please_select_letter')
        this.$notify.success(this.$t('delete') + ' ' + this.$t('letter') + ' ' + this.$t('success'))
        this.$store.commit('mail/SET_ALL_MAIL', newMailData)
        // 檢查按鈕狀態
        this.checkTopBtn(newMailData)
        //手機版刪除後回到信件列表
        if (this.$vuetify.breakpoint.xsOnly) {
          this.backToMailList()
        }
      } else {
        this.$notify.error(res.message)
      }
    },
    async doDeleteReadAll() {
      // 更新 mailList，保留未讀信件
      const newMailData = this.categorizedList.filter((mail) => {
        // 1.篩選目前分類未讀的信件，其餘分類信件保留
        // 2.分類在「全部」時，篩選全部未讀的信件
        if (mail.categoryType === this.mailCategoryType || this.mailCategoryType === 0) {
          return !mail.isRead || mail.guildInvitationLetter
        }
        return true
      })
      const willRemoveData = this.showMailDataArrTmp.filter(
        (val) => val.isRead === true && !val.guildInvitationLetter
      )
      const removeIds = willRemoveData.map((mail) => mail.mailId)

      let count = 0
      this.isDeleteReadAllLoading = true
      for (const mailId of removeIds) {
        this.$wsClient.send(this.$wsPacketFactory.deleteMail(mailId))
        const res = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
          return data.isFeature(this.$xinConfig.FEATURE.MAIL.TYPE.DELETE)
        })
        if (res.mailId) count++
      }
      this.isDeleteReadAllLoading = false
      if (count === removeIds.length) {
        // 檢查目前選中的信件是否已被刪除
        if (this.selection && willRemoveData.some((mail) => mail.mailId === this.selection)) {
          this.selection = null
          this.getSingleMail.content = this.$t('please_select_letter')
        }

        this.delReadBtnStatus = false
        this.confirmDeletionAllStatus = false

        this.$store.commit('mail/SET_ALL_MAIL', newMailData)
        this.$notify.success(this.$t('allReadDeleted'))
      }
    },
    transformPrice(value) {
      // 先進行千分位逗號處理
      const formattedValue = value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')

      // 再進行 K/M 轉換
      if (value < 1000) {
        return formattedValue
      } else if (value < 1000000) {
        return Math.floor(value / 1000) + 'K'
      } else {
        return Math.floor(value / 1000000) + 'M'
      }
    },
    async doReceive() {
      const index = this.findIndexByMailId(this.getSingleMail.mailId)
      if (index !== -1) {
        const element = this.showMailDataArrTmp[index]
        // 尚未已讀且為星幣類型的信件，才做領取附件(已讀)
        if (!element.isRead && element.itemType === 1 && element.canReceive) {
          const res = await this.readMail(element.mailId)
          if (res.isRead) {
            this.$notify.success(this.$t('mail_collect_success'))
            if (element.itemType === 1) {
              if (!element.from.startsWith('@lang')) {
                const sender = await this.getGuildAnalyticsData(element.from)
                const receiver = await this.getGuildAnalyticsData(this.userName)
                this.sendMailAnalytics(sender, receiver, element.count, 9, 1)
              }
            }
          } else {
            this.$notify.error(this.$t('mail_collect_fail'))
          }
          //已讀信件
          const mailListIndex = this.findIndexByMailIdAll(this.getSingleMail.mailId)
          this.$store.commit('mail/READ_SINGLE_MAIL', mailListIndex)
          // 檢查按鈕狀態
          this.checkTopBtn(this.showMailDataArrTmp)
          // 改變按鈕的狀態
          this.delReadBtnStatus = true
          this.getSingleMail.isRead = true
          //重新計算未讀信件數量
          this.updateNotyCount()
          // 更新信件顯示的到期日
          this.getSingleMail = { ...element }
        }
      }
    },
    async doReceiveAll() {
      const readIds = []
      for (let index = 0; index < this.showMailDataArrTmp.length; index++) {
        const item = this.showMailDataArrTmp[index]
        // 符合以下任一條件的信件，加入readIds陣列(將標記為已讀)
        // 1.未讀，信件 itemType > 0 且可領取的信件
        // 2.未讀，信件 itemType = 0 且可已讀的信件
        if (
          !item.isRead &&
          ((item.itemType > 0 && item.canReceive) ||
            (item.itemType === 0 && this.checkMailCanOpen(item)))
        ) {
          readIds.push(item.mailId)
        }
      }

      if (readIds.length > 0) {
        this.isReceiveAllLoading = true
        let resMail = []
        // 每次發送20筆讀取請求
        for (let i = 0; i < readIds.length; i += 20) {
          const mailSlice = readIds.slice(i, i + 20)
          this.$wsClient.send(this.$wsPacketFactory.readAllMail(mailSlice))
          const res = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
            return data.isFeature(this.$xinConfig.FEATURE.MAIL.TYPE.READ)
          })
          resMail.push(...res.mailIds)
          // 加入延遲避免請求過快
          await new Promise((resolve) => setTimeout(resolve, 100))
        }
        this.isReceiveAllLoading = false
        let hasReadMail = resMail.length > 0
        // 更新已讀郵件
        const updateMails = async (ids) => {
          for (let i = 0; i < ids.length; i++) {
            const id = ids[i]
            const index = this.findIndexByMailId(id)
            const mailListIndex = this.findIndexByMailIdAll(id)
            if (mailListIndex !== -1) this.$store.commit('mail/READ_SINGLE_MAIL', mailListIndex)
            if (id === this.getSingleMail.mailId) {
              this.getSingleMail.isRead = true
            }
            const element = this.showMailDataArrTmp[index]
            if (element.itemType === 1) {
              if (!element.from.startsWith('@lang')) {
                const sender = await this.getGuildAnalyticsData(element.from)
                const receiver = await this.getGuildAnalyticsData(this.userName)
                this.sendMailAnalytics(sender, receiver, element.count, 9, 1)
              }
            }
          }
        }
        if (hasReadMail) updateMails(resMail)
        if (hasReadMail) {
          if (hasReadMail) this.$notify.success(this.$t('receive_all') + ' ' + this.$t('success'))
          // 更新郵件列表和通知計數
          this.$store.commit(
            'mail/SET_NOTY_COUNT',
            this.categorizedList.filter((val) => !val.isRead).length
          )
          // 更新按鈕狀態
          this.allReceiveBtnStatus = false
          this.confirmReadAllStatus = false
          this.delReadBtnStatus = true
        }
      }
      // 只在有選擇信件的情況下，才更新信件顯示的到期日
      if (this.selection) {
        const mailObj = this.showMailDataArrTmp.find((element) => element.mailId === this.selection)
        if (mailObj) {
          this.getSingleMail = { ...mailObj }
        }
      }
    },
    async readMail(mailId) {
      this.$wsClient.send(this.$wsPacketFactory.readMail(mailId))
      const res = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) =>
        data.isFeature(this.$xinConfig.FEATURE.MAIL.TYPE.READ)
      )
      return res
    },
    findIndexByMailId(mailId) {
      return this.showMailDataArrTmp.findIndex((element) => element.mailId === mailId)
    },
    findIndexByMailIdAll(mailId) {
      return this.categorizedList.findIndex((element) => element.mailId === mailId)
    },
    updateNotyCount() {
      this.$store.commit(
        'mail/SET_NOTY_COUNT',
        this.categorizedList.filter((val) => val.isRead === false).length
      )
    },
    doTimeFormat(time, isRead) {
      const timeData = {}
      const expiredState = this.$moment()
        .subtract(this.serverTimestamp, 'milliseconds')
        .isAfter(time)
      if (expiredState) {
        timeData.style = 'error--text'
        timeData.content = this.$t('expired')
      } else {
        const now = this.$moment().subtract(this.serverTimestamp, 'milliseconds')
        const expireTime = this.$moment(time)
        const remainDays = expireTime.diff(now, 'days', true)
        const remainHours = expireTime.diff(now, 'hours')

        timeData.style = remainDays >= 1 ? 'warning--text' : 'error--text'
        timeData.content =
          remainDays >= 1
            ? `${Math.floor(remainDays)} ${this.$t('single_day')} ${this.$t('time_left')}`
            : remainHours > 0
            ? `${remainHours}${this.$t('hours')} ${this.$t('time_left')}`
            : this.$t('one_hour_remaining')
      }

      if (isRead) timeData.style = ''

      return timeData
    },
    doRedirectPage(url) {
      if (url !== null && url.length != 0) {
        this.$lineOpenWindow.open(url, '_blank')
      }
    },
    sendNewMail() {
      this.sendMode = 'new'
      this.doShowMailSendDialog()
    },
    replyNewMail() {
      this.sendMode = 'reply'
      this.doShowMailSendDialog()
    },
    //檢查是否為好友
    checkIsFriend(name) {
      return this.friendList.some((item) => item.username === name)
    },
    checkMailCanOpen(mail) {
      const title = ['【回饋卡通知】']
      try {
        // 當信件標題等於 "【回饋卡通知】" 時
        if (title.some((item) => mail.title === item)) {
          return false
        }
        // 當 items 中有 id = 20 的項目時
        if (mail.items?.some((item) => item.id === 20)) {
          return false
        }
        return mail.canReceive
      } catch (e) {
        console.log(e)
        return false
      }
    },
    doShowMailSendDialog() {
      if (!this.hasGuild) {
        this.$store.commit('easyDialog/SET_TITLE', this.$t('reminder'))
        this.$store.commit('easyDialog/SET_MESSAGE', this.$t('mail_send_noty6'))
        this.$nuxt.$emit('root:showNotyDialogStatus', true)
      } else {
        this.showMailSendDialogStatus = true
      }
    },
    goDownload() {
      this.closeDialog()
      if (this.$device.isAndroid || this.$device.isIos || this.$device.isMacOS) {
        const url = 'https://www.xin-stars.com/goStore'
        this.$lineOpenWindow.open(url)
      } else {
        this.$router.push({ path: this.localePath('/downloads'), hash: '#pc' })
      }
    },
    getCreateText(date) {
      const timeFormat = this.$UIConfig.timeStamp.format
      const time = this.$moment(date)
        .utcOffset(convertTime.getOffset(this.$UIConfig.timeStamp.timezone))
        .format(timeFormat)
      return (
        this.$t('send_mail_time') +
        ': ' +
        time.format(`(${convertTime.getGMTOffset(this.$UIConfig.timeStamp.timezone)}),`)
      )
    },
    goMailListTop() {
      if (this.$refs.mailList) {
        document.getElementById('mail-list').scrollTop = 0
      }
    },
    setOrientation() {
      this.$store.dispatch('deviceManagement/setOrientation')
    },
    formatDate(date) {
      const timeFormat = this.$UIConfig.timeStamp.format
      const time = this.$moment(date)
        .utcOffset(convertTime.getOffset(this.$UIConfig.timeStamp.timezone))
        .format(timeFormat)
      return time.format(`(${convertTime.getGMTOffset(this.$UIConfig.timeStamp.timezone)}),`)
    },
    showConfirmGuildInviteDialog(accept) {
      this.isAccept = accept
      this.$nextTick(() => {
        this.showConfirmGuildInviteDialogStatus = true
      })
    },
    categoryMap(mailFrom) {
      const official = '@lang_200'
      const support = this.$t('xincity')
      if (!mailFrom) return
      if (mailFrom === official) {
        return 1 // 官方
      } else if (mailFrom === support) {
        return 2 // 客服
      } else {
        return 3 // 其他視為為玩家
      }
    },
    selectedMailCategoryEvent() {
      // 當前開啟的信件類別
      const categoryType = this.categoryMap(this.getSingleMail.from)

      //符合以下條件之一：捲動到該信件
      //1.已選擇信件，切換到分類為「全部」
      //2.已選擇信件，切換類別與當前選擇的信件類別一致
      if (
        this.selection !== null &&
        (this.mailCategoryType === 0 || this.mailCategoryType === categoryType)
      ) {
        this.scrollToSelectedMail()
      } else {
        // 其他狀況：清除選擇，且卷軸置頂
        this.selection = null
        this.getSingleMail.content = this.$t('please_select_letter')
        this.mobile.showMailContentstatus = false
        this.goMailListTop()
      }
    },
    scrollToSelectedMail() {
      //使目標元素捲至 mailList 中間
      this.$nextTick(() => {
        const container = this.$refs.mailList.$el // mailList的DOM元素
        const target = this.mailItemRefs[this.selection].$el // selection的DOM元素
        const containerRect = container.getBoundingClientRect()
        const targetRect = target.getBoundingClientRect()
        const offset =
          targetRect.top -
          containerRect.top +
          container.scrollTop -
          container.clientHeight / 2 +
          target.clientHeight / 2
        container.scrollTo({
          top: offset,
          behavior: 'smooth'
        })
      })
    },
    getReceiveableItems() {
      const items = []
      // 處理 mail 物件的函數
      const getMailObject = (info) => {
        const defaultMail = {
          name: '',
          img: '',
          imgSrc: this.defaultImgWebp,
          canReceive: false
        }
        return info?.mail ? { ...info.mail } : defaultMail
      }

      // 收集 itemType 內容
      if (
        this.getSingleMail.itemType > 0 &&
        this.getSingleMail.count &&
        this.getSingleMail.count > 0
      ) {
        const itemTypeInfo = this.itemInfo.find(
          (item) => item.id === this.getSingleMail.itemType.toString()
        )
        items.push({
          id: this.getSingleMail.itemType,
          count: this.getSingleMail.count,
          isItemType: true,
          uniqueKey: `${this.getSingleMail.itemType}_${this.getSingleMail.mailId}_itemType`, // 避免出現同樣的索引值進v-for迴圈
          ...itemTypeInfo, // 展開 itemTypeInfo 的屬性
          mail: getMailObject(itemTypeInfo) // 使用新的 mail 物件
        })
      }

      // 收集 items 陣列內的內容
      if (this.getSingleMail.items?.length > 0) {
        this.getSingleMail.items.forEach((item, index) => {
          if (item.count && item.count > 0) {
            const itemInfo = this.itemInfo.find((info) => info.id === item.id.toString())
            items.push({
              id: item.id,
              count: item.count,
              isItemType: false,
              uniqueKey: `${item.id}_${this.getSingleMail.mailId}_${index}`, // 避免出現同樣的索引值進v-for迴圈
              ...itemInfo, // 展開 itemTypeInfo 的屬性
              mail: getMailObject(itemInfo) // 使用新的 mail 物件
            })
          }
        })
      }

      items.sort((a, b) => {
        const aIndex = this.displayOrder.indexOf(a.id)
        const bIndex = this.displayOrder.indexOf(b.id)

        if (aIndex === bIndex) {
          // 相同 ID 時，itemType 的項目優先顯示
          if (a.isItemType && !b.isItemType) return -1
          if (!a.isItemType && b.isItemType) return 1
          // 如果都來自同一個來源，則按照原始順序
          return a.sortIndex - b.sortIndex
        }

        return aIndex - bIndex
      })

      this.receiveableItems = items
    },
    async getItemInfo() {
      try {
        const resInfo = await this.$axios.get(
          process.env.IMAGE_URL +
            `/inventory_items/item_provider/${loadConfig.client_id}/item_provider.json?` +
            Math.random()
        )
        this.itemInfo = resInfo.data
      } catch (error) {
        console.warn('Failed to get item info：', error)
        return
      }

      // 篩選出 canReceive 為 true 的項目，並新增 imgSrc
      this.itemInfo = this.itemInfo
        .filter((item) => item.mail.canReceive)
        .map((item) => ({
          ...item,
          mail: {
            ...item.mail,
            imgSrc: item.mail.img
              ? `${process.env.IMAGE_URL}/inventory_items/item_img/${loadConfig.client_id}/${
                  item.mail.img
                }?t=${Date.now()}`
              : this.defaultImgWebp
          }
        }))

      // 定義顯示順序：按照 JSON 排序控制，只取 id
      this.displayOrder = this.itemInfo.map((item) => item.id)
    },
    errorImgHandler(errorItem) {
      const originImgSrc = this.itemInfo.find((item) => item.id === errorItem.id).mail.imgSrc

      // 情況 1：載入預設圖（webp）失敗，改為預設圖（png）
      if (errorItem.mail.imgSrc === this.defaultImgWebp) {
        this.$set(errorItem.mail, 'imgSrc', this.defaultImgPng)
        return
      }

      // 情況 2：載入原始圖片（webp，非預設圖）失敗，改為 png
      if (errorItem.mail.imgSrc === originImgSrc) {
        // 嘗試將圖片格式從 .webp 替換為 .png
        this.$set(errorItem.mail, 'imgSrc', errorItem.mail.imgSrc.replace('.webp', '.png'))
        return
      }

      // 情況 3：非預設圖（webp 及 png），改為預設圖（webp）
      if (
        errorItem.mail.imgSrc !== this.defaultImgWebp &&
        errorItem.mail.imgSrc !== this.defaultImgPng
      ) {
        this.$set(errorItem.mail, 'imgSrc', this.defaultImgWebp)
        return
      }
    }
  }
}
