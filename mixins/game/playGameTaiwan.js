/* eslint-disable no-undef */
const NUXT_ENV = process.env.NUXT_ENV
const loadConfig = require(`~/station/${process.env.STATION}/${NUXT_ENV}.js`).default
const STATION = process.env.STATION
// 因為渲染載入順序問題
// isDisconnect Cannot read properties of undefined (reading 'WEB_GAME_SERVICE') 這個問題
// 所以要在這邊獨立引入該檔案
const xinProtocol =
  require(`@/plugins/xin-socket/protocolConfig/${STATION}/protocol.config`).default

export default {
  data() {
    return {
      //配合星城伺服器的遊戲ID 為了避免跟其他伺服器的遊戲ID重複
      serverConstant: loadConfig.game.serverConstant,
      //若遊戲無經驗，於提示窗確認前，暫存遊戲資訊
      clickedNoExpGameInfo: {
        platformId: '',
        gameId: '',
        isDemo: false
      },
      clickedhasRobotGameInfo: {
        platformId: '',
        gameId: '',
        isDemo: false
      },
      openGameHandler: {},
      localGameLink: '',
      showDisconnectedDialog: false,
      showNotyNoExpGainNotyDialogStatus: {
        show: false,
        onConfirmNotify: () => {},
        onCancelNotify: () => {}
      },
      showNotyBothRobotExpNotyDialogStatus: {
        show: false,
        onConfirmNotify: () => {},
        onCancelNotify: () => {}
      },
      showNotyHasRobotNotyDialogStatus: {
        show: false,
        onConfirmNotify: () => {},
        onCancelNotify: () => {}
      }
    }
  },
  computed: {
    singleGameHallInfo({ $store }) {
      return $store.getters['gameHall/singleGameHallInfo']
    },
    showGameModeStatus({ $store }) {
      return $store.getters['menu/showGameModeStatus']
    },
    vipLevel({ $store }) {
      return $store.getters['role/vipLevel']
    },
    level({ $store }) {
      return $store.getters['role/level']
    },
    // "青銅"玩家特別限制，game.vipLevel為1時限制LV10(含)以上才可遊玩，為99表示LV1即可遊玩
    isVipLevelLimitSingle() {
      return (
        this.vipLevel !== 0 &&
        ((this.singleGameHallInfo.vipLevel === 1 && this.level < 10) ||
          (this.singleGameHallInfo.vipLevel !== 99 &&
            this.vipLevel < this.singleGameHallInfo.vipLevel))
      )
    },
    hasExpSingle() {
      return this.singleGameHallInfo.hasExp !== 0
    },
    gameLink({ $store }) {
      return $store.getters['gameHall/gameLink']
    },
    hasRobotSingle() {
      return this.singleGameHallInfo.hasRobot !== 0
    },
    isQCStation() {
      return STATION === 'qc_overseas' || STATION === 'qc_domestic'
    },
    isQCOverseasStation() {
      return STATION === 'qc_overseas'
    },
    isDisconnect() {
      return this.$store.getters['xinProtocol/services'][xinProtocol.WEB_GAME_SERVICE.ID].connected
    }
  },
  watch: {
    isDisconnect: {
      handler(val) {
        if (!val && this.$route.params.mode === 'play') {
          // 確定 LOGOUT，需要重置 id
          this.$store.commit('xinProtocol/SET_SERVICE_IDS', {
            serviceId: this.$xinConfig.WEB_GAME_SERVICE.ID,
            enable: false,
            connected: false,
            sendId: 0,
            receiveId: 0
          })
          this.showDisconnectedDialog = true
          this.$notify.error(this.$t('game_disconnect_reenter'))
        }
      }
    }
  },
  methods: {
    async loginGameService() {
      await this.$wsClient.send(
        this.$wsPacketFactory.getServiceJoin(this.$xinConfig.WEB_GAME_SERVICE.ID)
      )
      this.$store.commit('xinProtocol/SET_SERVICE_IDS', {
        serviceId: this.$xinConfig.WEB_GAME_SERVICE.ID,
        enable: true,
        connected: true,
        sendId: 0,
        receiveId: 0
      })
    },
    getGameBody(xinkey, platformId, gameCategoryId, gameId) {
      const userAgent = this.$device.userAgent
      const isSafari = userAgent.indexOf('Safari') !== -1 && userAgent.indexOf('Version') !== -1
      const isIphone = userAgent.indexOf('iPhone') !== -1 && userAgent.indexOf('Version') !== -1
      const isIpadPro = isSafari && !isIphone && 'ontouchend' in document // 判斷是否為ipadPro
      let gameBody = {
        xinkey,
        gameCategoryId,
        gameName: this.singleGameHallInfo.name,
        canRedirect: this.singleGameHallInfo.canRedirect,
        platformId,
        gameId,
        mobile: isIpadPro ? true : this.$device.isMobile,
        lang: this.$i18n.locale,
        ip: this.$store.getters['deviceManagement/getIP'],
        backUrl: '',
        userAgent: this.$ua._ua,
        thumbUrl: this.$store.getters['role/thumbUrl'],
        vipLevel: this.vipLevel
      }
      if (this.$route.params.qcStation && Object.keys(this.$route.params.qcStation).length > 0) {
        gameBody.stationName = this.$route.params.qcStation
      }

      gameBody = { ...gameBody, userLevel: this.level }
      return gameBody
    },
    setupGameLink(body, gameLink) {
      if (Number.isInteger(gameLink)) {
        this.$notify.backendError(gameLink)
        const gameDisableList = [50003, 60001, 60032, 60037]
        if (gameDisableList.includes(gameLink)) {
          this.$store.dispatch('gameHall/updateListWhenDisabled', {
            gameCategoryId: body.gameCategoryId,
            gameId: body.gameId
          })
          // 關閉遊戲rtp整合視窗
          if (this.showLocalGameIntroDialogStatus) {
            this.showLocalGameIntroDialogStatus = false
          }
        } else {
          this.$store.commit('gameHall/SET_GAMELIST_MAINTAIN_STATUS', {
            gameId: body.gameId,
            status: true
          })
          this.$store.commit('gameHall/SET_ALL_GAME_LIST_MAINTAIN_STATUS', {
            gameId: body.gameId,
            status: true
          })
        }
      }
      return !Number.isInteger(gameLink)
    },
    async initGameLink(xinkey, isDemo, body, headerData) {
      const gameSetting = isDemo ? 'fetchGameDemo' : 'fetchGameLink'
      const gameHall = `gameHall/${gameSetting}`
      await this.$store.dispatch(gameHall, { headerData, body })
      this.xinkey = isDemo ? this.xinkey : xinkey
      return this.gameLink
    },
    async getGameXinKey(gameId) {
      let userKey = { key: '', userAlias: '' }
      try {
        // 取得 user key
        if (this.isQCOverseasStation) {
          let stationConstant = this.$store.getters[`${STATION}/station/stationConstant`]
          this.$wsClient.send(
            this.$wsPacketFactory.getUserKey(stationConstant.serverConstant + gameId)
          )
        } else {
          this.$wsClient.send(this.$wsPacketFactory.getUserKey(this.serverConstant + gameId))
        }
        userKey = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) =>
          data.isFeature(this.$xinConfig.FEATURE.GAME.TYPE.USER_KEY)
        )
      } catch (error) {
        this.$notify.error(this.$t('get_xinkey_failed'))
      }
      return userKey
    },
    async prepareHandler(platformId, gameCategoryId, gameId, isDemo) {
      let xinkey = { key: '', userAlias: '' }
      let isSuccess = {}
      if (this.isLogin && !isDemo) {
        const serviceList = this.$store.getters['xinProtocol/services']
        const gameServiceEnable = serviceList[this.$xinConfig.WEB_GAME_SERVICE.ID].enable
        const gameServiceConnected = serviceList[this.$xinConfig.WEB_GAME_SERVICE.ID].connected
        if (!gameServiceEnable || !gameServiceConnected) await this.loginGameService()
        xinkey = await this.getGameXinKey(gameId)
        if (xinkey.key === '' || xinkey.userAlias === '') return { isSuccess: false }
      }
      const body = this.getGameBody(
        xinkey.key,
        platformId,
        gameCategoryId,
        gameId,
        xinkey.userAlias
      )
      const headerData = {
        username: this.$store.getters['role/userName']
          ? this.$store.getters['role/userName']
          : null,
        alias: xinkey.userAlias ? xinkey.userAlias : null
      }
      const gameLink = await this.initGameLink(xinkey.key, isDemo, body, headerData)
      isSuccess = this.setupGameLink(body, gameLink)
      return { isSuccess, openGame: { gameId, isDemo, body, xinkey: xinkey.key, headerData } }
    },
    async openGame({ headerData, xinkey, isDemo, body, gameId }) {
      if (this.showGameModeStatus && !isDemo) {
        // 若已在遊戲中(正式遊玩)，無須重新打開遊戲 router 跳轉
        this.localGameLink = this.gameLink
      } else {
        const mode = isDemo ? 'demo' : 'play'

        let startGamePath = ''
        if (this.isQCStation) {
          const stationName = this.$store.getters[`${STATION}/station/stationName`]
          startGamePath = `/${stationName}/${mode}/${gameId}/`
          if (!isDemo) {
            startGamePath = `/${stationName}/${mode}/${gameId}/`
          }
        } else {
          startGamePath = `/${mode}/${gameId}`
          if (!isDemo) {
            startGamePath = `/${mode}/${gameId}`
          }
        }
        this.$router.push(this.localePath(startGamePath))
      }

      if (this.isLogin) this.$store.dispatch('gameHall/updatePlayedGameList', gameId) //更新近期遊玩列表
      this.$store.commit('gameHall/SET_XINKEY', xinkey)

      this.gameClickAnalytics({
        headerData,
        gameId,
        isDemo,
        body,
        xinkey,
        ...(this.isQCStation && {
          stationName: this.$store.getters[`${STATION}/station/stationName`]
        })
      })
      if (isDemo) this.freePlayAnalytics()
      else this.playAnalytics()
    },

    // 因 props 傳入的參數，若作為函式的參數帶入，則不具備響應性，故改用 this.game.xxx
    async payGameClickHandler() {
      const localStorageLiveConfirm = this.$localStorage.get('localStorageLiveConfirm').expired
      if (!this.hasExpSingle) {
        this.clickedNoExpGameInfo.platformId = this.singleGameHallInfo.platformId
        this.clickedNoExpGameInfo.gameId = this.singleGameHallInfo.id
        this.clickedNoExpGameInfo.isDemo = false
      }
      if (this.hasRobotSingle) {
        this.clickedhasRobotGameInfo.platformId = this.singleGameHallInfo.platformId
        this.clickedhasRobotGameInfo.gameId = this.singleGameHallInfo.id
        this.clickedhasRobotGameInfo.isDemo = false
      }
      const memberLevel = this.vipLevel
      const canOpenGame = memberLevel > 0 && !this.isVipLevelLimitSingle

      if (canOpenGame) {
        const isShowDialog =
          this.$UIConfig.gameIframe.showNoExpDialog &&
          (localStorageLiveConfirm === undefined ||
            this.$moment(localStorageLiveConfirm).isBefore(this.$moment(), 'day'))
        if (isShowDialog && this.singleGameHallInfo.categoryType === 200) {
          // 先彈出免責聲明，但不帶 URL
          this.$nuxt.$emit('root:showGameCardConfirmDialogStatus', {
            show: true,
            hasExp: this.hasExpSingle,
            hasRobot: this.hasRobotSingle,
            onConfirmNotify: async () => {
              // 點擊時才重新取得最新 URL
              const newHandler = await this.prepareHandler(
                this.singleGameHallInfo.platformId,
                this.singleGameHallInfo.categoryType,
                this.singleGameHallInfo.id,
                false
              )

              if (newHandler.isSuccess) {
                this.openGame(newHandler.openGame)
              } else if (this.showGameModeStatus) {
                // 若在遊戲頁，因任何狀況無法開啟遊戲，轉跳回首頁
                this.$router.push(this.localePath('/'))
              }
            },
            onCancelNotify: this.showGameModeStatus
              ? () => this.$router.push(this.localePath('/'))
              : () => {}
          })
        } else if (!this.hasExpSingle && this.hasRobotSingle) {
          // 如果兩個都有，顯示both提示
          this.showNotyBothRobotExpNotyDialogStatus = {
            show: true,
            onConfirmNotify: this.playNoExpGame,
            onCancelNotify: this.showGameModeStatus
              ? () => this.$router.push(this.localePath('/'))
              : () => {}
          }
        } else if (!this.hasExpSingle) {
          // 如果無經驗值，顯示noExp提示
          this.showNotyNoExpGainNotyDialogStatus = {
            show: true,
            onConfirmNotify: this.playNoExpGame,
            onCancelNotify: this.showGameModeStatus
              ? () => this.$router.push(this.localePath('/'))
              : () => {}
          }
        } else if (this.hasRobotSingle) {
          // 如果有機器人，顯示hasRobot提示
          this.showNotyHasRobotNotyDialogStatus = {
            show: true,
            onConfirmNotify: this.playNoExpGame,
            onCancelNotify: this.showGameModeStatus
              ? () => this.$router.push(this.localePath('/'))
              : () => {}
          }
        } else {
          this.openGameHandler = await this.prepareHandler(
            this.singleGameHallInfo.platformId,
            this.singleGameHallInfo.categoryType,
            this.singleGameHallInfo.id,
            false
          )

          if (!this.openGameHandler.isSuccess) {
            // 若在遊戲頁，因任何狀況無法開啟遊戲，轉跳回首頁
            if (this.showGameModeStatus) this.$router.push(this.localePath('/'))
            return
          }

          this.openGame(this.openGameHandler.openGame)
        }
      } else if (this.showGameModeStatus || this.isVipLevelLimitSingle) {
        if (process.client) this.$notify.error(this.$t('game_restriction_notice'))
        this.$router.push(this.localePath('/'))
      } else {
        this.showNotyNotRealMemberDialogStatus = true
      }
    },
    async freeGameClickHandler() {
      this.openGameHandler = await this.prepareHandler(
        this.singleGameHallInfo.platformId,
        this.singleGameHallInfo.categoryType,
        this.singleGameHallInfo.id,
        true
      )
      if (this.openGameHandler.isSuccess) {
        this.openGame(this.openGameHandler.openGame)
      } else if (this.showGameModeStatus) {
        // 若在遊戲頁，因任何狀況無法開啟遊戲，轉跳回首頁
        this.$router.push(this.localePath('/'))
      }
    },
    async playNoExpGame() {
      this.openGameHandler = await this.prepareHandler(
        this.singleGameHallInfo.platformId,
        this.singleGameHallInfo.categoryType,
        this.singleGameHallInfo.id,
        false
      )
      if (this.openGameHandler.isSuccess) {
        this.openGame(this.openGameHandler.openGame)
      } else if (this.showGameModeStatus) {
        // 若在遊戲頁，因任何狀況無法開啟遊戲，轉跳回首頁
        this.$router.push(this.localePath('/'))
      }
    },
    closeGameMode() {
      // 當ERROR PAGE 觸發時，生命週期將不會走原本流程，所以把想刪除的放進來，避免殘留
      this.$store.commit('menu/SET_SHOWGAMEMODESTATUS', false)
      this.$store.commit('gameHall/SET_GAME_LINK', '')
      this.$store.commit('gameHall/SET_SINGLEGAMEHALLINFO', {})
      window.removeEventListener('resize', this.renderResize, false)
      document.documentElement.style.removeProperty('overflow-y')
    },
    async startGameWithClick(clickMode) {
      // 主要是怕畫面點到，所以加上遊戲鎖
      this.$store.commit('gameHall/SET_OPENGAMELOCK', true)
      this.$store.commit('gameHall/SET_SINGLEGAMEHALLINFO', this.game)
      // 因為以下警告訊息，$router.back() 會導致無法正常發揮效果，故先暫存路徑機制
      // An iframe which has both allow - scripts and allow - same - origin for its sandbox attribute can escape its sandboxing.
      // 放這裡是因為只有點擊才會有上一頁記錄，如果放在 startGameHandle 會導致URL 入口情境是自己，而要點擊兩次才有辦法返回
      this.$store.commit('SET_CUSTOM_ROUTER_PATH', this.$route.fullPath)

      try {
        await this.startGameHandle(clickMode)
      } catch (error) {
        console.error('[playGame] 啟動遊戲過程中發生錯誤:', error)

        // 處理遊戲啟動失敗，檢查是否需要設定動態維護
        if (this.handleGameFailure && typeof this.handleGameFailure === 'function') {
          this.handleGameFailure()
        }
      } finally {
        // 解除遊戲鎖
        this.$store.commit('gameHall/SET_OPENGAMELOCK', false)
      }
    },
    async startGameHandle(clickMode) {
      let gameId = this.$route.params.gameId || this.singleGameHallInfo.id || '0'
      let mode = clickMode || this.$route.params.mode || 'demo'

      if (
        Object.keys(this.singleGameHallInfo).length === 0 &&
        this.singleGameHallInfo.constructor === Object
      ) {
        const headerData = {
          username: this.$store.getters['role/userName']
            ? this.$store.getters['role/userName']
            : null,
          alias: null
        }
        const gameTmp = await this.$clientApi.game.gameList(headerData, [gameId], this.$i18n.locale)
        if (gameTmp.count > 0) {
          const game = gameTmp.list[0]
          this.$store.commit('gameHall/SET_SINGLEGAMEHALLINFO', game)
          gameId = this.singleGameHallInfo.id
        } else if (gameTmp.count === 0) {
          // 遊戲不存在
          if (process.client) {
            this.$notify.error(this.$t('not_found_game'))
          }
          this.checkYears18Noty()
          this.$router.push(this.localePath('/'))
          return
        } else if (gameTmp.errorCode) {
          // API 回傳錯誤
          if (process.client) {
            this.$notify.backendError(gameTmp.errorCode)
          }
          this.checkYears18Noty()
          this.$router.push(this.localePath('/'))
          return
        }
      }
      // 移除維護檢查，讓 API 錯誤處理這些情況
      if (this.isLogin && mode === 'play' && gameId) {
        await this.payGameClickHandler()
      } else if (!this.isLogin && !this.singleGameHallInfo.hasDemo) {
        const loginFn = () => this.$nuxt.$emit('root:showLoginDialogStatus', { show: true })
        if (!this.showGameModeStatus) {
          this.$notify.error(this.$t('plz_login_first_to_play_game'))
          loginFn()
        } else if (mode === 'demo') {
          // 不提供試玩情境(真人遊戲)
          this.$notify.error(this.$t('plz_login_first_to_play_game'))
          this.checkYears18Noty(loginFn)
          this.$router.push(this.localePath('/'))
        } else {
          this.checkYears18Noty(() => this.$set(this, 'showRedirectHomeDialog', true))
        }
      } else if (!this.isLogin && mode === 'play' && gameId && process.client) {
        const fn = () => this.$set(this, 'showRedirectDemoModeDialog', true)
        this.checkYears18Noty(fn)
      } else if (mode === 'demo' && gameId) {
        if (this.showGameModeStatus)
          this.checkYears18Noty(() => this.$set(this, 'gameDemoModeDialog', true))
        await this.freeGameClickHandler()
      }
    },
    showNotyDialog(title, message) {
      this.$store.dispatch('easyDialog/setDialog', {
        title: title,
        message: message
      })
      this.$nuxt.$emit('root:showNotyDialogStatus', true)
    },
    checkYears18Noty(fn) {
      // 獲取 18+ 提示的 localStorage
      const { enable = 0 } = this.$localStorage.get('years18Noty') || {}
      // 獲取已顯示過的提示清單
      const shownNoty = this.$localStorage.get('shownNoty') || []
      const shownYears18Noty = shownNoty.includes('years18Noty')
      // 是否顯示 18+ 提示
      if (enable === 0 && !shownYears18Noty) {
        this.$nuxt.$emit('root:showYears18NotyStatus', {
          show: true,
          onConfirmNotify: fn
        })
        // 若 localStorage 沒有值，則設置預設值
        if (!this.$localStorage.get('years18Noty')) {
          this.$localStorage.set('years18Noty', { enable: 0 })
        }
        shownNoty.push('years18Noty')
        this.$localStorage.set('shownNoty', shownNoty)
      } else if (fn) {
        fn()
      }
    }
  }
}
