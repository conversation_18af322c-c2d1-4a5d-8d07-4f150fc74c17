export default {
  computed: {
    facebookId({ $store }) {
      return $store.getters['role/facebookId']
    },
    accessToken({ $store }) {
      return $store.getters['role/accessToken']
    }
  },
  methods: {
    FBLogin(onResponse) {
      window.FB.getLoginStatus(async (loginStatusResponse) => {
        if (loginStatusResponse.status === 'connected') {
          try {
            onResponse(loginStatusResponse)
          } catch (error) {
            this.showNotyDialog(this.$t('reminder'), this.$t('fbLoginFailNoty1'))
          }
        } else if (
          loginStatusResponse.status === 'not_authorized' ||
          loginStatusResponse.status === 'unknown'
        ) {
          window.FB.login(
            async (response) => {
              try {
                onResponse(response)
              } catch (error) {
                this.showNotyDialog(this.$t('reminder'), this.$t('fbLoginFailNoty1'))
              }
            },
            {
              scope: 'public_profile,gaming_user_picture',
              return_scopes: false
            }
          )
        }
      })
    },
    /**
     * 獲取 Facebook 用戶頭像
     * @param {string} stickerSize - 頭像尺寸 ('small', 'normal', 'large', 'square')
     * @returns {Promise<string>} 頭像 URL
     */
    async getUserAvatar(stickerSize = 'normal') {
      try {
        // 驗證參數
        const validSizes = ['small', 'normal', 'large', 'square']
        if (!validSizes.includes(stickerSize)) {
          stickerSize = 'normal'
        }
        // 根據版本選擇不同的獲取方式
        return await this.getFacebookAvatar(stickerSize)
      } catch (error) {
        console.error('Error getting user avatar:', error)
        return this.getFallbackAvatarUrl(stickerSize)
      }
    },

    /**
     * 使用 Graph API 獲取頭像
     * @param {string} stickerSize
     * @returns {Promise<string>}
     */
    async getFacebookAvatar(stickerSize) {
      if (this.$UIConfig.facebookVersion > 10) {
        try {
          // 檢查必要參數
          if (!this.facebookId || !this.accessToken) {
            throw new Error('Missing required Facebook credentials')
          }
          const graphApiUrl = this.buildGraphApiUrl()
          const response = await this.fetchWithTimeout(graphApiUrl)
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
          }
          const data = await response.json()
          // 驗證返回的數據
          if (!data?.picture?.data?.url) {
            throw new Error('Invalid response format from Facebook API')
          }
          return data.picture.data.url
        } catch (error) {
          console.warn('Graph API request failed:', error)
          // 如果 Graph API 失敗，回退到直接 URL 方式
          return this.getFallbackAvatarUrl(stickerSize)
        }
      }
      return this.getFallbackAvatarUrl(stickerSize)
    },

    /**
     * 構建 Graph API URL
     * @returns {string}
     */
    buildGraphApiUrl() {
      const baseUrl = 'https://graph.facebook.com'
      const fields = 'id,name,email,picture'

      return `${baseUrl}/${this.facebookId}?fields=${fields}&access_token=${this.accessToken}`
    },

    /**
     * 獲取回退的頭像 URL
     * @param {string} stickerSize
     * @returns {string}
     */
    getFallbackAvatarUrl(stickerSize) {
      return `https://graph.facebook.com/${this.facebookId}/picture?type=${stickerSize}`
    },

    /**
     * 帶超時的 fetch 請求
     * @param {string} url
     * @param {number} timeout
     * @returns {Promise<Response>}
     */
    async fetchWithTimeout(url, timeout = 5000) {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), timeout)

      try {
        const response = await fetch(url, {
          signal: controller.signal
        })
        clearTimeout(timeoutId)
        return response
      } catch (error) {
        clearTimeout(timeoutId)
        throw error
      }
    }
  }
}
