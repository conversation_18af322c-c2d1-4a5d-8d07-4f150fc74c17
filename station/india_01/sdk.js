//apple login
const hostname = window.location.hostname

//google login
function handleResponse(response) {
  sdkObj.login.google.authData.idToken = response.credential
}

let sdkObj = {
  analysis: {
    facebook: {
      appId: '1876727332868248',
      cookie: true,
      xfbml: true,
      version: 'v10.0'
    },
    google: {
      config: { id: 'G-GKGQEMBMXY' },
      appName: 'win99',
      analyticsPluginName: 'ga' //看是使用ga 還是 gtm
    }
  },
  login: {
    facebook: {
      appId: '2582730525235002',
      cookie: true,
      xfbml: true,
      version: 'v10.0'
    },
    google: {
      client_id: '525846697165-rt7i1tch08a9s9a0bsta5oli3r5hkg74.apps.googleusercontent.com',
      callback: handleResponse,
      context: 'signin',
      authData: {
        idToken: undefined
      }
    },
    apple: {
      clientId: 'xyz.xincity.malaysia.service',
      scope: 'name email',
      redirectURI: 'https://' + hostname,
      usePopup: true
    }
  }
}

export default sdkObj
