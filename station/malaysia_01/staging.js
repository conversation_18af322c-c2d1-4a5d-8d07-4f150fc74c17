export default {
  client_id: '6a42c516',
  secret_key: '636979ebd7087c184895fdd8895a8793',
  env: {
    BASE_URL: 'https://joypal.xincity.xyz/',
    NUXT_ENV: process.env.NUXT_ENV || process.env.NODE_ENV || 'development',
    IMAGE_URL: 'https://img.xincity.xyz',
    CLIENT_VERSION: process.env.CLIENT_VERSION || '20230802100001',
    PLATFORM_URL: 'https://joypal.xincity.xyz/',
    AVATAR_BASE_URL: 'https://images.joyouspalace.com',
    STATION: process.env.STATION || undefined,
    GTR_WS_URL: 'wss://game-plugin.xincity.xyz'
  },
  YG_payment_info: {
    vendor_id: '957588d0-123f-4808-b19c-fc1a0e211922',
    appId: 'joyous.palace',
    url: 'https://playsdk.funone.io'
  },
  lock: {
    guild: true,
    useQpp: false,
    literalPrison: true,
    appDownload: false,
    autoCreatChar: true,
    chatroom: {
      otherStarCityOnlinePlatformsReminderDisabled: true
    },
    specialGame: {
      live: true,
      top_and_hot: true,
      fishing: false,
      featured: true,
      newest_and_recommend: true
    }
  },
  game: {
    serverConstant: 20000
  },
  customLocaleList: [
    // {
    //   code: 'zh-tw',
    //   text: '繁體中文',
    //   src: 'tw.svg'
    // },
    { code: 'en', text: 'English', src: 'en.svg' }
    //{ code: 'zh-cn', text: '简体中文', src: 'cn.svg' }
    // { code: 'vi-vn', text: 'Tiếng Việt', src:  'vietnam.svg' }
  ],
  i18n: {
    vueI18n: {
      messages: {
        en: require('../../locales/en')
        //'zh-cn': require('../../locales/zh-cn')
      },
      fallbackLocale: {
        default: ['en']
      }
    },
    locales: [
      { code: 'en', iso: 'en-US', file: 'en.js', isCatchallLocale: true }
      //{ code: 'zh-cn', iso: 'zh-CN', file: 'zh-cn.js' }
    ],
    detectBrowserLanguage: {
      useCookie: true,
      cookieKey: 'locale',
      redirectOn: 'root'
    },
    vuex: {
      syncLocale: true
    },
    lazy: true,
    langDir: 'locales/',
    seo: false,
    defaultLocale: 'en',
    baseUrl: process.env.BASE_URL
  }
}
