const STATION = process.env.STATION

// 共用的主題顏色定義
const replaceColor = {
  'title-soft': '#FFFFFF',
  'text-regular': '#FFFFFF',
  'text-medium': '#FFED30',
  'text-heavy': '#FFC700',
  'text-list': '#FFFFFF',
  'text-list-focused': '#FFED30',
  'text-dialog-header': '#E2E2E8',
  'text-dialog-header-inverse': '#09051A',
  'text-btn': '#09051A',
  'text-btn-heavy': '#09051A',
  'text-marquee-medium': '#FFED30',
  'text-marquee': '#FFFFFF',
  'text-app-awards': '#FFF85B',
  'text-app-awards-soft': '#FFFFFF',
  'text-app-awards-medium': '#FFED30',
  'text-game-square-rtp': '#FFFFFF',
  'text-game-promote-rtp': '#FFFFFF',
  'text-game-square-chip': '#1C255F',
  'text-pwa': '#09051A',
  'text-cookie': '#FFFFFF',
  'text-cookie-link': '#FFED30',
  'text-field-focused': '#FFED30',
  'text-field-error': '#F62C2C',
  'text-gift-pack': '#FFFFFF',
  'text-gift-pack-medium': '#FFED30',
  'bg-text-field': '#151050',
  'bg-dialog': '#151050',
  'bg-dialog-medium': '#0D1234',
  'bg-btn': '#FFED30',
  'bg-switch': '#FFED30',
  'bg-menu': '#0C105B',
  'bg-app-awards-title-dot': '#FFC700',
  'bg-game-square-rtp-dot': '#FFC700',
  'bg-card': '#1E0968',
  'bg-table-medium': '#3D4056',
  'bg-table-hover': '#27235E',
  'bg-table': '#151050',
  'tab-focused': '#FFED30',
  'btn-regular': '#FFED30',
  'btn-soft': '#FFFFFF',
  'btn-dialog-inverse': '#09051A',
  'btn-chat': '#FFED30',
  'border-regular': '#FFF85B',
  'bg-scrollbar': '#27235E',
  'scrollbar-regular': '#3E35A8',
  'btn-iframe-bar': '#FFFFFF',
  'bg-iframe-bar': '#09051A',
  'bg-slider-track': '#FFED30',
  'bg-slider-thumb': '#FFED30',
  'text-slider': '#FFFFFF',
  'text-slider-hover': '#FFED30',
  'bg-pagination': '#6923FF',
  'bg-pagination-focus': '#FFED30',
  'text-pagination': '#FFFFFF',
  'text-pagination-focused': '#09051A',
  'bg-badge': '#0C105B',
  'bg-badge-error': '#F62C2C',
  'text-badge': '#FFFFFF',
  'text-success': '#05C753',
  'text-member-level': '#898CA3',
  'bg-badge-success': '#05C753',
  'border-badge': '#151050',
  'bg-progress-determinate': '#FFC700',
  'text-progress': '#FFFFFF',
  'border-dialog': '#FFED30',
  'bg-badge-disable': '#ABAEB7',
  'text-datetime-picker-medium': '#FFC700',
  'text-datetime-picker-btn-focused': '#09051A',
  'btn-datetime-picker': '#FFF85B',
  'btn-datetime-picker-medium': '#FFC700',
  'btn-datetime-picker-heavy': '#09051A',
  'bg-datetime-picker': '#151050',
  'bg-datetime-picker-btn-focused': '#FFC700'
}

export default {
  css: [`@/assets/scss/${STATION}/main.scss`],

  styleResources: {
    scss: [`@/assets/scss/${STATION}/variables.scss`]
  },
  // Vuetify module configuration (https://go.nuxtjs.dev/config-vuetify)
  vuetify: {
    treeShake: true,
    theme: {
      dark: true,
      themes: {
        light: {
          primary: '#FFED30',
          secondary: '#6923FF',
          warning: '#FF8329',
          error: '#F62C2C',
          white: '#ffffff',
          success: '#05C753',
          'primary-variant-1': '#FFC700',
          'primary-variant-2': '#FFF85B',
          'primary-variant-3': '#FFF6AA',
          'grey-0': '#252C33',
          'grey-1': '#E2E2E8',
          'grey-2': '#ADAFC2',
          'grey-25': '#8A94B1',
          'grey-3': '#898CA3',
          'grey-4': '#3D4056',
          'grey-5': '#1C255F',
          'grey-6': '#0D1234'
        },
        dark: {
          primary: '#FFED30',
          secondary: '#6923FF',
          warning: '#FF8329',
          error: '#F62C2C',
          white: '#ffffff',
          black: '#000000',
          success: '#05C753',
          info: '#2253FF',
          'primary-variant-1': '#FFC700',
          'primary-variant-2': '#FFF85B',
          'primary-variant-3': '#FFF6AA',
          'secondary-variant-1': '#440106',
          'grey-1': '#E2E2E8',
          'grey-2': '#ADAFC2',
          'grey-3': '#898CA3',
          'grey-4': '#3D4056',
          'grey-5': '#1C255F',
          'grey-6': '#0D1234',
          'default-content': '#FFFFFF',
          'button-content': '#050A1A',
          'dialog-fill': '#151050',
          'dialog-fill-2': '#020B3E',
          divider: '#ffff66',
          'footer-fill': '#0C105B',
          'iframe-bar': '#09051A',
          'footer-item': '#4d4545',
          'app-bar-item': '#0C105B',
          'button-icon': '#292222',
          'game-hover': '#48454D',
          'card-gradient-2': '#1E0968',
          'card-fill': '#1E0968',
          'pagination-fill': '#6923FF',
          'scroll-fill': '#27235E',
          scroll: '#3E35A8',
          'letter-info': '#8FAFFF',
          'footer-button': '#BBBFC9',
          'btn-disable': '#ffff4d',
          offline: '#b2aaaa',
          private: '#FFFFFF',
          'name-private': '#36bf36',
          'app-bar_line': '#FFED30',
          'vip-g-1': '#FEC600',
          'vip-g-2': '#DC1646',
          ...replaceColor
        }
      }
    },
    breakpoint: {
      //與預設不同，md & lg 需考慮到 scrollbar 寬度
      thresholds: {
        xs: 600,
        sm: 960,
        md: 1270,
        lg: 1910
      },
      scrollBarWidth: 6
    }
  }
}
