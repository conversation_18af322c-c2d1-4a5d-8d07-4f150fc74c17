const STATION = process.env.STATION

// 共用的主題顏色定義
const replaceColor = {
  'title-soft': '#FBFBFB',
  'title-character-card': '#E9B950',
  'text-character-card': '#FBFBFB',
  'text-regular': '#FBFBFB',
  'text-medium': '#E9B950',
  'text-heavy': '#FEA600',
  'text-list': '#FBFBFB',
  'text-list-focused': '#E9B950',
  'text-dialog-header': '#E6E1E1',
  'text-dialog-header-inverse': '#1A0B0B',
  'text-btn': '#1A0B0B',
  'text-btn-heavy': '#1A0B0B',
  'text-marquee-medium': '#E9B950',
  'text-marquee': '#FBFBFB',
  'text-app-awards': '#F7B675',
  'text-app-awards-soft': '#FBFBFB',
  'text-app-awards-medium': '#E9B950',
  'text-game-square-rtp': '#FFFFFF',
  'text-game-promote-rtp': '#FFFFFF',
  'text-game-square-chip': '#FBFBFB',
  'text-pwa': '#1A0B0B',
  'text-cookie': '#FBFBFB',
  'text-cookie-link': '#E9B950',
  'text-field-focused': '#E9B950',
  'text-field-error': '#F62C2C',
  'text-gift-pack': '#FBFBFB',
  'text-gift-pack-medium': '#E9B950',
  'text-gift-pack-btn': '#FFFFFF',
  'bg-text-field': '#552F31',
  'bg-dialog': '#552F31',
  'bg-dialog-medium': '#402222',
  'bg-btn': '#FEA600',
  'bg-switch': '#E9B950',
  'bg-menu': '#472426',
  'bg-app-awards-title-dot': '#E9B950',
  'bg-game-square-rtp-up': '#F62C2C',
  'bg-game-square-rtp-down': '#4CAF50',
  'bg-card': '#440106',
  'bg-table-medium': '#4C3535',
  'bg-table-hover': '#331C1C',
  'bg-table': '#291515',
  'btn-chat': '#FEA600',
  'tab-focused': '#E9B950',
  'btn-regular': '#E9B950',
  'btn-soft': '#FBFBFB',
  'btn-dialog-inverse': '#1A0B0B',
  'border-regular': '#F7B675',
  'decorate-border-gift-pack': '#2a0909',
  'bg-scrollbar': '#331C1C',
  'scrollbar-regular': '#886D6D',
  'btn-iframe-bar': '#FFFFFF',
  'bg-iframe-bar': '#1A0B0B',
  'bg-slider-track': '#E9B950',
  'bg-slider-thumb': '#E9B950',
  'text-slider': '#FBFBFB',
  'text-slider-hover': '#E9B950',
  'bg-pagination': '#AC2335',
  'bg-pagination-focus': '#FEA600',
  'text-pagination': '#FFFFFF',
  'text-pagination-focused': '#1A0B0B',
  'bg-app-coin-section': '#440106',
  'text-app-coin': '#FBFBFB',
  'bg-badge': '#440106',
  'bg-badge-error': '#F62C2C',
  'text-badge': '#FBFBFB',
  'text-success': '#4CAF50',
  'text-member-level': '#A38989',
  'bg-badge-success': '#4CAF50',
  'border-badge': '#552F31',
  'bg-progress-determinate': '#FEA600',
  'text-progress': '#FBFBFB',
  'border-dialog': '#E9B950',
  'bg-badge-disable': '#B2AAAA',
  'text-datetime-picker-medium': '#FEA600',
  'text-datetime-picker-btn-focused': '#1A0B0B',
  'btn-datetime-picker': '#F7B675',
  'btn-datetime-picker-medium': '#FEA600',
  'btn-datetime-picker-heavy': '#1A0B0B',
  'bg-datetime-picker': '#552F31',
  'bg-datetime-picker-btn-focused': '#FEA600'
}

export default {
  css: [`@/assets/scss/${STATION}/main.scss`],

  styleResources: {
    scss: [`@/assets/scss/${STATION}/variables.scss`]
  },
  // Vuetify module configuration (https://go.nuxtjs.dev/config-vuetify)
  vuetify: {
    treeShake: true,
    theme: {
      dark: true,
      themes: {
        light: {
          primary: '#4176FA',
          secondary: '#FFB235',
          warning: '#FF7917',
          error: '#E71A5B',
          white: '#ffffff',
          success: '#4CAF50',
          'primary-variant-1': '#6CA7FF',
          'primary-variant-2': '#F7B675',
          'primary-variant-3': '#C8E8FF',
          'grey-0': '#252C33',
          'grey-1': '#3A4550',
          'grey-2': '#4E5774',
          'grey-25': '#8A94B1',
          'grey-3': '#8A94B1',
          'grey-4': '#CBD1E7',
          'grey-5': '#F0F6FF',
          'grey-6': '#FAFBFD'
        },
        dark: {
          primary: '#E9B950',
          secondary: '#AC2335',
          warning: '#FF9F21',
          error: '#F62C2C',
          white: '#ffffff',
          black: '#000000',
          success: '#4CAF50',
          info: '#4176FA',
          'primary-variant-1': '#FEA600',
          'primary-variant-2': '#F7B675',
          'primary-variant-3': '#FFE7BD',
          'secondary-variant-1': '#440106',
          'grey-1': '#E6E1E1',
          'grey-2': '#B2AAAA',
          'grey-3': '#A38989',
          'grey-4': '#4C3535',
          'grey-5': '#402222',
          'grey-6': '#291515',
          'default-content': '#FBFBFB',
          'button-content': '#1A0508',
          'dialog-fill': '#552F31',
          'dialog-fill-2': '#472426',
          divider: '#ffffff',
          'footer-fill': '#000000',
          'iframe-bar': '#1A0508',
          'footer-item': '#4D4545',
          'app-bar-item': '#440106',
          'button-icon': '#292222',
          'game-hover': '#4D4545',
          'card-gradient-2': '#440106',
          'card-fill': '#440106',
          'pagination-fill': '#AC2335',
          'scroll-fill': '#331C1C',
          scroll: '#886D6D',
          'letter-info': '#8FAFFF',
          'footer-button': '#B2AAAA',
          'btn-disable': '#FFFFFF',
          offline: '#B2AAAA',
          private: '#FFFFFF',
          'name-private': '#36BF36',
          ...replaceColor
        }
      }
    },
    breakpoint: {
      //與預設不同，md & lg 需考慮到 scrollbar 寬度
      thresholds: {
        xs: 600,
        sm: 960,
        md: 1270,
        lg: 1910
      },
      scrollBarWidth: 6
    }
  }
}
