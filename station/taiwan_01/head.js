const STATION = process.env.STATION
export default {
  head() {
    return this.$nuxtI18nHead({ addSeoAttributes: true })
  },
  // Global page headers (https://go.nuxtjs.dev/config-head)
  // eslint-disable-next-line no-dupe-keys
  head: {
    meta: [
      { charset: 'utf-8' },
      {
        name: 'viewport',
        content:
          'width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1, user-scalable=no,viewport-fit=cover'
      },
      {
        hid: 'description',
        name: 'description',
        content: '星城Online Web館:::全球華人的線上遊戲娛樂城:::帶您體驗來自世界各國的遊戲'
      },
      { name: 'mobile-web-app-capable', content: 'yes' },
      { name: 'apple-touch-fullscreen', content: 'yes' },
      { name: 'apple-mobile-web-app-title', content: '星城Online Web館 ' },
      { name: 'apple-mobile-web-app-capable', content: 'yes' },
      { name: 'apple-mobile-web-app-status-bar-style', content: 'default' },
      { name: 'facebook-domain-verification', content: 'dwny3mj57yj82ope9jakync9htx112' },
      { property: 'og:title', content: '星城Online Web館' },
      {
        property: 'og:description',
        content: '星城Online Web館:::全球華人的線上遊戲娛樂城:::帶您體驗來自世界各國的遊戲'
      }
    ],
    link: [
      {
        rel: 'icon',
        type: 'image/x-icon',
        href: `/${STATION}/favicon.ico?v=${Math.floor(Math.random() * 100)}`
      },
      {
        rel: 'apple-touch-icon',
        href: `/${STATION}/apple-touch-icon.png?v=${Math.floor(Math.random() * 100)}`
      },
      {
        rel: 'apple-touch-icon',
        sizes: '180x180',
        href: `/${STATION}/apple-touch-icon.png?v=${Math.floor(Math.random() * 100)}`
      },
      {
        rel: 'icon',
        type: 'image/png',
        sizes: '32x32',
        href: `/${STATION}/favicon-32x32.png?v=${Math.floor(Math.random() * 100)}`
      },
      {
        rel: 'icon',
        type: 'image/png',
        sizes: '16x16',
        href: `/${STATION}/favicon-16x16.png?v=${Math.floor(Math.random() * 100)}`
      },
      {
        rel: 'icon',
        type: 'image/png',
        sizes: '192x192',
        href: `/${STATION}/android-chrome-192x192.png?v=${Math.floor(Math.random() * 100)}`
      },
      {
        rel: 'icon',
        type: 'image/png',
        sizes: '256x256',
        href: `/${STATION}/android-chrome-256x256.png?v=${Math.floor(Math.random() * 100)}`
      },
      {
        rel: 'icon',
        type: 'image/png',
        sizes: '512x512',
        href: `/${STATION}/android-chrome-512x512.png?v=${Math.floor(Math.random() * 100)}`
      },
      {
        rel: 'mask-icon',
        href: `/safari-pinned-tab.svg?v=${Math.floor(Math.random() * 100)}`,
        color: '#5bbad5'
      },
      {
        rel: 'dns-prefetch',
        href: 'https://cdn.jsdelivr.net/'
      },
      {
        rel: 'dns-prefetch',
        href: 'https://fonts.googleapis.com/'
      },
      { rel: 'preconnect', href: 'https://cdn.jsdelivr.net/' },
      { rel: 'preconnect', href: 'https://fonts.googleapis.com/' },
      {
        rel: 'preload',
        href: 'https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@48,400,0,0',
        as: 'style'
      },
      {
        rel: 'stylesheet',
        href: 'https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@48,400,0,0'
      },
      {
        rel: 'stylesheet',
        href: 'https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@48,400,1,0'
      }
    ]
  }
}
