export default {
  client_id: '11a86521',
  secret_key: '842417ef317c19b64f2d953572923f0b',
  env: {
    BASE_URL: 'https://www.joyouspalace.com/',
    NUXT_ENV: process.env.NUXT_ENV || process.env.NODE_ENV || 'development',
    IMAGE_URL: 'https://img.xinverse.xyz',
    CLIENT_VERSION: process.env.CLIENT_VERSION || '20230802100001',
    PLATFORM_URL: 'https://www.joyouspalace.com/',
    AVATAR_BASE_URL: 'https://images.joyouspalace.com',
    STATION: process.env.STATION || undefined
  },
  lock: {
    guild: true,
    useQpp: false,
    literalPrison: true,
    appDownload: false,
    autoCreatChar: true,
    chatroom: {
      otherStarCityOnlinePlatformsReminderDisabled: true
    },
    specialGame: {
      live: true,
      top_and_hot: true,
      fishing: false,
      featured: true,
      newest_and_recommend: true
    }
  },
  game: {
    serverConstant: 20000
  },
  customLocaleList: [
    // {
    //   code: 'zh-tw',
    //   text: '繁體中文',
    //   src: 'tw.svg'
    // },
    { code: 'en', text: 'English', src: 'en.svg' }
    //{ code: 'zh-cn', text: '简体中文', src: 'cn.svg' }
    // { code: 'vi-vn', text: 'Tiếng Việt', src:  'vietnam.svg' }
  ],
  i18n: {
    vueI18n: {
      messages: {
        en: require('../../locales/en')
        //'zh-cn': require('../../locales/zh-cn')
      },
      fallbackLocale: {
        default: ['en']
      }
    },
    locales: [
      { code: 'en', iso: 'en-US', file: 'en.js', isCatchallLocale: true }
      //{ code: 'zh-cn', iso: 'zh-CN', file: 'zh-cn.js' }
    ],
    detectBrowserLanguage: {
      useCookie: true,
      cookieKey: 'locale',
      redirectOn: 'root'
    },
    vuex: {
      syncLocale: true
    },
    lazy: true,
    langDir: 'locales/',
    seo: false,
    defaultLocale: 'en',
    baseUrl: process.env.BASE_URL
  }
}
