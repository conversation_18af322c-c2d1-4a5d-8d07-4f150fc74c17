const STATION = process.env.STATION
export default {
  /**
   * 正式環境下的 Cache 策略
   * 靜態檔案將採用 CacheFirst 策略
   * Ajex Request 將採用 NetWorkFirst 策略
   *
   * 參照：https://developers.google.com/web/tools/workbox/modules/workbox-strategies
   */
  pwa: {
    name: 'JOYOUS PALACE',
    icon: {
      source: `@/static/${STATION}/android-chrome-512x512.png`,
      fileName: `${STATION}/android-chrome-512x512.png`,
      sizes: [64, 120, 144, 152, 192, 384, 512],
      iosSizes: [],
      purpose: 'maskable'
    },
    meta: {
      author: 'WANIN',
      title: 'JOYOUS PALACE',
      ogHost: process.env.BASE_URL || 'https://malaysia-client.xinverse.xyz/',
      nativeUI: true
    },
    manifest: {
      id: '/?source=pwa',
      start_url: '/?source=pwa',
      name: 'JOYOUS PALACE',
      short_name: 'JOYOUS PALACE',
      scope: '/',
      description: 'JOYOUS PALACE | Dive into an array of thrilling gaming experiences!',
      useWebmanifestExtension: 'json',
      lang: 'zh-Hant-TW',
      icons: [
        {
          src: `/${STATION}/favicon-16x16.png?v=${Math.floor(Math.random() * 100)}`,
          sizes: '16x16',
          type: 'image/png'
        },
        {
          src: `/${STATION}/favicon-32x32.png?v=${Math.floor(Math.random() * 100)}`,
          sizes: '32x32',
          type: 'image/png'
        },
        {
          src: `/${STATION}/apple-touch-icon.png?v=${Math.floor(Math.random() * 100)}`,
          sizes: '180x180',
          type: 'image/png'
        },
        {
          src: `/${STATION}/android-chrome-192x192.png?v=${Math.floor(Math.random() * 100)}`,
          sizes: '192x192',
          type: 'image/png'
        },
        {
          src: `/${STATION}/android-chrome-256x256.png?v=${Math.floor(Math.random() * 100)}`,
          sizes: '256x256',
          type: 'image/png'
        },
        {
          src: `/${STATION}/android-chrome-512x512.png?v=${Math.floor(Math.random() * 100)}`,
          sizes: '512x512',
          type: 'image/png'
        }
      ],
      theme_color: '#ffffff',
      background_color: '#ffffff',
      display_override: ['window-control-overlay'],
      display: 'standalone',
      shortcuts: [
        {
          name: 'JOYOUS PALACE',
          short_name: 'JOYOUS PALACE',
          description: 'JOYOUS PALACE | Dive into an array of thrilling gaming experiences!',
          url: '/?standalone=true',
          icons: [
            {
              src: `/${STATION}/android-chrome-192x192.png?v=${Math.floor(Math.random() * 100)}`,
              sizes: '192x192'
            }
          ]
        }
      ],
      screenshots: []
    },
    workbox: {
      enabled: false,
      // autoRegister: true,
      // cachingExtensions: '@/plugins/workbox/extensions.js'
      runtimeCaching: [
        {
          urlPattern: '/*',
          handler: 'networkFirst', // 快取策略
          method: 'GET'
        }
      ]
    }
  }
}
