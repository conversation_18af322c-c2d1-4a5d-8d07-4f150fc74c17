const config = {
  //站台功能開關
  lock: {
    //公會開關
    guild: true,
    //OPP開關
    useQpp: true,
    //星城Online下載顯示開關
    appDownload: true,
    //reminder顯示開關
    otherStarCityOnlinePlatformsReminderDisabled: true,
    //華義實名
    getSlotIdentity: true,
    //啟用Station翻譯>> lang_XXX
    stationConvert: false,
    //檢查綁定手機
    checkBindPhoneNumber: false,
    //文字獄
    literalPrison: false,
    specialGame: {
      live: true,
      top_and_hot: true,
      fishing: false,
      featured: true,
      newest_and_recommend: true,
      chess_and_card: false
    },
    chatroom: {
      playerSemantic: true,
      msgSticker: true,
      msgCustomSticker: true,
      customerServiceCustomSticker: true,
      checkoutUserStation: false
    },
    // 拉彩公告
    grandPrizeNoty: false,
    unlockOfficial: 1
  },
  grandPrizeNoty: {
    pageClass: 'grand-prize-cards-wrapper px-4 py-2 mt-4 bg-pale',
    defaultImg: require('~/assets/image/grand_prize_default.webp'),
    defaultImgPng: require('~/assets/image/grand_prize_default.png'),
    grandPrizeItem: {
      class: 'cursor-pointer rounded-0',
      hasDownLoad: true
    },
    imgStyle: ''
  },
  vipLevel: {
    vipLevelTitle: [
      'none_vip',
      'bronze',
      'silver',
      'gold',
      'whiteGold',
      'platinum',
      'diamond',
      'fancyDiamond',
      'moonDazzle',
      'sunDazzle',
      'starDazzle'
    ],
    vipLevelImgFileName: [
      '0-1_None',
      '1-1_Bronze',
      '2-1_Silver',
      '3-1_Gold',
      '4-1_WhiteGold',
      '5-1_Platinum',
      '6-1_Diamond',
      '7-1_FancyDiamond',
      '8-1_MoonDazzle',
      '9-1_SunDazzle',
      '10-1_StarDazzle'
    ],
    vipLevelImg3xFileName: [
      '0-1_None@3x',
      '1-1_Bronze@3x',
      '2-1_Silver@3x',
      '3-1_Gold@3x',
      '4-1_WhiteGold@3x',
      '5-1_Platinum@3x',
      '6-1_Diamond@3x',
      '7-1_FancyDiamond@3x',
      '8-1_MoonDazzle@3x',
      '9-1_SunDazzle@3x',
      '10-1_StarDazzle@3x'
    ],
    vipLevelIconFileName: [
      '0_None_icon.png',
      '1_Bronze_icon.png',
      '2_Silver_icon.png',
      '3_Gold_icon.png',
      '4_WhiteGold_icon.png',
      '5_Platinum_icon.png',
      '6_Diamond_icon.png',
      '7_FancyDiamond_icon.png',
      '8_MoonDazzle_icon.png',
      '9_SunDazzle_icon.png',
      '10_StarDazzle_icon.png'
    ]
  },
  miniGame: {
    enable: false,
    keyWords: ['已配發銅幣，請點擊大廳下方【銅幣】領取']
  },
  timeStamp: {
    format: 'YYYY-MM-DD HH:mm:ss',
    formatDate: 'YYYY/MM/DD',
    formatNewsNote: 'YYYY/MM/DD HH:mm [{0}]',
    formatError: 'YYYY-MM-DD HH:mm',
    formatLogin: 'YYYY-MM-DD HH:mm:ss',
    timezone: 'Asia/Taipei'
  },
  qpp: {
    showQPPIcon: false
  },
  //站台樣式名重新命名
  replaceClassName: true,
  defaultBtnColor: 'primary',
  //facebbok使用版本
  facebookVersion: 10,
  customerService: {
    title: 'gradient-primary-down',
    announcementColor: { red: 255, blue: 0, green: 199 } //text-medium
  },
  chatroom: {
    mutedText: '{0}{1}(GMT+8)',
    banSpeaking: '{0}(GMT+8)',
    //客服是否使用電話號碼>>電話:信箱
    usePhoneNumber: true,
    welcomeMessageContent: ''
  },
  msgBar: {
    customerBtn: { icon: 'support_agent', color: 'primary', isVuetifyIcon: false },
    sendIconColor: 'primary'
  },
  msgBox: {
    dateTimeText: 'A hh:mm'
  },
  specialGameCard: {
    fontWeight: 'font-weight-black'
  },
  gameCard: {
    backGroundColor: (dailyRtpResult, defaultRtp) => {
      return dailyRtpResult > defaultRtp ? 'bg-game-square-rtp-up' : 'bg-game-square-rtp-down'
    }
  },
  newLogin: {
    companyInfoList: { herf: true, click: false, style: '' },
    phoneIconColor: 'default-content--text',
    loginNotyArray: [
      'before_login_notice1',
      'before_login_notice2',
      'before_login_notice3',
      'before_login_notice4',
      'before_login_notice5',
      'infringement_notice',
      'free_play_no_calculate_notice'
    ],
    loginMethod: [
      {
        id: 1,
        icon: 'phone_iphone',
        iconFile: null,
        title: 'phone_number_login',
        maxWidth: '28px',
        status: 1
      },
      {
        id: 2,
        icon: null,
        iconFile: require('~/assets/image/login/apple_login.svg'),
        title: 'Apple',
        maxWidth: '28px',
        status: 1
      },
      {
        id: 3,
        icon: null,
        iconFile: require('~/assets/image/login/facebook_login.svg'),
        title: 'Facebook',
        maxWidth: '28px',
        status: 1
      },
      {
        id: 4,
        icon: null,
        iconFile: require('~/assets/image/login/google_login.svg'),
        title: 'Google',
        maxWidth: '28px',
        status: 1
      },
      {
        id: 5,
        icon: null,
        iconFile: require('~/assets/image/login/line-login.svg'),
        title: 'LINE',
        maxWidth: '36px',
        status: 1
      }
    ]
  },
  guildInfoPage: {
    showLeaveCountDown: false,
    infoListOptions: [10, 20, 30, -1]
  },
  guildEditDialog: {
    totemEnable: true,
    nameEnable: true,
    notyEnable: true,
    textFullWidthValidEnable: false
  },
  guildListPage: {
    backGroundColor: 'card-fill-bg',
    showGuildRank: true,
    guildRankExpiredDate: '2025-04-21 00:00:00',
    countDownTextSetting: '',
    countDownNumberColor: 'warning--text',
    guildBannerSetting: 'guild-banner-wayi'
  },
  guildAcceptList: {
    showAcceptNotyDialog: false
  },
  guildPinMsg: {
    enable: false,
    defaultMsg: '',
    splitMark: '－＄－'
  },
  phoneNumber: {
    dialogWidth: '720px',
    showQRCode: true,
    clearPhoneNumber: false
  },
  cookieNotice: {
    herf: null,
    target: null,
    click: true,
    companyInfoIdx: 3,
    companySetting: 'SET_WAYI_COMPANY_POLICY_TYPE',
    reflashOnAccept: false
  },

  coffer: {
    saveStarCoin: {
      title: '{0}{1}',
      balance: '{0}{1}',
      input: '{0}{1}'
    },
    withdrawCoin: {
      title: '{0}{1}',
      balance: '{0}{1}',
      input: '{0}{1}'
    }
  },
  mailIndex: {
    fontWeightRegular: true,
    mobileSendMailBtnColor: 'primary-variant-1',
    confirmDeleteDialogWidth: '290px',
    delieteReadAllBtnColor: 'primary-variant-1',
    mailTitleClass: 'primary-variant-1--text',
    mailTimeDay: '{1}{0}{2}',
    mailTimeHr: '{1}{0}{2}',
    sendNoty: 'mail_send_noty6_dont_bind_phone',
    sendMailLevel: 3
  },
  paymentIndex: {
    title: '{0}{1}',
    xinCoinFormat: '{0}{1}+{2}{3}{4}',
    redeemPoints: '{0}{1}',
    redeemXinCoins: '{1}{2}{3}',
    convertClass: 'pb-6',
    dictUpper: false,
    paymentDict: '{0}',
    showGiftPack: true,
    showVipTab: false
  },
  dailyList: {
    backGroundColor: 'card-fill-bg',
    description: true
  },
  role: {
    roleDialogWidth: '600px',
    userNameColor: 'default-content--text',
    titleBackgroundColor: 'gradient-primary-down',
    roleDescriptionSetting: 'line-height-20'
  },
  error: {
    showErrorInfo: true,
    showMaintenanceTime: true,
    maintenanceSubTitle: 'text-md-body-1',
    maintenanceText: 'font-weight-bold',
    maintenanceTimeText: '',
    maintenanceColon: '：',
    imgClass: 'mx-auto my-16'
  },
  playerInfoCardSetting: {
    dailyListEnable: true,
    achievementEnable: true,
    dailyListTableText: 'daily_list_desc1',
    useVisitBtn: false
  },
  badge: {
    classSetting: 'app-bar-item'
  },
  gamePage: {
    background: 'bg-color',
    sortGameName: 'recommend_games',
    resetUrlValue: true,
    inputValidate: 'special_character'
  },
  stationPage: {
    backGround: 'card-fill-bg'
  },
  easyPlayerInfo: {
    backGround: 'card-fill-2',
    showLevel: true
  },
  menu: {
    backGround: 'card-fill-bg'
  },
  yoeShop: {
    url: '{0}/yoegames/main?mode=web'
  },
  gameIframe: {
    logoHeight: '25',
    showNoExpDialog: true
  },
  swiperBox: {
    gameCardWidth: {
      lg: 'calc(16.67% - 15.9px)',
      md: 'calc(24.5% - 15.9px)',
      sm: 'calc(32.25% - 16px)',
      xs: 'calc(48% - 16px)'
    },
    featuredGameCardMinWidth: {
      lg: '260px'
    },
    featuredGameCardWidth: {
      lg: 'calc(25% - 15.9px)',
      md: '320px',
      sm: '268px',
      xs: '300px'
    },
    giftPackCardWidth: {
      lg: 'calc(100%/3 - 32px/3)',
      md: 'calc(100%/3 - 32px/3)',
      sm: 'calc(100%/3 - 32px/3)',
      xs: 'calc(100%/3 - 32px/3)',
      twoGiftPack: 'calc(100%/2 - 8px/2)'
    }
  },
  giftPackLayoutColor: {
    background: '#6D27B9',
    color: '#fbfbfb'
  },
  giftPackGradientBG: {
    background:
      'radial-gradient(95.52% 27.88% at 50% -12.07%, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%),radial-gradient(98.24% 20.9% at 50% 100%, #543FD2 0%, rgba(84, 63, 210, 0) 100%),conic-gradient(from {0}deg at 100% 100%, rgba(84, 63, 210, 0) 0deg, rgba(84, 63, 210, 0.3) 360deg),#543FD2',
    vipBackground:
      'radial-gradient(95.52% 27.88% at 50% -12.07%, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%),radial-gradient(98.24% 20.9% at 50% 100%, #7922AF 0%, rgba(121, 34, 175, 0) 100%),conic-gradient(from {0}deg at 100% 100%, rgba(121, 34, 175, 0) 0deg, rgba(121, 34, 175, 0.3) 360deg),#7922AF'
  },
  giftPackCurrency: 'TWD',
  footbar: {
    gameCategoryColor: 'white',
    menuBackgroundColor: ''
  },
  footer: {
    footerNotyArray: [
      'footer_warning1',
      'footer_warning2',
      'footer_warning3',
      'footer_warning4',
      'footer_warning5',
      'footer_warning6',
      'infringement_notice',
      'free_play_no_calculate_notice'
    ],
    communityList: [
      {
        name: 'facebook',
        icon: null,
        iconFile: 'facebook.svg',
        link: 'https://www.facebook.com/Wayi.BanaBana/'
      }
    ]
  },
  characterInfo: {
    showLevel: true,
    showVipLevel: true,
    showGuild: true,
    showCoin: true,
    showHonor: true,
    showActive: true,
    showSilverCoin: false
  },
  vipLevelDescDialog: {
    vipImgTitle: 'vip'
  },
  marqueeColor: {
    marqueeBackgroundColor: '',
    marqueeFontColor: ''
  },
  gameIntro: {
    coverWidth: {
      mdAndUp: '212px',
      smAndDown: '160px'
    }
  },
  playerAppAssetStatus: false,
  // 系統限制設定
  restriction: {
    reportInterval: 15 // 檢舉冷卻時間（秒）
  },
  //站台樣式名重新命名
  replaceColor: {
    btnRegular: 'btn-regular',
    bgGamePromoteRtp: 'bg-game-promote-rtp',
    textGamePromoteRtp: 'text-game-promote-rtp',
    white: 'white',
    bgBtnHeavy: 'bg-btn-heavy',
    textBtnHeavyText: 'text-btn-heavy--text',
    bgBtn: 'bg-btn',
    tabFocused: 'tab-focused',
    bgSwitch: 'bg-switch',
    bgDialogHeader: 'bg-dialog-header',
    customDialogTitleCloseBtn: 'btn-dialog-inverse',
    dailyListBackGroundColor: 'bg-card',
    textCharacterCard: 'text-character-card--text',
    roleIndexBgBtnHeavy: 'bg-btn-heavy',
    bgSlider: 'bg-slider',
    bgSliderTrack: 'bg-slider-track',
    bgSliderThumb: 'bg-slider-thumb',
    pageBackGroundColor: 'bg-card',
    paymentTab: 'tab-focused',
    textRegular: 'text-regular',
    bgDialogMedium: 'bg-dialog-medium',
    bgDialog: 'bg-dialog',
    customDialogSendIconColor: 'btn-chat',
    customDialogSendIconColor2: 'btn-chat',
    customDialogbgBtn2: 'bg-btn',
    customServiceImageBtn: 'btn-soft',
    playerDetailBgBtn: 'bg-btn',
    playerStatusBgDialog: 'bg-dialog',
    bgBadge: 'bg-badge',
    NotyCountBgBadgeError: 'bg-badge-error',
    chatSettingBgSliderTrack: 'bg-slider-track',
    chatSettingBgSliderThumb: 'bg-slider-thumb',
    chatSheetBgDialogMedium: 'bg-dialog-medium',
    bgProgress: 'bg-progress',
    bgProgressDeterminate: 'bg-progress-determinate',
    bgTextField: 'bg-text-field',
    chatStickerBtnSoft: 'btn-soft',
    bgBadgeSuccess: 'bg-badge-success',
    bgBadgeDisable: 'bg-badge-disable'
  },
  homeIndex: {
    titleClass: 'gradient-primary--text'
  }
}

// 導出 config
export { config }

// eslint-disable-next-line no-unused-vars
export default ({ app }, inject) => {
  // 将 config 注入到应用中
  inject('UIConfig', config)
}
