const STATION = process.env.STATION

// 共用的主題顏色定義
const replaceColor = {
  'title-soft': '#FFFFFF',
  'title-character-card': '#FFC700',
  'text-character-card': '#FFFFFF',
  'text-soft': '#B2AAB0',
  'text-regular': '#FFFFFF',
  'text-medium': '#FFC700',
  'text-heavy': '#FF9900',
  'text-list': '#FFFFFF',
  'text-list-focused': '#FFC700',
  'text-dialog-header': '#E6E1E6',
  'text-dialog-header-inverse': '#000000',
  'text-btn': '#000000',
  'text-btn-heavy': '#000000',
  'text-marquee-medium': '#FFC700',
  'text-marquee': '#FFFFFF',
  'text-app-awards': '#F7B675',
  'text-app-awards-soft': '#FBFBFB',
  'text-app-awards-medium': '#E9B950',
  'text-game-square-rtp': '#FFFFFF',
  'text-game-promote-rtp': '#FFFFFF',
  'text-game-square-chip': '#321B42',
  'text-pwa': '#1A0B0B',
  'text-cookie': '#FFFFFF',
  'text-cookie-link': '#FFC700',
  'text-field-focused': '#FFC700',
  'text-field-error': '#FF005C',
  'text-gift-pack': '#FFFFFF',
  'text-gift-pack-medium': '#FFC700',
  'text-gift-pack-btn': '#FFFFFF',
  'bg-text-field': '#3B2050',
  'bg-dialog': '#3B2050',
  'bg-dialog-medium': '#2B1439',
  'bg-btn': '#FFC700',
  'bg-switch': '#FFC700',
  'bg-menu': '#36174E',
  'bg-app-awards-title-dot': '#E9B950',
  'bg-game-square-rtp-up': '#FF005C',
  'bg-game-square-rtp-down': '#00CF6C',
  'bg-card': '#431B57',
  'bg-table-medium': '#47354C',
  'bg-table-hover': '#3B2050',
  'bg-table': '#2B1439',
  'btn-chat': '#FFC700',
  'tab-focused': '#FFC700',
  'btn-regular': '#FFC700',
  'btn-soft': '#FFFFFF',
  'btn-dialog-inverse': '#000000',
  'border-regular': '#FFAF37',
  'decorate-border-gift-pack': '#FFAF37',
  'bg-scrollbar': '#1E0B37',
  'scrollbar-regular': '#422C47',
  'btn-iframe-bar': '#FFFFFF',
  'bg-iframe-bar': '#0D051A',
  'bg-slider-track': '#FFC700',
  'bg-slider-thumb': '#FFC700',
  'text-slider': '#FFFFFF',
  'text-slider-hover': '#FFC700',
  'bg-pagination': '#693676',
  'bg-pagination-focus': '#FFC700',
  'text-pagination': '#FFFFFF',
  'text-pagination-focused': '#000000',
  'bg-badge': '#46184B',
  'text-badge': '#FFFFFF',
  'text-success': '#00CF6C',
  'text-member-level': '#948695',
  'bg-badge-error': '#FF005C',
  'bg-badge-success': '#00CF6C',
  'border-badge': '#3B2050',
  'bg-progress-determinate': '#FF9900',
  'text-progress': '#FFFFFF',
  'border-dialog': '#FFC700',
  'bg-badge-disable': '#B2AAB0',
  'text-datetime-picker-medium': '#FF9900',
  'text-datetime-picker-btn-focused': '#000000',
  'btn-datetime-picker': '#FFAF37',
  'btn-datetime-picker-medium': '#FF9900',
  'btn-datetime-picker-heavy': '#000000',
  'bg-datetime-picker': '#3B2050',
  'bg-datetime-picker-btn-focused': '#FF9900'
}

export default {
  css: [`@/assets/scss/${STATION}/main.scss`],

  styleResources: {
    scss: [`@/assets/scss/${STATION}/variables.scss`]
  },
  // Vuetify module configuration (https://go.nuxtjs.dev/config-vuetify)
  vuetify: {
    treeShake: true,
    theme: {
      dark: true,
      themes: {
        light: {
          primary: '#4176FA',
          secondary: '#FFB235',
          warning: '#FF7917',
          error: '#E71A5B',
          white: '#ffffff',
          success: '#4CAF50',
          'primary-variant-1': '#6CA7FF',
          'primary-variant-2': '#F7B675',
          'primary-variant-3': '#C8E8FF',
          'grey-0': '#252C33',
          'grey-1': '#3A4550',
          'grey-2': '#4E5774',
          'grey-25': '#8A94B1',
          'grey-3': '#8A94B1',
          'grey-4': '#CBD1E7',
          'grey-5': '#F0F6FF',
          'grey-6': '#FAFBFD'
        },
        dark: {
          primary: '#FFC700',
          secondary: '#6D27B9',
          warning: '#FF7A00',
          error: '#FF005C',
          white: '#FFF',
          black: '#000',
          success: '#00CF6C',
          info: '#0065FC',
          'primary-variant-1': '#FF9900',
          'primary-variant-2': '#FFAF37',
          'primary-variant-3': '#FFF576',
          'secondary-variant-1': '#440106',
          'grey-1': '#E6E1E6',
          'grey-2': '#B2AAB1',
          'grey-3': '#948695',
          'grey-4': '#47354C',
          'grey-5': '#341c47',
          'grey-6': '#2B1439',
          'default-content': '#FFF',
          'default-content-1': '#FFFFFF',
          'default-content-2': '#8e7194',
          'button-content': '#000',
          'dialog-fill': '#3B2050',
          'dialog-fill-2': '#2C183A',
          divider: '#ffffff',
          'footer-fill': '#000000',
          'iframe-bar': '#0D051A',
          'footer-item': '#4D4545',
          'app-bar-item': '#46184B',
          'button-icon': '#292222',
          'game-hover': '#4D4545',
          'card-gradient-2': '#440106',
          'card-fill': '#553073',
          'card-fill-2': '#36174E',
          'pagination-fill': '#693676',
          'scroll-fill': '#1E0B37',
          scroll: '#422c47',
          'letter-info': '#71A1FF',
          'footer-button': '#B2AAB1',
          'btn-disable': '#FFFFFF',
          offline: '#B2AAB1',
          private: '#FFFFFF',
          'name-private': '#36BF36',
          'vip-g-1': '#FE7A00',
          'vip-g-2': '#AC2335',
          ...replaceColor
        }
      }
    },
    breakpoint: {
      //與預設不同，md & lg 需考慮到 scrollbar 寬度
      thresholds: {
        xs: 600,
        sm: 960,
        md: 1270,
        lg: 1910
      },
      scrollBarWidth: 6
    }
  }
}
