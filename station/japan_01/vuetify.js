const STATION = process.env.STATION
export default {
  css: [`@/assets/scss/${STATION}/main.scss`],

  styleResources: {
    scss: [`@/assets/scss/${STATION}/variables.scss`]
  },
  // Vuetify module configuration (https://go.nuxtjs.dev/config-vuetify)
  vuetify: {
    treeShake: true,
    theme: {
      dark: true,
      themes: {
        light: {
          primary: '#FFCFAA',
          secondary: '#675BD5 ',
          warning: '#F68041',
          error: '#FF3333',
          white: '#FFFFFF ',
          success: '#10BC69',
          'primary-variant-1': '#F9A778',
          'primary-variant-2': '#FFA159',
          'primary-variant-3': '#FFE1B7',
          'grey-0': '#252C33',
          'grey-1': '#DDDCE5',
          'grey-2': '#ABA7BE',
          'grey-25': '#8A94B1',
          'grey-3': '#8C849F',
          'grey-4': '#3E3459',
          'grey-5': '#2B2142',
          'grey-6': '#201736'
        },
        dark: {
          primary: '#FFCFAA',
          secondary: '#675BD5',
          warning: '#F68041',
          error: '#FF3333',
          white: '#FFFFFF',
          black: '#000000',
          success: '#10BC69',
          info: '#119CFF',
          'primary-variant-1': '#F9A778',
          'primary-variant-2': '#FFA159',
          'primary-variant-3': '#FFE1B7',
          'secondary-variant-1': '#440106',
          'grey-1': '#DDDCE5',
          'grey-2': '#ABA7BE',
          'grey-3': '#8C849F',
          'grey-4': '#3E3459',
          'grey-5': '#2B2142',
          'grey-6': '#201736',
          'default-content': '#FFFFFF',
          'button-content': '#231047',
          'dialog-fill': '#341B4C',
          'dialog-fill-2': '#26143B',
          divider: '#ffff66',
          'footer-fill': '#0E0E2A',
          'iframe-bar': '#0E0E2A',
          'footer-item': '#4d4545',
          'app-bar-item': '#3E1171',
          'button-icon': '#292222',
          'game-hover': '#5F5870',
          'card-gradient-2': '#1E0968',
          'card-fill': '#2B1543',
          'pagination-fill': '#675BD5',
          'scroll-fill': '#0E0E2A',
          scroll: '#6D3A95',
          'letter-info': '#8E8AFF',
          'footer-button': '#C6C4D4',
          'btn-disable': '#FFFFFF',
          offline: '#ABA7BE',
          private: '#FFFFFF',
          'name-private': '#36bf36',
          'app-bar_line': '#FFCFAA',
          'vip-g-1': '#FEC600',
          'vip-g-2': '#DC1646'
        }
      }
    },
    breakpoint: {
      //與預設不同，md & lg 需考慮到 scrollbar 寬度
      thresholds: {
        xs: 600,
        sm: 960,
        md: 1270,
        lg: 1910
      },
      scrollBarWidth: 6
    }
  }
}
