import moment from 'moment-timezone'

/**
 * Living Room 專案統一時間格式化工具
 *
 * 🎯 設計原則：
 * - 統一入口：所有時間格式化都通過此工具
 * - 零依賴：不依賴 Vue 實例或 Store
 * - 純函數：相同輸入永遠產生相同輸出
 * - 容錯性：提供完整的錯誤處理和後備方案
 *
 * 🚀 使用範例：
 * // 基本時間轉換
 * convertTime.convertISOTime('2024-01-01T10:30:00Z')
 *
 * // 維護時間格式化（推薦用法）
 * const timeConfig = convertTime.getTimeConfig(this)
 * const formatted = convertTime.formatMaintenanceTime(isoTime, timeConfig)
 *
 * // 批量處理維護資料
 * const processed = convertTime.processMaintenanceList(dataList, timeConfig)
 */
const convertTime = {
  convertUnixTime(
    timestamp,
    format = 'YYYY-MM-DD HH:mm:ss',
    timezone = Intl.DateTimeFormat().resolvedOptions().timeZone
  ) {
    if (!timezone) timezone = Intl.DateTimeFormat().resolvedOptions().timeZone
    // 確保輸入的是數字
    const unixTime = Number(timestamp)
    return moment.unix(unixTime).tz(timezone).format(format)
  },
  convertISOTime(
    isoTime,
    format = 'YYYY-MM-DD HH:mm:ss',
    timezone = Intl.DateTimeFormat().resolvedOptions().timeZone
  ) {
    if (!timezone) timezone = Intl.DateTimeFormat().resolvedOptions().timeZone

    // 修正非標準 ISO 時間格式（如 .533.000000Z -> .533Z）
    let normalizedTime = isoTime
    if (typeof isoTime === 'string') {
      // 移除多餘的微秒部分，將 .XXX.XXXXXX 格式轉換為 .XXX
      normalizedTime = isoTime.replace(/(\.\d{3})\.\d{6}(Z?)$/, '$1$2')
    }

    return moment(normalizedTime).tz(timezone).format(format)
  },
  convertGMT8Time(
    localTime,
    format = 'YYYY-MM-DD HH:mm:ss',
    timezone = Intl.DateTimeFormat().resolvedOptions().timeZone
  ) {
    if (!timezone) timezone = Intl.DateTimeFormat().resolvedOptions().timeZone
    return moment.tz(localTime, 'YYYY/MM/DD HH:mm:ss', 'Asia/Taipei').tz(timezone).format(format)
  },
  getGMTOffset(timezone = Intl.DateTimeFormat().resolvedOptions().timeZone) {
    if (!timezone) timezone = Intl.DateTimeFormat().resolvedOptions().timeZone
    const GMT = this.getOffset(timezone)
    return `GMT${GMT >= 0 ? '+' : ''}${GMT}`
  },
  getOffset(timezone) {
    try {
      if (moment.tz.zone(timezone)) {
        // 直接返回小時數
        return moment().tz(timezone).utcOffset() / 60
      } else {
        // 本地時差轉換成小時
        return -new Date().getTimezoneOffset() / 60
      }
    } catch (err) {
      // 錯誤時返回本地時差
      return -new Date().getTimezoneOffset() / 60
    }
  },

  /**
   * 🔧 統一時間配置獲取 - 專案唯一入口
   *
   * @param {Object} vueInstance - Vue 組件實例
   * @returns {Object} 標準化時間配置
   *
   * 📝 使用說明：
   * - 在 Vue 組件中：convertTime.getTimeConfig(this)
   * - 在 Vuex action 中：convertTime.getTimeConfig(context.app)
   * - 如果無法獲取配置，自動使用預設值
   */
  getTimeConfig(vueInstance) {
    const defaultConfig = {
      formatError: 'YYYY-MM-DD HH:mm',
      timezone: 'Asia/Taipei'
    }

    // 直接嘗試從 Vue 實例獲取，取不到就用預設值
    return vueInstance?.$UIConfig?.timeStamp || defaultConfig
  },

  /**
   * 🕐 維護時間專用格式化函數
   *
   * @param {string} isoTimeString - ISO 時間字符串
   * @param {Object} userTimeConfig - 時間配置物件
   * @returns {string} 格式化後的時間字符串
   *
   * 🎯 特點：
   * - 專為維護時間設計
   * - 完整的錯誤處理
   * - 自動後備方案
   */
  formatMaintenanceTime(
    isoTimeString,
    userTimeConfig = { formatError: 'YYYY-MM-DD HH:mm', timezone: 'Asia/Taipei' }
  ) {
    if (!isoTimeString) return ''

    // 驗證並設定預設配置
    const timeConfig = {
      formatError: userTimeConfig?.formatError || 'YYYY-MM-DD HH:mm',
      timezone: userTimeConfig?.timezone || 'Asia/Taipei'
    }

    try {
      const { formatError, timezone } = timeConfig
      const formattedTime = this.convertISOTime(isoTimeString, formatError, timezone)

      return formattedTime
    } catch (primaryError) {
      // 僅在開發環境輸出錯誤日誌
      if (process.env.NODE_ENV === 'development') {
        console.warn('[convertTime] 維護時間格式化錯誤:', {
          isoTimeString,
          timeConfig,
          error: primaryError.message
        })
      }

      // 後備方案：使用預設台北時間
      try {
        const fallbackTime = this.convertISOTime(isoTimeString, 'YYYY-MM-DD HH:mm', 'Asia/Taipei')
        return fallbackTime
      } catch (fallbackError) {
        // 最終後備：返回處理過的原始時間
        if (process.env.NODE_ENV === 'development') {
          console.warn('[convertTime] 後備方案失敗，返回原始時間:', fallbackError.message)
        }
        return isoTimeString
      }
    }
  },

  /**
   * 📦 批量處理維護資料列表 - 僅在真正需要時使用
   *
   * @param {Array} maintenanceDataList - 維護資料列表
   * @param {Object} userTimeConfig - 時間配置
   * @returns {Array} 處理後的維護資料列表
   *
   * ⚠️ 使用建議：
   * - 僅在列表展示時使用
   * - Store 中不建議使用（保持原始資料）
   */
  processMaintenanceList(
    maintenanceDataList,
    userTimeConfig = { formatError: 'YYYY-MM-DD HH:mm', timezone: 'Asia/Taipei' }
  ) {
    if (!Array.isArray(maintenanceDataList)) return maintenanceDataList

    return maintenanceDataList.map((maintenanceItem) => {
      // 支援多種時間欄位格式 - 更具描述性的變量名
      const maintenanceBeginTime = maintenanceItem.beginAt || maintenanceItem.maintainBeginAt
      const maintenanceEndTime = maintenanceItem.endAt || maintenanceItem.maintainEndAt

      return {
        ...maintenanceItem,
        // 統一轉換為 maintainBeginAt 和 maintainEndAt 格式
        maintainBeginAt: this.formatMaintenanceTime(maintenanceBeginTime, userTimeConfig),
        maintainEndAt: this.formatMaintenanceTime(maintenanceEndTime, userTimeConfig)
      }
    })
  }
}

export default convertTime
