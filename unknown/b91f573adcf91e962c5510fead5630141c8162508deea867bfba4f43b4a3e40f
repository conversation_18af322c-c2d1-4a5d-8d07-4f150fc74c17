<template>
  <div class="d-flex">
    <v-container class="noscroll pa-0">
      <v-row v-if="maintainText" no-gutters align="center">
        <v-col cols="12">
          <v-card color="grey-6" elevation="0" class="d-flex justify-center">
            <span class="grey-3--text custom-text-noto text-caption">
              {{ $t('in_maintenance') }}
            </span>
          </v-card>
          <v-card color="grey-6" elevation="0" class="d-flex justify-center mt-2">
            <span class="custom-text-noto text-caption primary-variant-1--text">
              {{ '{0} {1}%'.format($t('expected_value'), defaultRtp) }}
            </span>
          </v-card>
        </v-col>
      </v-row>
      <v-row v-else-if="!rtpStyleObj.showRTPTooltipStatus" no-gutters align="center">
        <v-col cols="12">
          <v-card color="grey-6" elevation="0" class="d-flex justify-center">
            <span class="grey-3--text custom-text-noto text-caption">
              {{ $t('rtp_show_comming_soon') }}
            </span>
          </v-card>
          <v-card color="grey-6" elevation="0" class="d-flex justify-center mt-2">
            <span class="custom-text-noto text-caption primary-variant-1--text">
              {{ '{0} {1}%'.format($t('expected_value'), defaultRtp) }}
            </span>
          </v-card>
        </v-col>
      </v-row>
      <v-row v-else no-gutters align="center">
        <v-col
          :cols="$vuetify.breakpoint.width >= 375 ? 6 : 12"
          :class="{
            'pr-1': $vuetify.breakpoint.width >= 375,
            'pb-2': $vuetify.breakpoint.width >= 375,
            'pb-1': $vuetify.breakpoint.width < 375
          }"
        >
          <v-card color="grey-6" elevation="0" class="d-flex">
            <rtpShow
              text="single_daily"
              background-color="grey-6"
              :rtp="dailyRtp"
              :icon="rtpStyleObj.dailyIcon"
              :icon-color="rtpStyleObj.dailyIconColor"
              chip-text-color="default-content"
            />
          </v-card>
        </v-col>
        <v-col
          :cols="$vuetify.breakpoint.width >= 375 ? 6 : 12"
          :class="{
            'pl-1': $vuetify.breakpoint.width >= 375,
            'pb-2': $vuetify.breakpoint.width >= 375,
            'pb-1': $vuetify.breakpoint.width < 375
          }"
        >
          <v-card color="grey-6" elevation="0" class="d-flex">
            <rtpShow
              text="single_weekly"
              background-color="grey-6"
              :rtp="weeklyRtp"
              :icon="rtpStyleObj.weeklyIcon"
              :icon-color="rtpStyleObj.weeklyIconColor"
              chip-text-color="default-content"
            />
          </v-card>
        </v-col>
        <v-col
          :cols="$vuetify.breakpoint.width >= 375 ? 6 : 12"
          :class="{
            'pr-1': $vuetify.breakpoint.width >= 375,
            'pb-1': $vuetify.breakpoint.width < 375
          }"
        >
          <v-card color="transparent" class="grey-6 d-flex" elevation="0">
            <rtpShow
              text="single_monthly"
              background-color="grey-6"
              :rtp="monthlyRtp"
              :icon="rtpStyleObj.monthlyIcon"
              :icon-color="rtpStyleObj.monthlyIconColor"
              chip-text-color="default-content"
            />
          </v-card>
        </v-col>
        <v-col
          :cols="$vuetify.breakpoint.width >= 375 ? 6 : 12"
          :class="{
            'pl-1': $vuetify.breakpoint.width >= 375
          }"
        >
          <v-card class="d-flex" elevation="0">
            <v-chip
              class="rtp-chip px-2 ma-0 w-100 custom-text-noto text-body-2"
              color="grey-6"
              small
              label
              text-color="default-content"
            >
              <p class="w-100 mb-0 d-flex align-center justify-center">
                <span class="custom-text-noto text-caption primary-variant-1--text">
                  {{ '{0} {1}%'.format($t('expected_value'), defaultRtp) }}
                </span>
              </p>
            </v-chip>
          </v-card>
        </v-col>
      </v-row>
      <v-row class="mt-1" no-gutters>
        <v-col cols="12">
          <span
            v-if="!rtpStyleObj.showRTPTooltipStatus || maintainText"
            class="custom-text-noto text-caption default-content--text"
            >{{ $t('expected_value_explanation_noty1') }}
          </span>
          <span v-else class="custom-text-noto text-caption default-content--text">{{
            $t('expected_value_explanation_noty1') + ' ' + $t('expected_value_explanation_noty2')
          }}</span>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>
<script>
  const STATION = process.env.STATION
  export default {
    name: 'RtpDashBoad',
    components: {
      rtpShow: () => import(`~/components_station/${STATION}/rtp/rtpShow`)
    },
    props: {
      dailyRtp: {
        type: Number,
        default: () => {}
      },
      weeklyRtp: {
        type: Number,
        default: () => {}
      },
      monthlyRtp: {
        type: Number,
        default: () => {}
      },
      rtpStyleObj: {
        type: Object,
        default: () => {}
      },
      defaultRtp: {
        type: Number,
        default: 0
      },
      iconColor: {
        type: String,
        default: 'grey-3--text'
      },
      noExperienceText: {
        type: Boolean,
        default: false
      },
      hasRobotText: {
        type: Boolean,
        default: true
      },
      maintainText: {
        type: Boolean,
        default: false
      }
    }
  }
</script>
