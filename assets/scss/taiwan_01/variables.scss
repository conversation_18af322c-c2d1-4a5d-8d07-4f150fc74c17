$colors: (
  primary: #e9b950,
  secondary: #ac2335,
  warning: #ff9f21,
  error: #f62c2c,
  white: #ffffff,
  black: #000000,
  success: #4caf50,
  info: #4176fa,
  primary-variant-1: #fea600,
  primary-variant-2: #f7b675,
  primary-variant-3: #ffe7bd,
  secondary-variant-1: #440106,
  grey-1: #e6e1e1,
  grey-2: #b2aaaa,
  grey-3: #a38989,
  grey-4: #4c3535,
  grey-5: #402222,
  grey-6: #291515,
  default-content: #fbfbfb,
  button-content: #1a0508,
  dialog-fill: #552f31,
  dialog-fill-2: #472426,
  divider: #ffffff66,
  footer-fill: #000000,
  iframe-bar: #1a0508,
  footer-item: #4d4545,
  app-bar-item: #440106,
  button-icon: #292222,
  game-hover: #4d4545,
  card-gradient-2: #440106,
  card-fill: #440106,
  pagination-fill: #ac2335,
  scroll-fill: #331c1c,
  scroll: #886d6d,
  letter-info: #8fafff,
  footer-button: #b2aaaa,
  btn-disable: #ffffff4d,
  offline: #b2aaaa,
  private: #ffffff,
  name-private: #36bf36,
  black-with-opacity-20: #00000033,
  black-with-opacity-50: rgba(0, 0, 0, 0.5),
  // replaceColor
  title-soft: #fbfbfb,
  title-character-card: #e9b950,
  text-character-card: #fbfbfb,
  text-soft: rgba(251, 251, 251, 0.7),
  text-regular: #fbfbfb,
  text-medium: #e9b950,
  text-heavy: #fea600,
  text-list: #fbfbfb,
  text-list-focused: #e9b950,
  text-dialog-header: #e6e1e1,
  text-dialog-header-inverse: #1a0b0b,
  text-btn: #1a0b0b,
  text-btn-heavy: #1a0b0b,
  text-marquee-medium: #e9b950,
  text-marquee: #fbfbfb,
  text-app-awards: #f7b675,
  text-app-awards-soft: #fbfbfb,
  text-app-awards-medium: #e9b950,
  text-game-square-rtp: #ffffff,
  text-game-promote-rtp: #ffffff,
  text-game-square-chip: #fbfbfb,
  text-pwa: #1a0b0b,
  text-cookie: #fbfbfb,
  text-cookie-link: #e9b950,
  text-field-focused: #e9b950,
  text-field-error: #f62c2c,
  text-gift-pack: #fbfbfb,
  text-gift-pack-medium: #e9b950,
  text-gift-pack-btn: #ffffff,
  bg-text-field: #552f31,
  bg-dialog: #552f31,
  bg-dialog-medium: #402222,
  bg-btn: #fea600,
  bg-switch: #e9b950,
  bg-menu: #472426,
  bg-app-awards-title-dot: #e9b950,
  bg-app-awards-title-dot-soft: rgba(233, 185, 80, 0.2),
  bg-card: #440106,
  bg-table-medium: #4c3535,
  bg-table-hover: #331c1c,
  bg-table: #291515,
  tab-focused: #e9b950,
  btn-regular: #e9b950,
  btn-soft: #fbfbfb,
  btn-dialog-inverse: #1a0b0b,
  btn-chat: #fea600,
  border-regular: #f7b675,
  border-marquee: rgba(255, 255, 255, 0.5),
  decorate-border-gift-pack: #f7b675,
  bg-scrollbar: #331c1c,
  scrollbar-regular: #886d6d,
  btn-iframe-bar: #ffffff,
  bg-iframe-bar: #1a0b0b,
  bg-slider-track: #e9b950,
  bg-slider-thumb: #e9b950,
  text-slider: #fbfbfb,
  text-slider-hover: #e9b950,
  bg-pagination: #ac2335,
  bg-pagination-focus: #fea600,
  text-pagination: #ffffff,
  text-pagination-focused: #1a0b0b,
  bg-app-coin: rgba(68, 1, 6, 0.5),
  bg-app-coin-section: #440106,
  text-app-coin: #fbfbfb,
  bg-badge: #440106,
  bg-badge-error: #f62c2c,
  text-badge: #fbfbfb,
  text-success: #4caf50,
  text-member-level: #a38989,
  bg-badge-success: #4caf50,
  border-badge: #552f31,
  bg-progress-determinate: #fea600,
  text-progress: #fbfbfb,
  border-dialog: #e9b950,
  text-list-disable: rgba(255, 255, 255, 0.3),
  bg-progress: rgba(255, 231, 189, 0.4),
  bg-dialog-section-soft: rgba(85, 47, 49, 0.6),
  bg-badge-disable: #b2aaaa,
  text-datetime-picker-medium: #fea600,
  text-datetime-picker-btn-focused: #1a0b0b,
  btn-datetime-picker: #f7b675,
  btn-datetime-picker-medium: #fea600,
  btn-datetime-picker-heavy: #1a0b0b,
  bg-datetime-picker: #552f31,
  bg-datetime-picker-btn-focused: #fea600,
  btn-datetime-picker-disable: rgba(255, 255, 255, 0.3),
  bg-datetime-picker-soft: rgba(255, 255, 255, 0.3) // replaceColor end
) !default;

//replaceColor
//選單
$bg-menu: map-get($colors, bg-menu);
$text-list: map-get($colors, text-list);
$text-list-focused: map-get($colors, text-list-focused);
//按鈕
$btn-soft: map-get($colors, btn-soft);
$btn-regular: map-get($colors, btn-regular);
$bg-btn: map-get($colors, bg-btn);
$btn-dialog-inverse: map-get($colors, btn-dialog-inverse);
$text-btn-heavy: map-get($colors, text-btn-heavy);
$btn-chat: map-get($colors, btn-chat);
//文字、icon
$text-regular: map-get($colors, text-regular);
$title-soft: map-get($colors, title-soft);
$title-character-card: map-get($colors, title-character-card);
$text-character-card: map-get($colors, text-character-card);
$text-soft: map-get($colors, text-soft);
$text-medium: map-get($colors, text-medium);
$text-heavy: map-get($colors, text-heavy);
$text-dialog-header: map-get($colors, text-dialog-header);
$text-dialog-header-inverse: map-get($colors, text-dialog-header-inverse);
$text-btn: map-get($colors, text-btn);
$text-marquee-medium: map-get($colors, text-marquee-medium);
$text-marquee: map-get($colors, text-marquee);
$text-app-awards: map-get($colors, text-app-awards);
$text-app-awards-soft: map-get($colors, text-app-awards-soft);
$text-app-awards-medium: map-get($colors, text-app-awards-medium);
$text-game-square-rtp: map-get($colors, text-game-square-rtp);
$text-game-promote-rtp: map-get($colors, text-game-promote-rtp);
$text-game-square-chip: map-get($colors, text-game-square-chip);
$text-pwa: map-get($colors, text-pwa);
$text-cookie: map-get($colors, text-cookie);
$text-cookie-link: map-get($colors, text-cookie-link);
$text-field-focused: map-get($colors, text-field-focused);
$text-field-error: map-get($colors, text-field-error);
$text-gift-pack: map-get($colors, text-gift-pack);
$text-gift-pack-medium: map-get($colors, text-gift-pack-medium);
$text-gift-pack-btn: map-get($colors, text-gift-pack-btn);
$bg-text-field: map-get($colors, bg-text-field);
$bg-app-awards-title-dot: map-get($colors, bg-app-awards-title-dot);
$bg-app-awards-title-dot-soft: map-get($colors, bg-app-awards-title-dot-soft);
$bg-card: map-get($colors, bg-card);
$bg-table-medium: map-get($colors, bg-table-medium);
$bg-table-hover: map-get($colors, bg-table-hover);
$bg-table: map-get($colors, bg-table);
//dialog
$bg-dialog: map-get($colors, bg-dialog);
$bg-dialog-medium: map-get($colors, bg-dialog-medium);
//右滑開關
$bg-switch: map-get($colors, bg-switch);
//tab
$tab-focused: map-get($colors, tab-focused);
//邊框
$border-regular: map-get($colors, border-regular);
$border-marquee: map-get($colors, border-marquee);
$decorate-border-gift-pack: map-get($colors, decorate-border-gift-pack);
//scrollbar
$bg-scrollbar: map-get($colors, bg-scrollbar);
$scrollbar-regular: map-get($colors, scrollbar-regular);
$btn-iframe-bar: map-get($colors, btn-iframe-bar);
$bg-iframe-bar: map-get($colors, bg-iframe-bar);
$bg-slider-track: map-get($colors, bg-slider-track);
$bg-slider-thumb: map-get($colors, bg-slider-thumb);
$text-slider: map-get($colors, text-slider);
$text-slider-hover: map-get($colors, text-slider-hover);
$bg-pagination: map-get($colors, bg-pagination);
$bg-pagination-focus: map-get($colors, bg-pagination-focus);
$text-pagination: map-get($colors, text-pagination);
$text-pagination-focused: map-get($colors, text-pagination-focused);
$bg-app-coin: map-get($colors, bg-app-coin);
$bg-app-coin-section: map-get($colors, bg-app-coin-section);
$text-app-coin: map-get($colors, text-app-coin);
$bg-badge: map-get($colors, bg-badge);
$bg-badge-error: map-get($colors, bg-badge-error);
$text-badge: map-get($colors, text-badge);
$text-success: map-get($colors, text-success);
$text-member-level: map-get($colors, text-member-level);
$bg-badge-success: map-get($colors, bg-badge-success);
$border-badge: map-get($colors, border-badge);
$bg-progress-determinate: map-get($colors, bg-progress-determinate);
$text-progress: map-get($colors, text-progress);
$border-dialog: map-get($colors, border-dialog);
$text-list-disable: map-get($colors, text-list-disable);
$bg-progress: map-get($colors, bg-progress);
$bg-dialog-section-soft: map-get($colors, bg-dialog-section-soft);
$bg-badge-disable: map-get($colors, bg-badge-disable);
$text-datetime-picker-medium: map-get($colors, text-datetime-picker-medium);
$text-datetime-picker-btn-focused: map-get($colors, text-datetime-picker-btn-focused);
$btn-datetime-picker: map-get($colors, btn-datetime-picker);
$btn-datetime-picker-medium: map-get($colors, btn-datetime-picker-medium);
$btn-datetime-picker-heavy: map-get($colors, btn-datetime-picker-heavy);
$bg-datetime-picker: map-get($colors, bg-datetime-picker);
$bg-datetime-picker-btn-focused: map-get($colors, bg-datetime-picker-btn-focused);
$btn-datetime-picker-disable: map-get($colors, btn-datetime-picker-disable);
$bg-datetime-picker-soft: map-get($colors, bg-datetime-picker-soft);
