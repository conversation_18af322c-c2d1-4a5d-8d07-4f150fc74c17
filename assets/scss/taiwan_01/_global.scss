$primary-color: map-get($colors, primary);
$secondary-color: map-get($colors, secondary);
$white-color: map-get($colors, white);
$grey-1-color: map-get($colors, grey-1);
$grey-5-color: map-get($colors, grey-5);
$grey-6-color: map-get($colors, grey-6);
$pv2-color: map-get($colors, primary-variant-2);
$pv3-color: map-get($colors, primary-variant-3);
$scroll-fill-color: map-get($colors, scroll-fill);
$scroll-color: map-get($colors, scroll);
$button-content-color: map-get($colors, button-content);
$dialog-fill-color: map-get($colors, dialog-fill);
$app-bar-item-color: map-get($colors, app-bar-item);
$card-fill-color: map-get($colors, card-fill);
$divider-color: map-get($colors, divider);
$btn-disable-color: map-get($colors, btn-disable);

* {
  font-family: "Noto Sans TC", sans-serif;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    background-color: $bg-scrollbar;
  }

  &::-webkit-scrollbar-thumb {
    background-color: $scrollbar-regular;
  }

  &::-webkit-scrollbar-corner {
    background-color: transparent;
  }
}

.scrollbar-thin::-webkit-scrollbar,
.scrollbar-thin *::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

body,
html {
  font-family: "Noto Sans TC", sans-serif;
  position: relative;
  height: auto;
}

a,
.link {
  color: $primary-color !important;
  color: $text-medium !important;
  cursor: pointer;
}

.cursor-pointer {
  cursor: pointer;
}

.cursor-default {
  cursor: default;
}

.scrollable {
  overflow-y: auto;
  height: 100%;
}
.noscroll {
  overflow: hidden !important;
}

.word-break-all {
  word-break: break-all;
}

//深色主題 客製化
body .theme--dark.v-application {
  background: linear-gradient(180deg, #2a0909 0%, #6a132d 51.04%, #2a0909 100%);
}
.v-application {
  .v-dialog {
    background: $dialog-fill-color;
    //replaceColor
    background: $bg-dialog;
  }
  .theme--dark.v-list {
    background: $dialog-fill-color;
  }
  .theme--dark.v-tabs-items {
    background: $grey-6-color;
  }
  // 分頁元件
  .theme--dark.v-pagination {
    .v-pagination__item,
    .v-pagination__navigation {
      background-color: $secondary-color;
      //replaceColor
      background-color: $bg-pagination !important;
      color: $text-pagination !important;
    }
    .v-pagination__item--active {
      color: $button-content-color;
      //replaceColor
      background-color: $bg-pagination-focus !important;
      color: $text-pagination-focused !important;
    }
  }

  .theme--dark.v-divider {
    border-color: $divider-color;
  }
  //v-textarea 為了避免內容跟title連在一起 將margin-top調高
  .v-textarea.v-text-field--enclosed textarea {
    margin-top: 30px;
  }

  .player-menu-mobilebar {
    width: 250px;
  }

  .player-menu-playerinfo {
    width: 300px;
  }

  .w-100 {
    width: 100%;
  }

  .h-100-percent {
    height: 100% !important;
  }

  .mw-75-v {
    min-width: 75vw;
  }

  .mh-auto {
    min-height: auto;
  }

  //replaceColor
  //v-footer彈出的v-menu
  .v-menu__content.menuable__content__active .v-list {
    background-color: $bg-menu !important;
    &-item:not(.v-list-item--active):not(.v-list-item--disabled) {
      color: $text-list !important;
    }
    &-item--active {
      color: $text-list-focused !important;
    }
  }

  //vuetify config無法設opacity 故手動設定opacity樣式名稱
  .btn-disable--text {
    color: $btn-disable-color !important;
    caret-color: $btn-disable-color !important;
  }
  .text-soft--text {
    color: rgba(251, 251, 251, 0.7) !important;
  }
  .text-disable--text {
    color: rgba(255, 255, 255, 0.3) !important;
  }
  .bg-dialog-section {
    background-color: rgba(129, 101, 85, 0.75) !important;
  }
  .border-marquee {
    border-color: rgba(255, 255, 255, 0.5) !important;
  }
  .bg-app-awards-title-dot-soft {
    background-color: rgba(233, 185, 80, 0.2) !important;
  }
  .bg-pale {
    background-color: rgba(255, 255, 255, 0.08) !important;
  }
  .bg-game-promote-rtp {
    background-color: rgba(0, 0, 0, 0.3) !important;
  }
  .bg-cookie {
    background-color: rgba(0, 0, 0, 0.9) !important;
  }
}
//data table第一筆資料不要有bofder-radius, data-footer不要有margin
.v-data-table {
  & > .v-data-table__wrapper tbody tr:first-child:hover {
    td:last-child {
      border-top-right-radius: 0 !important;
    }
    td:first-child {
      border-top-left-radius: 0 !important;
    }
  }
  .v-data-footer {
    margin-right: 0 !important;
  }
}
.easyPlayer-custom-border.v-menu__content {
  border-radius: 10px;
}

.chat-alert-fill {
  background: rgba(129, 101, 85, 0.75);
}
.v-tooltip__content {
  &.tooltip-content-custom {
    width: max-content !important;
    max-width: 400px !important;
    font-weight: 400 !important;
    @media screen and (max-width: 432px) {
      max-width: calc(100% - 32px) !important;
    }
    // 內容寬度<400，且靠視窗左側
    &.left-position {
      left: 16px !important;
      right: initial !important;
    }
    // 內容寬度<400，且靠視窗右側
    &.right-position {
      left: initial !important;
      right: 16px !important;
    }
    // 內容寬度>400，距離視窗左右16px
    &.full-position {
      left: 16px !important;
      right: 16px !important;
    }
  }
}
.input-height {
  // 修復 v-text-field 字體被切掉的問題
  &.v-text-field--dense .v-input__control {
    min-height: 40px !important;
  }
  &.v-text-field--outlined .v-input__control {
    min-height: 40px !important;
  }
  &.v-text-field--dense .v-text-field__details {
    min-height: 16px !important;
  }
  &.v-text-field input {
    line-height: 1.5 !important;
    padding: 8px 0 !important;
  }
  &.v-text-field--outlined.v-text-field--dense .v-label {
    top: 6px !important;
  }
  &.v-text-field--outlined.v-text-field--dense.v-text-field--placeholder .v-label {
    top: 6px !important;
  }
}

.v-text-field,
.v-textarea {
  // Focus 狀態
  &.v-input--is-focused {
    caret-color: $text-field-focused !important;
    .v-label,
    .v-text-field__prefix,
    .v-input__slot::before,
    .v-input__slot::after,
    .v-messages__message {
      color: $text-field-focused !important;
    }
    .v-input__icon {
      .v-icon {
        color: $text-field-focused !important;
      }
    }
  }
  // Error 狀態
  &.v-input--has-state.error--text {
    caret-color: $text-field-error !important;
    .v-label,
    .v-text-field__prefix,
    .v-input__slot::before,
    .v-input__slot::after,
    .v-messages__message {
      color: $text-field-error !important;
    }
    .v-input__icon {
      .v-icon {
        color: $text-field-error !important;
      }
    }
  }
}

.text-iframe--text {
  color: rgba(255, 255, 255, 0.4) !important;
}
.bg-iframe-section {
  background-color: rgba(0, 0, 0, 0.5) !important;
}
.bg-slider {
  background: rgba(255, 255, 255, 0.2) !important;
}
.bg-app-coin {
  background-color: rgba(68, 1, 6) !important;
}
.text-list-disable--text {
  color: rgba(255, 255, 255, 0.3) !important;
}
.bg-progress {
  background-color: rgba(255, 231, 189, 0.4) !important;
}
.bg-dialog-section-soft {
  background-color: rgba(85, 47, 49, 0.6) !important;
}
.btn-datetime-picker-disable {
  background-color: rgba(255, 255, 255, 0.3) !important;
}
.bg-datetime-picker-soft {
  background-color: rgba(255, 255, 255, 0.3) !important;
}
