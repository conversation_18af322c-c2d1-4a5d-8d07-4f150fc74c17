$pv1-color: map-get($colors, primary-variant-1);
$pv2-color: map-get($colors, primary-variant-2);
$pv3-color: map-get($colors, primary-variant-3);
$grey-1-color: map-get($colors, grey-1);
$grey-2-color: map-get($colors, grey-2);
$grey-3-color: map-get($colors, grey-3);
$grey-4-color: map-get($colors, grey-4);
$grey-5-color: map-get($colors, grey-5);
$primary-color: map-get($colors, primary);
$bg-card: map-get($colors, bg-card);
$bg-app-coin-section: map-get($colors, bg-app-coin-section);

.gradient-primary {
  background: linear-gradient(180deg, #ffe7bd 0%, #f7b675 100%);
}

.gradient-primary-reverse {
  background: linear-gradient(180deg, #f7b675 0%, #ffe7bd 100%);
}

.gradient-primary-left {
  background: linear-gradient(270deg, #ffe7bd 0%, #fea600 100%);
}

.gradient-light-primary {
  background: linear-gradient(180deg, rgba(102, 191, 255, 0.8) 0%, rgba(65, 118, 250, 1) 100%);
}

.gradient-white-card {
  background: linear-gradient(180deg, $white-color 29.17%, $grey-4-color 92.71%, $grey-5-color 100%);
}

.gradient-button {
  background: linear-gradient(180deg, #ffe7bd 0%, #f7b675 100%);
}

.gradient-game-maintenance {
  background: linear-gradient(180deg, rgba(153, 144, 144, 0.5) 0%, #211616 100%);
}

.gradient-primary-box {
  background: linear-gradient(270.47deg, #402222 -0.45%, #440106 99.59%);
}

.gradient-title--text {
  background: linear-gradient(180deg, #ffe7bd 0%, #f7b675 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  -webkit-box-decoration-break: clone;
  box-decoration-break: clone;
}
.gradient-subtitle--text {
  background: linear-gradient(180deg, $grey-2-color 61.46%, $grey-4-color 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25);
}

.gradient-primary--text {
  background: linear-gradient(180deg, #ffe7bd 0%, #f7b675 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  border-radius: 4px;
}

.gradient-primary-border {
  border: 3px solid;
  border-image: linear-gradient(180deg, rgba(255, 231, 189, 1), rgba(247, 182, 117, 1)) 1;
}

.gradient-app-icon {
  background: linear-gradient(90deg, #790514 0%, rgba(121, 5, 20, 0.2) 100%);
}

.app-bar-gradient {
  background: linear-gradient(180deg, #8f071b 0%, #500001 100%);
}
.bottom-bar-gradient {
  background: linear-gradient(180deg, #8f071b 0%, #500001 100%);
}
.app-bar-button {
  background: linear-gradient(180deg, #ffe7bd 0%, #f7b675 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.border-line {
  width: 100%;
  height: 100%;
  border: solid 0.14159rem transparent;
  background-image: linear-gradient(0deg, #440106, #440106),
    linear-gradient(rgba(233, 185, 79, 1) 0%, rgba(255, 255, 255, 1) 50%, rgba(233, 185, 79, 1) 100%);
  background-origin: border-box;
  background-clip: content-box, border-box;
}

.card-gradient-1 {
  background: linear-gradient(180deg, #ac2335 29.17%, #550d10 92.71%, #a0574c 100%);
}

.background {
  background: linear-gradient(180deg, #2a0909 0%, #6a132d 51.04%, #2a0909 100%);
}

.info-card {
  background: linear-gradient(180deg, #4c3535 0%, #331c1c 100%);
}

.vip-title {
  background: linear-gradient(180deg, #fe7a00 0%, #ac2335 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-game-maintenance-2 {
  background: linear-gradient(180deg, rgba(153, 144, 144, 0) 0%, #211616 100%);
}

.gradient-side-bar-game {
  background: linear-gradient(100deg, #ffcc9b 25.77%, #fe9b4e 71.04%, #fd6a00 103.52%);
}

.gift-pack-reward-card-fill {
  background: linear-gradient(
    270deg,
    rgba(172, 35, 53, 0) 0%,
    rgba(172, 35, 53, 0.5) 12.86%,
    #c51d33 50%,
    rgba(172, 35, 53, 0.5) 85%,
    rgba(172, 35, 53, 0) 100%
  );
}
.gift-pack-reward-card-fill-vip {
  background: linear-gradient(
    270deg,
    rgba(239, 136, 0, 0) 0%,
    rgba(239, 136, 0, 0.75) 30%,
    #ef8800 50%,
    rgba(239, 136, 0, 0.75) 70%,
    rgba(239, 136, 0, 0) 100%
  );
}

.gift-pack-frame {
  background: linear-gradient(
    270deg,
    rgba(237, 193, 167, 0) 0%,
    rgba(237, 193, 167, 0.8) 51%,
    rgba(237, 193, 167, 0) 100%
  );
}
.gift-pack-frame-vip {
  background: linear-gradient(
    270deg,
    rgba(199, 44, 113, 0) 0%,
    rgba(199, 44, 113, 0.8) 51%,
    rgba(199, 44, 113, 0) 100%
  );
}

.gift-pack-price-fill {
  background: linear-gradient(0deg, #1a0b0b00 0%, #1a0b0b80 100%);
}

.game-intro-grade-bg {
  background: linear-gradient(180deg, #a76849 0%, rgba(116, 70, 46, 0.4) 50%, rgba(136, 90, 67, 0) 100%) !important;
}

.game-intro-grade-border {
  background: linear-gradient(90deg, #673f3c 0%, #7c5448 28%, rgba(126, 86, 74, 0.2) 76%, rgba(107, 73, 64, 0.2) 100%);
  &::before {
    border: 2px solid;
    border-image: linear-gradient(
        to right bottom,
        #d8c6bd 2.02%,
        #8b624e 51.01%,
        rgba(116, 90, 78, 0.2) 75.5%,
        #c7b9b2 87.75%,
        rgba(116, 90, 78, 0.2) 100%
      )
      1;
  }
}

.mail-attachment-gradient-bg {
  background: linear-gradient(180deg, #a76849 0%, rgba(151, 97, 70, 0.4) 50%, rgba(136, 90, 67, 0) 100%);
}

//replaceColor start
.bg-dialog-header {
  background: linear-gradient(270deg, #ffe7bd 0%, #fea600 100%);
}
.bg-marquee {
  background: linear-gradient(90deg, #6b0500 0%, #aa3313 50%, #c74f1a 100%);
}
.title-heavy--text {
  background: linear-gradient(180deg, #ffe7bd 0%, #f7b675 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  border-radius: 4px;
}
.overlay-game-square {
  background: linear-gradient(180deg, rgba(153, 144, 144, 0.5) 0%, #211616 100%);
}
.overlay-game-promote {
  background: linear-gradient(180deg, rgba(153, 144, 144, 0) 0%, #211616 100%);
}
.bg-btn-heavy {
  background: linear-gradient(180deg, #ffe7bd 0%, #f7b675 100%);
}
.bg-game-square-chip {
  background: linear-gradient(180deg, #f7b675 0%, #ffe7bd 100%);
}
.bg-character-card {
  background: linear-gradient(270deg, #402222 0%, #440106 100%);
}
.border-medium {
  border: 3px solid;
  border-image: linear-gradient(180deg, #ffe7bd 0%, #f7b675 100%) 1;
}
.border-title-right {
  border-image-source: linear-gradient(270deg, #f7b675 0%, #f7b67500 100%) !important;
  -webkit-border-image-source: linear-gradient(270deg, #f7b675 0%, #f7b67500 100%) !important;
  -moz-border-image-source: linear-gradient(270deg, #f7b675 0%, #f7b67500 100%) !important;
  -ms-border-image-source: linear-gradient(270deg, #f7b675 0%, #f7b67500 100%) !important;
  border-image-slice: 1;
  -webkit-border-image-slice: 1;
  -moz-border-image-slice: 1;
  -ms-border-image-slice: 1;
  transform: rotate(180deg);
}
.border-title-left {
  border-image-source: linear-gradient(270deg, #f7b67500 0%, #f7b675 100%) !important;
  -webkit-border-image-source: linear-gradient(270deg, #f7b67500 0%, #f7b675 100%) !important;
  -moz-border-image-source: linear-gradient(270deg, #f7b67500 0%, #f7b675 100%) !important;
  -ms-border-image-source: linear-gradient(270deg, #f7b67500 0%, #f7b675 100%) !important;
  border-image-slice: 1;
  -webkit-border-image-slice: 1;
  -moz-border-image-slice: 1;
  -ms-border-image-slice: 1;
  transform: rotate(180deg);
}
.bg-card-border {
  width: 100%;
  height: 100%;
  border: solid 0.14159rem transparent;
  background-image: linear-gradient(0deg, $bg-card, $bg-card), linear-gradient(#e9b950 0%, #ffffff 50%, #e9b950 100%) !important;
  background-origin: border-box;
  background-clip: content-box, border-box;
}
.bg-gift-pack-vip-section {
  background: linear-gradient(
    270deg,
    rgba(239, 136, 0, 0) 0%,
    rgba(239, 136, 0, 0.75) 30%,
    #ef8800 50%,
    rgba(239, 136, 0, 0.75) 70%,
    rgba(239, 136, 0, 0) 100%
  );
}
.bg-gift-pack-section {
  background: linear-gradient(
    270deg,
    rgba(172, 35, 53, 0) 0%,
    rgba(172, 35, 53, 0.5) 12.86%,
    #c51d33 50%,
    rgba(172, 35, 53, 0.5) 85%,
    rgba(172, 35, 53, 0) 100%
  );
}
.bg-gift-pack-section-soft {
  background: linear-gradient(360deg, rgba(26, 11, 11, 0) 0%, rgba(26, 11, 11, 0.5) 100%);
}
.bg-app-coin-border {
  width: 100%;
  height: 100%;
  border: solid 0.14159rem transparent;
  background-origin: border-box;
  background-clip: content-box, border-box;
  &.bg-card {
    background-image: linear-gradient(0deg, $bg-card, $bg-card), linear-gradient(#e9b950 0%, #ffffff 50%, #e9b950 100%) !important;
  }
  &.bg-app-coin-section {
    background-image: linear-gradient(0deg, $bg-app-coin-section, $bg-app-coin-section),
      linear-gradient(#e9b950 0%, #ffffff 50%, #e9b950 100%) !important;
  }
}
.bg-app-coin-section-heavy {
  background: linear-gradient(90deg, #790514 0%, rgba(121, 5, 20, 0.2) 100%);
}
.title-member-level {
  background: linear-gradient(180deg, #fe7a00 0%, #ac2335 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
.border-badge-heavy {
  background: linear-gradient(to bottom, #e9b950, #ffffff, #e9b950) !important;
}
.bg-datetime-picker-heavy {
  background: linear-gradient(270deg, #ffe7bd 0%, #fea600 100%) !important;
}
//replaceColor end
