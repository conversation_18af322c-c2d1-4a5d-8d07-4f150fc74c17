
$info-color: map-get($colors, info);

.notification {
  display: flex;
  padding: 16px;
  margin: 5px 5px;
  font-size: 16px;
  color: white;
  position: relative;
  border-radius: 24px 4px;
  align-items: center;
  gap: 8px;
  background-color: map-get($colors, primary);

  &::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
  }

  i {
    color: white;
  }

  .notification-title,
  .notification-content {
    color: white;
    display: inline-block;
    word-break: break-word;
  }

  &.notification--success {
    background: linear-gradient(0deg, rgba(76, 175, 80, 0.12), rgba(76, 175, 80, 0.12)), #FFFFFF;
    .notification-title,
    .notification-content,
    i {
        color: #4CAF50;
      }
  }

  &.notification--info {
    background: linear-gradient(0deg, rgba(65, 118, 250, 0.12), rgba(65, 118, 250, 0.12)), #FFFFFF;
    .notification-title,
    .notification-content,
    i {
        color: $info-color;
      }
  }

  &.notification--error {
    background: linear-gradient(0deg, rgba(231, 26, 91, 0.12), rgba(231, 26, 91, 0.12)), #FFFFFF;
    .notification-title,
    .notification-content,
    i {
        color: map-get($colors, error);
      }

  }

  &.notification--warning {
    background: linear-gradient(0deg, rgba(255, 121, 23, 0.12), rgba(255, 121, 23, 0.12)), #FFFFFF;
    .notification-title,
    .notification-content,
    i {
        color:  #FF7917;
        ;
      }

  }
}
