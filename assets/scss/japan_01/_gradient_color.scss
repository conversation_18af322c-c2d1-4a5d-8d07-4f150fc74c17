$pv1-color: map-get($colors, primary-variant-1);
$pv2-color: map-get($colors, primary-variant-2);
$pv3-color: map-get($colors, primary-variant-3);
$grey-1-color: map-get($colors, grey-1);
$grey-2-color: map-get($colors, grey-2);
$grey-3-color: map-get($colors, grey-3);
$grey-4-color: map-get($colors, grey-4);
$grey-5-color: map-get($colors, grey-5);
$primary-color: map-get($colors, primary);

.gradient-primary {
  background: linear-gradient(180deg, #ffcfaa 0%, #ff974b 100%);
}

.gradient-primary-reverse {
  background: linear-gradient(180deg, #ff974b 0%, #ffcfaa 100%);
}

.gradient-primary-left {
  background: linear-gradient(270deg, #ffcfaa 0%, #ff974b 100%);
}

.gradient-light-primary {
  background: linear-gradient(180deg, rgba(102, 191, 255, 0.8) 0%, rgba(65, 118, 250, 1) 100%);
}

.gradient-white-card {
  background: linear-gradient(
    180deg,
    $white-color 29.17%,
    $grey-4-color 92.71%,
    $grey-5-color 100%
  );
}

.gradient-button {
  background: linear-gradient(180deg, #ffcfaa 0%, #ff974b 100%);
}

.gradient-game-maintenance {
  background: linear-gradient(180deg, rgba(171, 167, 190, 0.5) 0%, #292632 100%);
}

.gradient-primary-box {
  background: linear-gradient(270deg, #b56dc8 -0.45%, #4d1cb7 99.59%);
}

.gradient-title--text {
  background: linear-gradient(180deg, #ffcfaa 0%, #ff974b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  -webkit-box-decoration-break: clone;
  box-decoration-break: clone;
}
.gradient-subtitle--text {
  background: linear-gradient(180deg, $grey-2-color 61.46%, $grey-4-color 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25);
}

.gradient-primary--text {
  background: linear-gradient(180deg, #fff6aa 0%, #ffed30 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  border-radius: 4px;
}

.gradient-primary-border {
  border: 3px solid;
  border-image: linear-gradient(180deg, rgba(255, 231, 189, 1), rgba(247, 182, 117, 1)) 1;
}

.app-bar-gradient {
  background-color: #231047 !important;
}

.bottom-bar-gradient {
  background: #231047;
}

.app-bar-button {
  background: linear-gradient(180deg, #ffcfaa 0%, #ff974b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.border-line {
  width: 100%;
  height: 100%;
  border: solid 0.14159rem transparent;
  background-image: linear-gradient(0deg, #2b1543, #2b1543),
    linear-gradient(0deg, #ff974b 0%, #fee7d6 50%, #ff974b 100%);
  background-origin: border-box;
  background-clip: content-box, border-box;
}

.card-gradient-1 {
  background: linear-gradient(180deg, #ac2335 29.17%, #550d10 92.71%, #a0574c 100%);
}

.card-gradient-2 {
  background: linear-gradient(180deg, #462370 0%, #1b0e33 100%);
}

.background {
  background: linear-gradient(180deg, #231047 0%, #522b58 67%, #fa4821 100%);
}

.info-card {
  background: linear-gradient(180deg, #9a4eab 0%, #462370 100%);
}

.vip-title {
  background: linear-gradient(180deg, #fec600 0%, #dc1646 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-game-maintenance-2 {
  background: linear-gradient(180deg, rgba(171, 167, 190, 0) 0%, #292632 100%);
}

.gradient-side-bar-game {
  background: linear-gradient(99deg, #9aa3ff -2.56%, #7778da 37.71%, #6031be 109.3%);
}

.game-intro-grade-bg {
  background: linear-gradient(
    180deg,
    #552a80 0%,
    rgba(144, 91, 189, 0.4) 50%,
    rgba(224, 190, 233, 0) 100%
  ) !important;
}

.game-intro-grade-border {
  background: linear-gradient(
    137deg,
    #e9dcf8 -0.44%,
    #6d3a95 49.78%,
    rgba(43, 21, 67, 0.2) 74.89%,
    #e9dcf8 87.45%,
    rgba(43, 21, 67, 0.2) 100%
  );
  &::before {
    border: 2px solid;
    border-image: linear-gradient(
        to right bottom,
        #dad6e4 -0.44%,
        #574e8b 49.78%,
        rgba(90, 78, 116, 0.2) 74.89%,
        #c4bdd8 87.45%,
        rgba(90, 78, 116, 0.2) 100%
      )
      1;
  }
}
.mail-attachment-gradient-bg {
  background: linear-gradient(
    180deg,
    #cd96dc 0%,
    rgba(205, 150, 220, 0.4) 50%,
    rgba(205, 150, 220, 0) 100%
  );
}
.gift-pack-reward-card-fill {
  background: linear-gradient(
    270deg,
    rgba(232, 159, 41, 0) 0%,
    rgba(232, 159, 41, 0.5) 12.86%,
    #e89f29 50%,
    rgba(232, 159, 41, 0.5) 85%,
    rgba(232, 159, 41, 0) 100%
  );
}

.gift-pack-frame {
  background: linear-gradient(
    270deg,
    rgba(193, 153, 231, 0) 0%,
    rgba(193, 153, 231, 0.8) 51%,
    rgba(193, 153, 231, 0) 100%
  );
}
.gift-pack-frame-vip {
  background: linear-gradient(
    270deg,
    rgba(217, 193, 241, 0) 0%,
    rgba(217, 193, 241, 0.8) 51%,
    rgba(217, 193, 241, 0) 100%
  );
}
