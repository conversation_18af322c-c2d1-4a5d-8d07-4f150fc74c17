$colors: (
  primary: #ffed30,
  secondary: #6923ff,
  warning: #ff8329,
  error: #f62c2c,
  white: #ffffff,
  black: #000000,
  success: #05c753,
  info: #2253ff,
  primary-variant-1: #ffc700,
  primary-variant-2: #fff85b,
  primary-variant-3: #fff6aa,
  secondary-variant-1: #440106,
  grey-1: #e2e2e8,
  grey-2: #adafc2,
  grey-3: #898ca3,
  grey-4: #3d4056,
  grey-5: #1c255f,
  grey-6: #0d1234,
  default-content: #ffffff,
  default-content-1: #ffffff,
  button-content: #050a1a,
  dialog-fill: #151050,
  dialog-fill-2: #020b3e,
  divider: #ffffff66,
  footer-fill: #0c105b,
  iframe-bar: #09051a,
  footer-item: #4d4545,
  app-bar-item: #0c105b,
  button-icon: #292222,
  game-hover: #48454d,
  card-gradient-2: #1e0968,
  card-fill: #1e0968,
  pagination-fill: #6923ff,
  scroll-fill: #27235e,
  scroll: #3e35a8,
  letter-info: #8fafff,
  footer-button: #bbbfc9,
  btn-disable: #ffffff4d,
  offline: #b2aaaa,
  private: #ffffff,
  name-private: #36bf36,
  app-bar_line: #ffed30,
  vip-g-1: #fec600,
  vip-g-2: #dc1646,
  black-with-opacity-20: #00000033,
  black-with-opacity-50: rgba(0, 0, 0, 0.5),
  // replaceColor
  title-soft: #ffffff,
  text-soft: rgba(255, 255, 255, 0.7),
  text-regular: #ffffff,
  text-medium: #ffed30,
  text-heavy: #ffc700,
  text-list: #ffffff,
  text-list-focused: #ffed30,
  text-dialog-header: #e2e2e8,
  text-dialog-header-inverse: #09051a,
  text-btn: #09051a,
  text-btn-heavy: #09051a,
  text-marquee-medium: #ffed30,
  text-marquee: #ffffff,
  text-app-awards: #fff85b,
  text-app-awards-soft: #ffffff,
  text-app-awards-medium: #ffed30,
  text-game-square-rtp: #ffffff,
  text-game-promote-rtp: #ffffff,
  text-game-square-chip: #1c255f,
  text-pwa: #09051a,
  text-cookie: #ffffff,
  text-cookie-link: #ffed30,
  text-field-focused: #ffed30,
  text-field-error: #f62c2c,
  text-gift-pack: #ffffff,
  text-gift-pack-medium: #ffed30,
  bg-text-field: #151050,
  bg-dialog: #151050,
  bg-dialog-medium: #0d1234,
  bg-btn: #ffed30,
  bg-switch: #ffed30,
  bg-menu: #0c105b,
  bg-app-awards-title-dot: #ffc700,
  bg-app-awards-title-dot-soft: rgba(255, 237, 48, 0.3),
  bg-game-square-rtp-dot-soft: rgba(255, 199, 0, 0.3),
  bg-game-square-rtp-dot: #ffc700,
  bg-card: #1e0968,
  bg-table-medium: #3d4056,
  bg-table-hover: #27235e,
  bg-table: #151050,
  tab-focused: #ffed30,
  btn-regular: #ffed30,
  btn-soft: #ffffff,
  btn-dialog-inverse: #09051a,
  btn-chat: #ffed30,
  border-regular: #fff85b,
  border-marquee: rgba(255, 255, 255, 0.5),
  bg-scrollbar: #27235e,
  scrollbar-regular: #3e35a8,
  btn-iframe-bar: #ffffff,
  bg-iframe-bar: #09051a,
  bg-slider-track: #ffed30,
  bg-slider-thumb: #ffed30,
  text-slider: #ffffff,
  text-slider-hover: #ffed30,
  bg-pagination: #6923ff,
  bg-pagination-focus: #ffed30,
  text-pagination: #ffffff,
  text-pagination-focused: #09051a,
  bg-badge: #0c105b,
  bg-badge-error: #f62c2c,
  text-badge: #ffffff,
  text-success: #05c753,
  text-member-level: #898ca3,
  bg-badge-success: #05c753,
  border-badge: #151050,
  bg-progress-determinate: #ffc700,
  text-progress: #ffffff,
  border-dialog: #ffed30,
  text-list-disable: rgba(255, 255, 255, 0.3),
  bg-progress: rgba(255, 255, 255, 0.2),
  bg-dialog-section-soft: rgba(21, 16, 80, 0.6),
  bg-badge-disable: #abaeb7,
  text-datetime-picker-medium: #ffc700,
  text-datetime-picker-btn-focused: #09051a,
  btn-datetime-picker: #fff85b,
  btn-datetime-picker-medium: #ffc700,
  btn-datetime-picker-heavy: #09051a,
  bg-datetime-picker: #151050,
  bg-datetime-picker-btn-focused: #ffc700,
  btn-datetime-picker-disable: rgba(255, 255, 255, 0.3),
  bg-datetime-picker-soft: rgba(255, 255, 255, 0.3) // replaceColor end
) !default;

$title-soft: map-get($colors, title-soft);
$text-soft: map-get($colors, text-soft);
$text-regular: map-get($colors, text-regular);
$text-medium: map-get($colors, text-medium);
$text-heavy: map-get($colors, text-heavy);
$text-list: map-get($colors, text-list);
$text-list-focused: map-get($colors, text-list-focused);
$text-dialog-header: map-get($colors, text-dialog-header);
$text-dialog-header-inverse: map-get($colors, text-dialog-header-inverse);
$text-btn: map-get($colors, text-btn);
$text-btn-heavy: map-get($colors, text-btn-heavy);
$text-marquee-medium: map-get($colors, text-marquee-medium);
$text-marquee: map-get($colors, text-marquee);
$text-app-awards: map-get($colors, text-app-awards);
$text-app-awards-soft: map-get($colors, text-app-awards-soft);
$text-app-awards-medium: map-get($colors, text-app-awards-medium);
$text-game-square-rtp: map-get($colors, text-game-square-rtp);
$text-game-promote-rtp: map-get($colors, text-game-promote-rtp);
$text-game-square-chip: map-get($colors, text-game-square-chip);
$text-pwa: map-get($colors, text-pwa);
$text-cookie: map-get($colors, text-cookie);
$text-cookie-link: map-get($colors, text-cookie-link);
$text-field-focused: map-get($colors, text-field-focused);
$text-field-error: map-get($colors, text-field-error);
$text-gift-pack: map-get($colors, text-gift-pack);
$text-gift-pack-medium: map-get($colors, text-gift-pack-medium);
$bg-text-field: map-get($colors, bg-text-field);
$bg-dialog: map-get($colors, bg-dialog);
$bg-dialog-medium: map-get($colors, bg-dialog-medium);
$bg-btn: map-get($colors, bg-btn);
$bg-switch: map-get($colors, bg-switch);
$bg-menu: map-get($colors, bg-menu);
$bg-app-awards-title-dot: map-get($colors, bg-app-awards-title-dot);
$bg-app-awards-title-dot-soft: map-get($colors, bg-app-awards-title-dot-soft);
$bg-game-square-rtp-dot-soft: map-get($colors, bg-game-square-rtp-dot-soft);
$bg-game-square-rtp-dot: map-get($colors, bg-game-square-rtp-dot);
$bg-card: map-get($colors, bg-card);
$bg-table-medium: map-get($colors, bg-table-medium);
$bg-table-hover: map-get($colors, bg-table-hover);
$bg-table: map-get($colors, bg-table);
$tab-focused: map-get($colors, tab-focused);
$btn-regular: map-get($colors, btn-regular);
$btn-soft: map-get($colors, btn-soft);
$btn-dialog-inverse: map-get($colors, btn-dialog-inverse);
$btn-chat: map-get($colors, btn-chat);
$border-regular: map-get($colors, border-regular);
$border-marquee: map-get($colors, border-marquee);
$bg-scrollbar: map-get($colors, bg-scrollbar);
$scrollbar-regular: map-get($colors, scrollbar-regular);
$btn-iframe-bar: map-get($colors, btn-iframe-bar);
$bg-iframe-bar: map-get($colors, bg-iframe-bar);
$bg-slider-track: map-get($colors, bg-slider-track);
$bg-slider-thumb: map-get($colors, bg-slider-thumb);
$text-slider: map-get($colors, text-slider);
$text-slider-hover: map-get($colors, text-slider-hover);
$bg-pagination: map-get($colors, bg-pagination);
$bg-pagination-focus: map-get($colors, bg-pagination-focus);
$text-pagination: map-get($colors, text-pagination);
$text-pagination-focused: map-get($colors, text-pagination-focused);
$bg-badge: map-get($colors, bg-badge);
$bg-badge-error: map-get($colors, bg-badge-error);
$text-badge: map-get($colors, text-badge);
$text-success: map-get($colors, text-success);
$text-member-level: map-get($colors, text-member-level);
$bg-badge-success: map-get($colors, bg-badge-success);
$border-badge: map-get($colors, border-badge);
$bg-progress-determinate: map-get($colors, bg-progress-determinate);
$text-progress: map-get($colors, text-progress);
$border-dialog: map-get($colors, border-dialog);
$text-list-disable: map-get($colors, text-list-disable);
$bg-progress: map-get($colors, bg-progress);
$bg-dialog-section-soft: map-get($colors, bg-dialog-section-soft);
$bg-badge-disable: map-get($colors, bg-badge-disable);
$text-datetime-picker-medium: map-get($colors, text-datetime-picker-medium);
$text-datetime-picker-btn-focused: map-get($colors, text-datetime-picker-btn-focused);
$btn-datetime-picker: map-get($colors, btn-datetime-picker);
$btn-datetime-picker-medium: map-get($colors, btn-datetime-picker-medium);
$btn-datetime-picker-heavy: map-get($colors, btn-datetime-picker-heavy);
$bg-datetime-picker: map-get($colors, bg-datetime-picker);
$bg-datetime-picker-btn-focused: map-get($colors, bg-datetime-picker-btn-focused);
$btn-datetime-picker-disable: map-get($colors, btn-datetime-picker-disable);
$bg-datetime-picker-soft: map-get($colors, bg-datetime-picker-soft);
