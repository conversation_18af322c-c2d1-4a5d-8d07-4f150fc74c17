$pv1-color: map-get($colors, primary-variant-1);
$pv2-color: map-get($colors, primary-variant-2);
$pv3-color: map-get($colors, primary-variant-3);
$grey-1-color: map-get($colors, grey-1);
$grey-2-color: map-get($colors, grey-2);
$grey-3-color: map-get($colors, grey-3);
$grey-4-color: map-get($colors, grey-4);
$grey-5-color: map-get($colors, grey-5);
$primary-color: map-get($colors, primary);

.gradient-primary {
  background: linear-gradient(180deg, #fff6aa 0%, #ffed30 100%);
}

.gradient-primary-reverse {
  background: linear-gradient(180deg, #ffed30 0%, #fff6aa 100%);
}

.gradient-primary-left {
  background: linear-gradient(270deg, #fff6aa 0%, #ffc700 100%);
}

.gradient-light-primary {
  background: linear-gradient(180deg, rgba(102, 191, 255, 0.8) 0%, rgba(65, 118, 250, 1) 100%);
}

.gradient-white-card {
  background: linear-gradient(180deg, $white-color 29.17%, $grey-4-color 92.71%, $grey-5-color 100%);
}

.gradient-button {
  background: linear-gradient(180deg, #fff6aa 0%, #ffed30 100%);
}

.gradient-game-maintenance {
  background: linear-gradient(180deg, rgba(144, 145, 153, 0.8) 0%, rgba(22, 24, 33, 1) 100%);
}

.gradient-primary-box {
  background: linear-gradient(270.47deg, #7b0bec -0.45%, #0d1497 99.59%);
}

.gradient-title--text {
  background: linear-gradient(180deg, #fff6aa 0%, #ffed30 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  -webkit-box-decoration-break: clone;
  box-decoration-break: clone;
}
.gradient-subtitle--text {
  background: linear-gradient(180deg, $grey-2-color 61.46%, $grey-4-color 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25);
}

.gradient-primary--text {
  background: linear-gradient(180deg, #fff6aa 0%, #ffed30 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  border-radius: 4px;
}

.gradient-primary-border {
  border: 3px solid;
  border-image: linear-gradient(180deg, rgba(255, 231, 189, 1), rgba(247, 182, 117, 1)) 1;
}

.app-bar-gradient {
  background: linear-gradient(180deg, #290a92 0%, #290a92 100%);
}
.bottom-bar-gradient {
  background: linear-gradient(180deg, #290a92 0%, #290a92 100%);
}

.app-bar-button {
  background: linear-gradient(180deg, #fff6aa 0%, #ffed30 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.border-line {
  width: 100%;
  height: 100%;
  border: solid 0.14159rem transparent;
  background-image: linear-gradient(0deg, #440106, #440106),
    linear-gradient(rgba(233, 185, 79, 1) 0%, rgba(255, 255, 255, 1) 50%, rgba(233, 185, 79, 1) 100%);
  background-origin: border-box;
  background-clip: content-box, border-box;
}

.card-gradient-1 {
  background: linear-gradient(180deg, #ac2335 29.17%, #550d10 92.71%, #a0574c 100%);
}

.card-gradient-2 {
  background: linear-gradient(180deg, #240d77 0%, #13143c 100%);
}

.background {
  background: linear-gradient(180deg, #290a92 0%, #270a62 51.04%, #8f2094 100%);
}

.info-card {
  background: linear-gradient(180deg, #202FAD 0%, #530782 100%);
}

.vip-title {
  background: linear-gradient(180deg, #fec600 0%, #dc1646 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-game-maintenance-2 {
  background: linear-gradient(180deg, rgba(153, 144, 144, 0) 0%, #161821 100%);
}

.gradient-side-bar-game {
  background: linear-gradient(99deg, #3f4dc6 -2.56%, #5c40cb 37.71%, #762bc1 109.3%);
}

.game-intro-grade-bg {
  background: linear-gradient(180deg, #4530c3 0%, rgba(115, 81, 197, 0.4) 50%, rgba(114, 67, 136, 0) 100%) !important;
}

.game-intro-grade-border {
  background: linear-gradient(270deg, #37296d 0%, #443892 28%, rgba(73, 56, 143, 0.2) 76%, rgba(28, 26, 121, 0.2) 100%);
  &::before {
    border: 2px solid;
    border-image: linear-gradient(
        to right bottom,
        #dad6e4 -0.44%,
        #574e8b 49.78%,
        rgba(90, 78, 116, 0.2) 74.89%,
        #c4bdd8 87.45%,
        rgba(90, 78, 116, 0.2) 100%
      )
      1;
  }
}

.mail-attachment-gradient-bg {
  background: linear-gradient(180deg, #6573F2 0%, rgba(101, 115, 242, 0.40) 50%, rgba(101, 115, 242, 0.00) 100%);
}

.gift-pack-reward-card-fill {
  background: linear-gradient(270deg, rgba(241, 142, 28, 0) 0%, rgba(241, 142, 28, 0.5) 12.86%, rgb(241, 142, 28) 50%, rgba(241, 142, 28, 0.50) 85%, rgba(241, 142, 28, 0) 100%);
}

.gift-pack-frame {
  background: linear-gradient(270deg, rgba(173, 196, 255, 0) 0%, rgba(173, 196, 255, 0.8) 51%, rgba(173, 196, 255, 0) 100%);
}
.gift-pack-frame-vip {
  background: linear-gradient(270deg, rgba(130, 134, 211, 0) 0%, rgba(130, 134, 211, 0.8) 51%, rgba(130, 134, 211, 0) 100%);
}